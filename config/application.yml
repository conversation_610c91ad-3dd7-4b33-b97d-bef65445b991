spring:
  jackson:
    time-zone: GMT+8
    date-format: yyyy-MM-dd HH:mm:ss
  servlet:
    multipart:
      max-file-size: 100MB
      max-request-size: 100MB
      enabled: true
  mvc:
    throw-exception-if-no-handler-found: true
    pathmatch:
      matching-strategy: ant_path_matcher
  autoconfigure:
    #自动化配置 例外处理
    exclude: com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    dynamic:
      druid: # Druid 【连接池】相关的全局配置
        initial-size: 1 # 初始连接数
        min-idle: 10 # 最小连接池数量
        max-active: 500 # 最大连接池数量
        max-wait: 6000 # 配置获取连接等待超时的时间，单位：毫秒
        time-between-eviction-runs-millis: 6000 # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位：毫秒
        min-evictable-idle-time-millis: 30000 # 配置一个连接在池中最小生存的时间，单位：毫秒
        max-evictable-idle-time-millis: 90000 # 配置一个连接在池中最大生存的时间，单位：毫秒
        validation-query: SELECT 1 FROM DUAL # 配置检测连接是否有效
        test-while-idle: true
        test-on-borrow: true
        test-on-return:
      primary: master
      datasource:
        master:
          driver-class-name: oracle.jdbc.OracleDriver
          url: jdbc:oracle:thin:@${config.db.ip}:1521/orcl
          username: ${config.db.username}
          password: ${config.db.password}
        im:
          driver-class-name: com.mysql.cj.jdbc.Driver
          url: jdbc:mysql://${config.db.im.ip}:${config.db.im.port}/box-im?useUnicode=true&characterEncoding=UTF-8&serverTimezone=Asia/Shanghai&nullCatalogMeansCurrent=true
          username: ${config.db.im.username}
          password: ${config.db.im.password}



  #          driver-class-name: dm.jdbc.driver.DmDriver
  #          url: jdbc:dm://*************:5236/TZXTEST&compatibleMode=oracle&characterEncoding=UTF-8&useUnicode=true&useSSL=false&tinyInt1isBit=false&allowPublicKeyRetrieval=true&serverTimezone=Asia/Shanghai
  #          username: TZXTEST
  #          password: Jiushi5xixi

  #          driver-class-name: com.kingbase8.Driver
  #          url: ************************************************************
  #          username: system
  #          password: 123456

  #          driver-class-name: com.ibm.db2.jcc.DB2Driver
  #          url: jdbc:db2://*************:50000/sample
  #          username: db2inst1
  #          password: db.2.admin




  redis:
    database: 9
    host: ${config.redis.ip}
    port: ${config.redis.port}
    password:
    timeout: 60000ms  # 连接超时时长（毫秒）
    jedis:
      pool:
        max-active: 1000  # 连接池最大连接数（使用负值表示没有限制）
        max-wait: -1ms      # 连接池最大阻塞等待时间（使用负值表示没有限制）
        max-idle: 10      # 连接池中的最大空闲连接
        min-idle: 5       # 连接池中的最小空闲连接

logging:
  level:
    org.camunda: debug #打印camunda 日志  一般用于 查看camunda  执行sql
  config: file:./config/logback.xml



xjrsoft:
  oss:
    enabled: true
    cloud-type: minio
    access-key: minioadmin
    secret-key: minioadmin
    endpoint: http://${config.oss.ip}:${config.oss.port}
    bucket-name: xjrsoft
  job:
    enabled: true                                         # 执行器通讯TOKEN [必填]：是否启用定时任务功能；
    accessToken:                                          # 执行器通讯TOKEN [选填]：非空时启用；
    admin:
      addresses: http://***********:18080/xxl-job-admin   # 调度中心部署跟地址 [选填]：如调度中心集群部署存在多个地址则用逗号分隔。执行器将会使用该地址进行"执行器心跳注册"和"任务结果回调"；为空则关闭自动注册；
    executor:
      address:                                            # 执行器注册 [选填]：优先使用该配置作为注册地址，为空时使用内嵌服务 ”IP:PORT“ 作为注册地址。从而更灵活的支持容器类型执行器动态IP和动态映射端口问题。
      appname: xjrsoft                                    # 执行器AppName [选填]：执行器心跳注册分组依据；为空则关闭自动注册
      ip:                                                 # 执行器IP [选填]：默认为空表示自动获取IP，多网卡时可手动设置指定IP，该IP不会绑定Host仅作为通讯实用；地址信息用于 "执行器注册" 和 "调度中心请求并触发任务"；
      port: 0                                             # 执行器端口号 [选填]：小于等于0则自动获取；默认端口为9999，单机部署多个执行器时，注意要配置不同执行器端口；
      log-path: /data/logs/xxl-job/job-logs               # 执行器运行日志文件存储磁盘路径 [选填] ：需要对该路径拥有读写权限；为空则使用默认路径；
      log-retention-days: 30                              # 执行器日志文件保存天数 [选填] ： 过期日志自动清理, 限制值大于等于3时生效; 否则, 如-1, 关闭自动清理功能；
  generate:
    webPath: C:\dyz\5code\ulp\xjrsoft-vue3 #生成代码的路径
    appPath: C:\dyz\5code\ulp\xjrsoft-uni   #前端app
  common:
    error-type: SYSTEM
    druid-account: admin # druid 监控账户
    druid-password: admin # druid 监控密码
    default-password: "1" #默认密码（用户重置密码后为该密码）
    white-list:
      - 192.168.0.139
    exclude-urls:
      - /favicon.ico  #网站图标
      - /webjars/**   #swagger(knife4j)  接口文档必要资源
      - /swagger-resources/** #swagger(knife4j) 接口文档必要资源
      - /v2/api-docs           #swagger(knife4j)  接口文档必要资源
      - /doc.html              #swagger(knife4j) 接口文档必要资源
      - /druid/**         #druid 监控
      - /ureport/**
      - /system/captcha    # 验证码
      - /system/login      # 登录接口
      - /system/logout     # 登出接口
      - /camunda/**        # camunda工作台
      - /language/**        # language
      - /${magic-api.web}/**    # magic-api web
      - /${magic-api.prefix}/** # magic-api 前缀
      - /bi/project/info # 桌面
      - /system/qrcode-login # 扫码登录
      - /oauth/callback/** #回调
      - /system/logoConfig/logo-info #登录之后加载图片的接口
      - /system/dictionary-item/initDictItem
      - /v2/api-docs/**
      - /XJRSOFT.ZY_FORM_SCHEMEINFO/export
      - /system/getCertVerifyCode
      - /sysTemplate/export
      - /sysTemplate/yl
      - /system/dictionary-item
      - /system/dictionary-item/**
      - /system/file
      - /system/file/**
      - /system/dictionary-detail
      - /system/dictionary-detail/**
      - /system/https
      - /webSocket/**
      - /print/**
  email:
    host:  #邮件服务器的SMTP地址，可选，默认为smtp.<发件人邮箱后缀>
    port:  # 邮件服务器的SMTP端口，可选，默认25
    auth: true
    from:  # 发件人（必须正确，否则发送失败
    user: # 用户名，默认为发件人邮箱前缀
    pass:       # 密码（注意，某些邮箱需要为SMTP服务单独设置授权码，详情查看相关帮助
  wechatenterprise:
    appkey: ww00ce9acc4f19f18
    appSecret: 5bYCMH3ULPHzrJviIFWCIdX0OTyQoFqfnel_noyrwo
    agentid: 1000005
    redirectUri: http://www.tzx.test.com:8080/oauth/callback/wechat_enterprise
    frontUrl: http://localhost:3100/#/login
  dingtalk:
    appkey: dingaex2gok1rllmlqs
    appSecret: Nv2pe-UoR0Z_Iw829laZfL3kH6ElmhZpRdPwI7SvmKJmaCI29qCrSlFEiMM88MB
    agentid: *********
    redirectUri: http://www.tzx.test.com:8080/oauth/callback/dingtalk
    frontUrl: http://localhost:3100/#/login
  ureport:
    account: xjrsoft #ureport 账号
    password: 123456 #ureport 密码
  keycloak:
    url: http://*************:12829/auth/
    realm: test
    client-id: tzx-java
    secret: yo5kPZlHgmodz4vVASH61zq8i6gMhkk2
    user-name: tzx  #如果不需要后端再次 可以不需要
    password: "000000" #可以不需要
    payload: code #从payload 某个key中取值 登录
  chatgpt:
    proxy-type: 0 # 不开启代理 == -1 http == 0   socket == 1
    proxy: *************
    port: 1081
    api-key: ***************************************************   #your api-key. It can be generated in the link https://beta.openai.com/docs/quickstart/adjust-your-settings
    # some properties as below have default values. Of course, you can change them.
    max-tokens: 4000           # The maximum number of tokens to generate in the completion.The token count of your prompt plus max_tokens cannot exceed the model's context length. Most models have a context length of 2048 tokens (except for the newest models, which support 4096).
  #  model: text-davinci-003   # GPT-3 models can understand and generate natural language. We offer four main models with different levels of power suitable for different tasks. Davinci is the most capable model, and Ada is the fastest.
  #  temperature: 0.0          # What sampling temperature to use. Higher values means the model will take more risks. Try 0.9 for more creative applications, and 0 (argmax sampling) for ones with a well-defined answer.We generally recommend altering this or top_p but not both.
  #  top-p: 1.0                # An alternative to sampling with temperature, called nucleus sampling, where the model considers the results of the tokens with top_p probability mass. So 0.1 means only the tokens comprising the top 10% probability mass are considered.We generally recommend altering this or temperature but not both.
  sms:
    limit-time: 24 # 短信限制时间（单位：小时，正整数）
    limit-count: 10 # 短信限制次数，与limit-time一起使用，限制时长内允许发送的次数
    platform: HW_CLOUD #默认使用短信服务商
    captcha-sender: 882305308920 #验证码通道号
    captcha-template-id: 97c6fcf9d0149b39c0ed0e53480116f # 验证码 模板id
    notify-sender: 88230531143 # 提示类提示短信 通道号
    notify-template-id: d424df2a9244ac9d2f2685cd158635 #提示类短信 模板id
    circulated-sender: 88230511843 # 提示类传阅短信 通道号
    circulated-template-id: 5c17907a72447c79568ac22371be803 #提示类传阅短信 模板id
    timeout-sender: ********** # 提示类短超时信 通道号
    timeout-template-id: 5c1790702447c79568ac22371be803 #提示类超时短信 模板id
  license:
    enabled: false                  #是否需要验证
    login-max: 100                  #最大登陆人数
    start-time: 2023-12-14 00:00:00 #开始时间
    end-time: 2023-12-14 17:35:00   #结束时间
    company-name: xxx               #公司名
    contact-number: xxx             #联系人

sms:
  huawei:
    #华为短信appKey
    appKey: 69s6YyLsrLgWW99J5K1K26WiF29
    #华为短信appSecret
    app-secret: RuE1YajkI6FGh2WjVtOpychRGL
    #短信签名
    signature: 湘北智造
    #通道号
    sender: 8823053028920
    #模板ID 如果使用自定义模板发送方法可不设定
    template-id: 97c6fcf9fd149b39c0ed0e53480116f
    #华为回调地址，如不需要可不设置或为空
    statusCallBack:
    #华为分配的app请求地址
    url: https://smsapi.cn-north-4.myhuaweicloud.com:443

powerjob:
  worker:
    enabled: false
    app-name: xjrsoft-boot
    server-address: *************:7700
    protocol: http
    store-strategy: disk
    max-result-length: 4096
    max-appended-wf-context-length: 4096
    max-lightweight-task-num: 1024



# 验证码配置
#smst:
#  sp: huawei # 短信服务商，huawei(华为),ali(阿里巴巴),tencent(腾讯)
#  url: https://smsapi.cn-north-4.myhuaweicloud.com:443/sms/batchSendSms/v1 # APP接入地址+接口访问URI
#  app_key: 69s6YyLsrLgWW99BJ5K1K26WiF29
#  secret_id: RuE1YajkI6FC4Gh2WjVtOpychRGL # API秘钥
#  secret_key: RuE1YajkI6FC4Gh2WjVtOpychRGL # 目前只有腾讯用到
#  sender: 8823053028920   # 验证码类发送人手机号通道号
#  message_sender: 8823053111843 # 通知类发送人手机号通道号
#  signature: 湘北智造    # 签名名称
#  region: default # 地区名，阿里和腾讯需要配置，默认default
#  templates:
#    - type: login # 手机登录验证 模板ID  keys是短信模板内容中的参数名，只有阿里的短信模板需要key去填充，其他两个则是传入的数组参数顺序填充
#      id: 97c6fcf9fd0149b39c0ed0e53480116f
#      keys: code
#    - type: reset # 忘记密码-验证手机号码 模板ID
#      id: 614a2cd08b704cf393321e1df054e3df
#      keys: code
#    - type: message # 消息管理发送短信通道码
#      id: d424de0f2a9244ac9d2f2685cd158635
#      keys: code

magic-api:
  #配置web页面入口
  web: /magic/web
  resource: #配置存储方式
    type: database # 配置存储在数据库中
    tableName: magic_api_file # 数据库中的表名
    #    datasource: master #指定数据源（单数据源时无需配置，多数据源时默认使用主数据源，如果存在其他数据源中需要指定。）
    prefix: /magic-api # key前缀
    readonly: false # 是否是只读模式
  #  security: # 安全配置
  #    username: admin # 登录用的用户名
  #    password: 123456 # 登录用的密码
  prefix: /magic-api
  editor-config: classpath:./magic-editor-config.js #编辑器配置
  date-pattern: # 配置请求参数支持的日期格式
    - yyyy-MM-dd
    - yyyy-MM-dd HH:mm:ss
    - yyyyMMddHHmmss
    - yyyyMMdd
  response: |- #配置JSON格式，格式为magic-script中的表达式
    {
      code: code,
      msg: message,
      data,
      timestamp,
      requestTime,
      executeTime,
    }
  response-code:
    success: 0 #执行成功的code值
    invalid: 10400 #参数验证未通过的code值
    exception: 10500 #执行出现异常的code值
  backup: #备份相关配置
    enable: false #是否启用
    max-history: -1 #备份保留天数，-1为永久保留
    table-name: magic_api_backup #使用数据库存储备份时的表名
  crud: # CRUD相关配置
    logic-delete-column: delete_mark #逻辑删除列
    logic-delete-value: 1 #逻辑删除值
  page:
    size: size # 页大小的参数名称
    page: limit # 页码的参数名称
    default-page: 1 # 未传页码时的默认首页
    default-size: 10 # 未传页大小时的默认页大小
  debug:
    timeout: 60 # 断点超时时间，默认60s
  throw-exception: true # 执行出错时，异常将抛出处理
  auto-import-module: db,http,log,request,response,env,magic #自动导入模块
  auto-import-package: java.lang.*,java.util.* #自动导包
  secret-key: 123456789
  push-path: /magic-api #远程推送的路径，默认为/_magic-api-sync
  show-sql: true #配置打印SQL
  swagger:
    version: 1.0
    description: MagicAPI 接口信息
    title: MagicAPI Swagger Docs
    name: MagicAPI 接口
    location: /v2/api-docs/magic-api/swagger2.json





# Sa-Token配置
sa-token:
  # sa-token-temp-jwt 模块的秘钥 （随便乱摁几个字母就行了）
  jwt-secret-key: xxxxxxxxx
  # token前缀
  token-prefix: Bearer
  # token名称 (同时也是cookie名称)
  token-name: hzhltoken
  # token有效期，单位s 默认30天, -1代表永不过期
  timeout: 2592000
  # token临时有效期 (指定时间内无操作就视为token过期) 单位: 秒
  activity-timeout: -1
  # 是否允许同一账号并发登录 (为true时允许一起登录, 为false时新登录挤掉旧登录)
  is-concurrent: true
  # 在多人登录同一账号时，是否共用一个token (为true时所有登录共用一个token, 为false时每次登录新建一个token)
  is-share: false
  # token风格
  token-style: uuid
  # 是否输出操作日志
  is-log: false

mybatis-plus-join:
  #是否打印 mybatis plus join banner 默认true
  banner: true
  #全局启用副表逻辑删除(默认true) 关闭后关联查询不会加副表逻辑删除
  sub-table-logic: true
  #拦截器MappedStatement缓存(默认true)
  ms-cache: true
  #表别名(默认 t)
  table-alias: t



#mybatis
mybatis-plus:
  mapper-locations: classpath*:/mapper/**/*.xml
  #实体扫描，多个package用逗号或者分号分隔
  typeAliasesPackage: com.xjrsoft.modules.*.entity
  global-config:
    #数据库相关配置
    db-config:
      #主键类型  AUTO:"数据库ID自增", INPUT:"用户输入ID", ID_WORKER:"全局唯一ID (数字类型唯一ID)", UUID:"全局唯一ID UUID";
      id-type: ASSIGN_ID
    banner: false
  #原生配置
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false
    call-setters-on-nulls: true
    jdbc-type-for-null: 'null'


camunda:
  bpm:
    admin-user:
      id: admin
      password: admin
      #        first-name: admin
      #      filter:
      #        create: All tasks
      #        #指定数据库类型
      #        database:
      #          type: mysql
      #自动部署resources下面的bpmn文件
      #    auto-deployment-enabled: true
      #禁止index跳转到Camunda自带的管理界面，默认true
    #    webapp:
    #      index-redirect-enabled: false
    database:
      jdbc-batch-processing: false #oracle使用camunda工作流报错关闭批处理
#      type: postgres  #  kingbase 需要根据模式来填写type  是 oracle  还是 postgres

#easyTrans字典转换配置
easy-trans:
  #启用redis缓存 如果不用redis请设置为false
  is-enable-redis: false
  #启用全局翻译(拦截所有responseBody进行自动翻译)，如果对于性能要求很高可关闭此配置
  is-enable-global: true
  #启用平铺模式
  is-enable-tile: true
bean-searcher:
  params:
    sort: bsort
    order: border
    pagination:
      # 起始页，不配置默认为0，这里配置为1，是为了兼容element UI的分页组件
      start: 1
      default-size: 1000
      max-allowed-size: 1000000
  sql:
    dialect: Oracle
  field-convertor:
    zone-id: Asia/Shanghai
liteflow:
  #规则文件路径
  rule-source: config/**/*.el.xml
  #-----------------以下非必须-----------------
  #liteflow是否开启，默认为true
  enable: true
  #liteflow的banner打印是否开启，默认为true
  print-banner: true
  #zkNode的节点，只有使用zk作为配置源的时候才起作用，默认为/lite-flow/flow
  zk-node: /lite-flow/flow
  #上下文的最大数量槽，默认值为1024
  slot-size: 1024
  #FlowExecutor的execute2Future的线程数，默认为64
  main-executor-works: 64
  #FlowExecutor的execute2Future的自定义线程池Builder，LiteFlow提供了默认的Builder
  main-executor-class: com.yomahub.liteflow.thread.LiteFlowDefaultMainExecutorBuilder
  #自定义请求ID的生成类，LiteFlow提供了默认的生成类
  request-id-generator-class: com.yomahub.liteflow.flow.id.DefaultRequestIdGenerator
  #并行节点的线程池Builder，LiteFlow提供了默认的Builder
  thread-executor-class: com.yomahub.liteflow.thread.LiteFlowDefaultWhenExecutorBuilder
  #异步线程最长的等待时间(只用于when)，默认值为15000
  when-max-wait-time: 15000
  #异步线程最长的等待时间(只用于when)，默认值为MILLISECONDS，毫秒
  when-max-wait-time-unit: MILLISECONDS
  #when节点全局异步线程池最大线程数，默认为16
  when-max-workers: 16
  #并行循环子项线程池最大线程数，默认为16
  parallel-max-workers: 16
  #并行循环子项线程池等待队列数，默认为512
  parallel-queue-limit: 512
  #并行循环子项的线程池Builder，LiteFlow提供了默认的Builder
  parallel-loop-executor-class: com.yomahub.liteflow.thread.LiteFlowDefaultParallelLoopExecutorBuilder
  #when节点全局异步线程池等待队列数，默认为512
  when-queue-limit: 512
  #设置解析模式，一共有三种模式，PARSE_ALL_ON_START | PARSE_ALL_ON_FIRST_EXEC | PARSE_ONE_ON_FIRST_EXEC
  parse-mode: PARSE_ALL_ON_START
  #全局重试次数，默认为0
  retry-count: 0
  #是否支持不同类型的加载方式混用，默认为false
  support-multiple-type: false
  #全局默认节点执行器
  node-executor-class: com.yomahub.liteflow.flow.executor.DefaultNodeExecutor
  #是否打印执行中过程中的日志，默认为true
  print-execution-log: true
  #是否开启本地文件监听，默认为false
  enable-monitor-file: false
  #是否开启快速解析模式，默认为false
  fast-load: false
  #简易监控配置选项
  monitor:
    #监控是否开启，默认不开启
    enable-log: false
    #监控队列存储大小，默认值为200
    queue-limit: 200
    #监控一开始延迟多少执行，默认值为300000毫秒，也就是5分钟
    delay: 300000
    #监控日志打印每过多少时间执行一次，默认值为300000毫秒，也就是5分钟
    period: 300000
---
print:
  ip: *************
  port: 9200


