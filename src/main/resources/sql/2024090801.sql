--liquibase formatted sql
--changeset dyz:2024090801 failOnError:false
-- Create table
create table SYS_BILL_PRINT_DS
(
    page_id        VARCHAR2(64) not null,
    id             VARCHAR2(64) not null,
    name           VA<PERSON>HAR2(255),
    primary_table  VARCHAR2(40),
    is_use         CHAR(1),
    create_by      VARCHAR2(64),
    create_date    DATE,
    update_by      VA<PERSON>HAR2(64) not null,
    update_date    DATE not null,
    remarks        NVARCHAR2(255),
    del_flag       CHAR(1) default '0',
    alias_name     VARCHAR2(50),
    primary_field  VARCHAR2(50),
    f_sql          VARCHAR2(2000),
    is_use_comment CHAR(1),
    table_sql      CLOB
);
-- Add comments to the table
comment on table SYS_BILL_PRINT_DS
    is ' 单据打印数据源表1';
-- Add comments to the columns
comment on column SYS_BILL_PRINT_DS.page_id
    is 'PAGEID唯一标志';
comment on column SYS_BILL_PRINT_DS.id
    is '编号';
comment on column SYS_BILL_PRINT_DS.name
    is ' 数据源名称';
comment on column SYS_BILL_PRINT_DS.primary_table
    is '主表';
comment on column SYS_BILL_PRINT_DS.is_use
    is '是否使用中(使用中的删除进行提示)  1 使用中  0 未使用';
comment on column SYS_BILL_PRINT_DS.alias_name
    is '数据表别名';
comment on column SYS_BILL_PRINT_DS.primary_field
    is '数据主表主键ID';
comment on column SYS_BILL_PRINT_DS.f_sql
    is '过滤sql';
comment on column SYS_BILL_PRINT_DS.is_use_comment
    is '数据主表使用审核意见';
comment on column SYS_BILL_PRINT_DS.table_sql
    is '主表替换sql';
-- Create/Recreate indexes
create index SYS_BILL_PRINT_DS_PID on SYS_BILL_PRINT_DS (PAGE_ID)
    tablespace XJRSOFT
    pctfree 10
    initrans 2
    maxtrans 255
    storage
    (
    initial 64K
    next 1M
    minextents 1
    maxextents unlimited
    );
-- Create/Recreate primary, unique and foreign key constraints
alter table SYS_BILL_PRINT_DS
    add constraint SYS_BILL_PRINT_DS_PK primary key (ID)
        using index
            tablespace XJRSOFT
            pctfree 10
            initrans 2
            maxtrans 255
            storage
            (
            initial 64K
            next 1M
            minextents 1
            maxextents unlimited
            );
-- Create table
create table SYS_BILL_PRINT_DS_DETAIL
(
    id            VARCHAR2(64) not null,
    bill_print_id VARCHAR2(64),
    primary_field VARCHAR2(50),
    foreign_table VARCHAR2(50),
    foreign_field VARCHAR2(50),
    create_by     VARCHAR2(64),
    create_date   DATE,
    update_by     VARCHAR2(64) not null,
    update_date   DATE not null,
    remarks       NVARCHAR2(255),
    del_flag      CHAR(1) default '0',
    alias_name    VARCHAR2(50),
    record_size   NUMBER(10) default 0,
    f_sql         VARCHAR2(2000),
    table_sql     CLOB
)
    tablespace XJRSOFT
    pctfree 10
    initrans 1
    maxtrans 255
    storage
(
    initial 64K
    next 1M
    minextents 1
    maxextents unlimited
);
-- Add comments to the table
comment on table SYS_BILL_PRINT_DS_DETAIL
    is ' 单据打印数据源从表';
-- Add comments to the columns
comment on column SYS_BILL_PRINT_DS_DETAIL.id
    is '主键ID';
comment on column SYS_BILL_PRINT_DS_DETAIL.bill_print_id
    is '单据打印ID';
comment on column SYS_BILL_PRINT_DS_DETAIL.primary_field
    is '关联主表字段';
comment on column SYS_BILL_PRINT_DS_DETAIL.foreign_table
    is '从表表名/视图名';
comment on column SYS_BILL_PRINT_DS_DETAIL.foreign_field
    is '从表关联字段';
comment on column SYS_BILL_PRINT_DS_DETAIL.alias_name
    is '数据表别名';
comment on column SYS_BILL_PRINT_DS_DETAIL.record_size
    is '每页记录数';
comment on column SYS_BILL_PRINT_DS_DETAIL.f_sql
    is '过滤sql';
comment on column SYS_BILL_PRINT_DS_DETAIL.table_sql
    is '从表sql';
-- Create/Recreate indexes
create index SYS_BPDS_DETAIL_INDEX_BPI on SYS_BILL_PRINT_DS_DETAIL (BILL_PRINT_ID)
    tablespace XJRSOFT
    pctfree 10
    initrans 2
    maxtrans 255
    storage
    (
    initial 64K
    next 1M
    minextents 1
    maxextents unlimited
    );
-- Create/Recreate primary, unique and foreign key constraints
alter table SYS_BILL_PRINT_DS_DETAIL
    add constraint SYS_BILL_PRINT_DS_DETAIL_PK primary key (ID)
        using index
            tablespace XJRSOFT
            pctfree 10
            initrans 2
            maxtrans 255
            storage
            (
            initial 64K
            next 1M
            minextents 1
            maxextents unlimited
            );
-- Create table
create table SYS_BILL_PRINT_DS_RELATION
(
    id                       VARCHAR2(64) not null,
    bill_print_id            VARCHAR2(64) not null,
    bill_print_ds_primary_id VARCHAR2(64) not null,
    primary_field            VARCHAR2(50),
    bill_print_ds_foreign_id VARCHAR2(64) not null,
    foreign_field            VARCHAR2(50),
    create_by                VARCHAR2(64),
    create_date              DATE,
    update_by                VARCHAR2(64) not null,
    update_date              DATE not null,
    remarks                  NVARCHAR2(255),
    del_flag                 CHAR(1) default '0'
)
    tablespace XJRSOFT
    pctfree 10
    initrans 1
    maxtrans 255;
-- Add comments to the table
comment on table SYS_BILL_PRINT_DS_RELATION
    is ' 单据打印数据源从表关系';
-- Add comments to the columns
comment on column SYS_BILL_PRINT_DS_RELATION.id
    is '主键ID';
comment on column SYS_BILL_PRINT_DS_RELATION.bill_print_id
    is '单据打印ID';
comment on column SYS_BILL_PRINT_DS_RELATION.bill_print_ds_primary_id
    is '单据从表1 ID  SYS_BILL_PRINT_DS_DETAIL 的ID';
comment on column SYS_BILL_PRINT_DS_RELATION.primary_field
    is '关联从表1 字段';
comment on column SYS_BILL_PRINT_DS_RELATION.bill_print_ds_foreign_id
    is '单据从表2 ID SYS_BILL_PRINT_DS_DETAIL 的ID';
comment on column SYS_BILL_PRINT_DS_RELATION.foreign_field
    is '关联从表2 字段';
-- Create/Recreate indexes
create index SYS_BPDSR_DETAIL_INDEX_BPDSFI on SYS_BILL_PRINT_DS_RELATION (BILL_PRINT_DS_FOREIGN_ID)
    tablespace XJRSOFT
    pctfree 10
    initrans 2
    maxtrans 255;
create index SYS_BPDSR_DETAIL_INDEX_BPDSPI on SYS_BILL_PRINT_DS_RELATION (BILL_PRINT_DS_PRIMARY_ID)
    tablespace XJRSOFT
    pctfree 10
    initrans 2
    maxtrans 255;
create index SYS_BPDSR_DETAIL_INDEX_BPI on SYS_BILL_PRINT_DS_RELATION (BILL_PRINT_ID)
    tablespace XJRSOFT
    pctfree 10
    initrans 2
    maxtrans 255;
-- Create/Recreate primary, unique and foreign key constraints
alter table SYS_BILL_PRINT_DS_RELATION
    add constraint SYS_BILL_PRINT_DS_RELATION_PK primary key (ID)
        using index
            tablespace XJRSOFT
            pctfree 10
            initrans 2
            maxtrans 255;

