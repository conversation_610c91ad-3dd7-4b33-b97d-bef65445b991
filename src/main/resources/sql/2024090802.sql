--liquibase formatted sql
--changeset dyz:2024090802 failOnError:false
-- Create table
-- Create table
create table SYS_TEMPLATE
(
    id          VARCHAR2(64) not null,
    name        <PERSON><PERSON><PERSON>R2(200),
    content     CLOB,
    name_alias  <PERSON><PERSON>HAR2(255),
    create_by   <PERSON><PERSON><PERSON>R2(64),
    create_date DATE,
    update_by   VA<PERSON><PERSON>R2(64),
    update_date DATE,
    remarks     NVA<PERSON>HAR2(255),
    del_flag    CHAR(1) default '0',
    module      VARCHAR2(10),
    report_id   VARCHAR2(64)
);
-- Add comments to the columns
comment on column SYS_TEMPLATE.module
    is '自定义参数排序字段';
comment on column SYS_TEMPLATE.report_id
    is '报表ID';
-- Create/Recreate primary, unique and foreign key constraints
alter table SYS_TEMPLATE
    add primary key (ID)
        using index
            tablespace XJRSOFT
            pctfree 10
            initrans 2
            maxtrans 255
            storage
            (
            initial 64K
            next 1M
            minextents 1
            maxextents unlimited
            );
