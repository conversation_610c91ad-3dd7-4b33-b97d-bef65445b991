--liquibase formatted sql
--changeset dyz:2024092601 failOnError:false
-- Create table
drop table XJR_OA_NEWS;
create table XJR_OA_NEWS
(
    id              NUMBER(21) not null,
    type_id         NUMBER(11),
    category_id     NVARCHAR2(255),
    category        NVARCHAR2(255),
    full_head       NVARCHAR2(255),
    full_head_color NVARCHAR2(50),
    brief_head      NVARCHAR2(255),
    author_name     NVARCHAR2(50),
    compile_name    NVARCHAR2(50),
    tag_word        NVARCHAR2(50),
    keyword         NVARCHAR2(50),
    source_name     NVARCHAR2(50),
    source_address  NVARCHAR2(255),
    news_content    NCLOB,
    pv              NUMBER(11),
    release_time    TIMESTAMP(6),
    sort_code       NUMBER(11),
    remark          NVARCHAR2(255),
    tenant_id       NUMBER(21),
    create_date     TIMESTAMP(6),
    create_user_id  VARCHAR2(64),
    modify_date     TIMESTAMP(6),
    modify_user_id  VARCHAR2(64),
    delete_mark     NUMBER(11),
    enabled_mark    NUMBER(11)
);
-- Add comments to the table
comment on table XJR_OA_NE<PERSON>
    is '新闻';
-- Add comments to the columns
comment on column XJR_OA_NEWS.id
    is '主键';
comment on column XJR_OA_NEWS.type_id
    is '类型（1-新闻2-公告）';
comment on column XJR_OA_NEWS.category_id
    is '所属类别主键';
comment on column XJR_OA_NEWS.category
    is '所属类别';
comment on column XJR_OA_NEWS.full_head
    is '完整标题';
comment on column XJR_OA_NEWS.full_head_color
    is '完整标题颜色';
comment on column XJR_OA_NEWS.brief_head
    is '简略标题';
comment on column XJR_OA_NEWS.author_name
    is '作者';
comment on column XJR_OA_NEWS.compile_name
    is '编辑';
comment on column XJR_OA_NEWS.tag_word
    is 'tag  关键词';
comment on column XJR_OA_NEWS.keyword
    is ' 关键词';
comment on column XJR_OA_NEWS.source_name
    is ' 来源';
comment on column XJR_OA_NEWS.source_address
    is ' 来源地址';
comment on column XJR_OA_NEWS.news_content
    is '新闻内容';
comment on column XJR_OA_NEWS.pv
    is '点击量';
comment on column XJR_OA_NEWS.release_time
    is '发布时间';
comment on column XJR_OA_NEWS.sort_code
    is '排序';
comment on column XJR_OA_NEWS.remark
    is '备注';
comment on column XJR_OA_NEWS.tenant_id
    is '租户id';
comment on column XJR_OA_NEWS.create_date
    is '创建时间';
comment on column XJR_OA_NEWS.create_user_id
    is '创建人id';
comment on column XJR_OA_NEWS.modify_date
    is '修改时间';
comment on column XJR_OA_NEWS.modify_user_id
    is '修改人id';
comment on column XJR_OA_NEWS.delete_mark
    is '删除标记';
comment on column XJR_OA_NEWS.enabled_mark
    is '启用标记';
-- Create/Recreate primary, unique and foreign key constraints
alter table XJR_OA_NEWS
    add constraint OA_NEWS_pk1 primary key (ID)
        using index;
