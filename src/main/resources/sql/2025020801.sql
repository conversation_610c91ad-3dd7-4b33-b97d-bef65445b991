--liquibase formatted sql
--changeset dyz:2025020801 failOnError:false
-- Add/modify columns
-- Create table
-- Create table
create table XJR_DCZX
(
    id             VARCHAR2(32) not null,
    sort_code      NUMBER(11),
    remark         NVARCHAR2(255),
    create_date    TIMESTAMP(6),
    create_user_id VARCHAR2(64),
    modify_date    TIMESTAMP(6),
    modify_user_id VARCHAR2(64),
    delete_mark    NUMBER(11),
    enabled_mark   NUMBER(11),
    file_path      varchar2(250),
    dczt           varchar2(10),
    file_name      varchar2(200)
)
;
-- Add comments to the table
comment on table XJR_DCZX
    is '导出中心';
-- Add comments to the columns
comment on column XJR_DCZX.sort_code
    is '排序';
comment on column XJR_DCZX.remark
    is '备注';
comment on column XJR_DCZX.create_date
    is '创建时间';
comment on column XJR_DCZX.create_user_id
    is '创建人id';
comment on column XJR_DCZX.modify_date
    is '修改时间';
comment on column XJR_DCZX.modify_user_id
    is '修改人id';
comment on column XJR_DCZX.delete_mark
    is '删除标记';
comment on column XJR_DCZX.enabled_mark
    is '启用标记';
comment on column XJR_DCZX.file_path
    is '文件下载路径';
comment on column XJR_DCZX.dczt
    is '导出状态0未开始1进行中2已完成3已下载4导出失败';
comment on column XJR_DCZX.file_name
    is '文件名字';
-- Create/Recreate primary, unique and foreign key constraints
alter table XJR_DCZX
    add constraint XJR_DCZX_ID primary key (ID);
