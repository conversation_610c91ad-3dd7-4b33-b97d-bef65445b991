--liquibase formatted sql
--changeset dyz:2024090101 failOnError:false
create table ZY_MOBILE_BASIC
(
    id            VARCHAR2(64) not null,
    create_date   DATE,
    create_by     VARCHAR2(64),
    update_date   DATE,
    update_by     VA<PERSON>HAR2(64),
    remarks       VARCHAR2(255),
    del_flag      CHAR(1) default '0',
    name          VARCHAR2(200),
    label         VARCHAR2(200),
    category      VARCHAR2(64),
    icon          VARCHAR2(64),
    color         VARCHAR2(64),
    schemeinfo_id VARCHAR2(64),
    users         VARCHAR2(4000),
    offices       VARCHAR2(4000),
    roles         VARCHAR2(4000),
    type          CHAR(1) default '0',
    del_url       VARCHAR2(128),
    jump_type     VARCHAR2(30) default 0,
    sort          NUMBER(8) default 0
);
comment on column ZY_MOBILE_BASIC.users
    is '用户权限';
comment on column ZY_MOBILE_BASIC.offices
    is '单位权限';
comment on column ZY_MOBILE_BASIC.roles
    is '角色权限';
comment on column ZY_MOBILE_BASIC.type
    is 'app按钮类型 0为默认按钮 1为拓展按钮';
comment on column ZY_MOBILE_BASIC.del_url
    is '跳转url';
comment on column ZY_MOBILE_BASIC.jump_type
    is '跳转类型 0只读 1可编辑 2自定义';
comment on column ZY_MOBILE_BASIC.sort
    is '排序';

create table ZY_MOBILE_MARKED
(
    schemeinfo_id VARCHAR2(64),
    businessid    VARCHAR2(64),
    user_id       VARCHAR2(64),
    create_by     VARCHAR2(255),
    create_date   DATE,
    proc_inst_id  VARCHAR2(64)
)
    tablespace XJRSOFT
    pctfree 10
    initrans 1
    maxtrans 255
    storage
(
    initial 64K
    next 1M
    minextents 1
    maxextents unlimited
);
comment on column ZY_MOBILE_MARKED.create_by
    is '创建人';
comment on column ZY_MOBILE_MARKED.create_date
    is '创建时间';
comment on column ZY_MOBILE_MARKED.proc_inst_id
    is '流程实例';

create table ZY_MOBILE_SURVEY
(
    row_id      VARCHAR2(64) not null,
    create_by   VARCHAR2(64),
    create_date DATE,
    update_by   VARCHAR2(64),
    update_date DATE,
    remarks     VARCHAR2(4000),
    design_id   VARCHAR2(64),
    content     CLOB
)
    tablespace XJRSOFT
    pctfree 10
    initrans 1
    maxtrans 255;
comment on table ZY_MOBILE_SURVEY
    is '问卷填写记录表';

comment on column ZY_MOBILE_SURVEY.row_id
    is '主键';
comment on column ZY_MOBILE_SURVEY.create_by
    is '创建人';
comment on column ZY_MOBILE_SURVEY.create_date
    is '创建时间';
comment on column ZY_MOBILE_SURVEY.update_by
    is '修改人';
comment on column ZY_MOBILE_SURVEY.update_date
    is '修改时间';
comment on column ZY_MOBILE_SURVEY.remarks
    is '备注';
comment on column ZY_MOBILE_SURVEY.design_id
    is '问卷设计id';
comment on column ZY_MOBILE_SURVEY.content
    is '填写内容';
-- Create/Recreate primary, unique and foreign key constraints
alter table ZY_MOBILE_SURVEY
    add constraint PK_ZY_MOBILE_SURVEY primary key (ROW_ID)
        using index
            tablespace XJRSOFT
            pctfree 10
            initrans 2
            maxtrans 255;


create table ZY_MOBILE_VIEW
(
    id            VARCHAR2(64) not null,
    scheme        CLOB,
    create_date   DATE,
    create_by     VARCHAR2(64),
    update_date   DATE,
    update_by     VARCHAR2(64),
    remarks       VARCHAR2(255),
    del_flag      CHAR(1) default '0',
    name          VARCHAR2(200),
    schemeinfo_id VARCHAR2(64),
    is_used       CHAR(1) default '0',
    is_todo_view  CHAR(1),
    ext_param     CLOB,
    basic_id      VARCHAR2(64),
    is_survey     CHAR(1) default '0',
    survey_type   VARCHAR2(64)
)
    tablespace XJRSOFT
    pctfree 10
    initrans 1
    maxtrans 255
    storage
(
    initial 128K
    next 1M
    minextents 1
    maxextents unlimited
);
comment on column ZY_MOBILE_VIEW.ext_param
    is '扩展参数';
comment on column ZY_MOBILE_VIEW.basic_id
    is '对应主菜单Id';
comment on column ZY_MOBILE_VIEW.is_survey
    is '是否为调查问卷视图 0否 1是';
comment on column ZY_MOBILE_VIEW.survey_type
    is '调查问卷视图列表类型';




