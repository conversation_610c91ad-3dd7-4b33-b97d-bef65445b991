--liquibase formatted sql
--changeset dyz:2024122301 failOnError:false
-- Add/modify columns
-- Create table
-- Create table
create table xjr_gryj
(
    id          NVARCHAR2(255),
    yj         NVARCHAR2(255),
    tenant_id      NUMBER(21),
    create_date    TIMESTAMP(6),
    create_user_id VARCHAR2(64),
    modify_date    TIMESTAMP(6),
    modify_user_id VARCHAR2(64),
    delete_mark    NUMBER(11),
    enabled_mark   NUMBER(11)
)
;
-- Add comments to the columns
comment on column xjr_gryj.yj
    is '意见';
comment on column xjr_gryj.tenant_id
    is '租户id';
comment on column xjr_gryj.create_date
    is '创建时间';
comment on column xjr_gryj.create_user_id
    is '创建人id';
comment on column xjr_gryj.modify_date
    is '修改时间';
comment on column xjr_gryj.modify_user_id
    is '修改人id';
comment on column xjr_gryj.delete_mark
    is '删除标记';
comment on column xjr_gryj.enabled_mark
    is '启用标记';
-- Create/Recreate primary, unique and foreign key constraints
alter table xjr_gryj
    add constraint xjr_gryj_id primary key (ID);

