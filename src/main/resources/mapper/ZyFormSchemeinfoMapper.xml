<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xjrsoft.module.zy.form.mapper.ZyFormSchemeinfoMapper">
  <resultMap id="BaseResultMap" type="com.xjrsoft.module.zy.form.pojo.entity.ZyFormSchemeinfo">
    <!--@mbg.generated-->
    <!--@Table XJRSOFT.ZY_FORM_SCHEMEINFO-->
    <result column="ID" jdbcType="VARCHAR" property="id" />
    <result column="NAME" jdbcType="VARCHAR" property="name" />
    <result column="TYPE" jdbcType="DECIMAL" property="type" />
    <result column="CATEGORY" jdbcType="VARCHAR" property="category" />
    <result column="SCHEME_ID" jdbcType="VARCHAR" property="schemeId" />
    <result column="ENABLED_MARK" jdbcType="DECIMAL" property="enabledMark" />
    <result column="DESCRIPTION" jdbcType="VARCHAR" property="description" />
    <result column="URL_ADDRESS" jdbcType="VARCHAR" property="urlAddress" />
    <result column="CREATE_DATE" jdbcType="TIMESTAMP" property="createDate" />
    <result column="CREATE_BY" jdbcType="VARCHAR" property="createBy" />
    <result column="UPDATE_DATE" jdbcType="TIMESTAMP" property="updateDate" />
    <result column="UPDATE_BY" jdbcType="VARCHAR" property="updateBy" />
    <result column="REMARKS" jdbcType="VARCHAR" property="remarks" />
    <result column="DEL_FLAG" jdbcType="CHAR" property="delFlag" />
    <result column="LAYOUT" jdbcType="VARCHAR" property="layout" />
    <result column="MODEL_ID" jdbcType="VARCHAR" property="modelId" />
    <result column="PAGE_SHOW_TYPE" jdbcType="VARCHAR" property="pageShowType" />
    <result column="DATASOURCE_ID" jdbcType="VARCHAR" property="datasourceId" />
    <result column="LEFT_TITLE" jdbcType="VARCHAR" property="leftTitle" />
    <result column="LEFT_MULTI_CHECK" jdbcType="CHAR" property="leftMultiCheck" />
    <result column="LEFT_EXPAND" jdbcType="CHAR" property="leftExpand" />
    <result column="SELECT_FIELD" jdbcType="VARCHAR" property="selectField" />
    <result column="IS_LEAF" jdbcType="CHAR" property="isLeaf" />
    <result column="LEFT_TOP_LEVEL_ID" jdbcType="VARCHAR" property="leftTopLevelId" />
    <result column="EXT_PARAM" jdbcType="CLOB" property="extParam" />
    <result column="WF_TYPE" jdbcType="CHAR" property="wfType" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, "NAME", "TYPE", CATEGORY, SCHEME_ID, ENABLED_MARK, DESCRIPTION, URL_ADDRESS, 
    CREATE_DATE, CREATE_BY, UPDATE_DATE, UPDATE_BY, REMARKS, DEL_FLAG, LAYOUT, MODEL_ID, 
    PAGE_SHOW_TYPE, DATASOURCE_ID, LEFT_TITLE, LEFT_MULTI_CHECK, LEFT_EXPAND, SELECT_FIELD, 
    IS_LEAF, LEFT_TOP_LEVEL_ID, EXT_PARAM, WF_TYPE
  </sql>
</mapper>