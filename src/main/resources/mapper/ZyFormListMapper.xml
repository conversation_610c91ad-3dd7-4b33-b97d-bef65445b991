<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xjrsoft.module.zy.form.mapper.ZyFormListMapper">
  <resultMap id="BaseResultMap" type="com.xjrsoft.module.zy.form.pojo.entity.ZyFormList">
    <!--@mbg.generated-->
    <!--@Table XJRSOFT.ZY_FORM_LIST-->
    <result column="ID" jdbcType="VARCHAR" property="id" />
    <result column="SCHEMEINFO_ID" jdbcType="VARCHAR" property="schemeinfoId" />
    <result column="NUM" jdbcType="VARCHAR" property="num" />
    <result column="NAME" jdbcType="VARCHAR" property="name" />
    <result column="CONDITION" jdbcType="VARCHAR" property="condition" />
    <result column="PAGE_SIZE" jdbcType="VARCHAR" property="pageSize" />
    <result column="SORT" jdbcType="VARCHAR" property="sort" />
    <result column="SHOW" jdbcType="VARCHAR" property="show" />
    <result column="CREATE_DATE" jdbcType="TIMESTAMP" property="createDate" />
    <result column="CREATE_BY" jdbcType="VARCHAR" property="createBy" />
    <result column="UPDATE_DATE" jdbcType="TIMESTAMP" property="updateDate" />
    <result column="UPDATE_BY" jdbcType="VARCHAR" property="updateBy" />
    <result column="REMARKS" jdbcType="VARCHAR" property="remarks" />
    <result column="DEL_FLAG" jdbcType="CHAR" property="delFlag" />
    <result column="SCHEME" jdbcType="CLOB" property="scheme" />
    <result column="DATASOURCE_ID" jdbcType="VARCHAR" property="datasourceId" />
    <result column="DBCLICK" jdbcType="CHAR" property="dbclick" />
    <result column="BTNNUM" jdbcType="DECIMAL" property="btnnum" />
    <result column="PARAMS" jdbcType="CLOB" property="params" />
    <result column="AUTH" jdbcType="CLOB" property="auth" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, SCHEMEINFO_ID, NUM, "NAME", "CONDITION", PAGE_SIZE, SORT, "SHOW", CREATE_DATE, 
    CREATE_BY, UPDATE_DATE, UPDATE_BY, REMARKS, DEL_FLAG, SCHEME, DATASOURCE_ID, DBCLICK, 
    BTNNUM, PARAMS, AUTH
  </sql>
</mapper>