<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xjrsoft.module.zy.form.mapper.ZyFormListFieldMapper">
  <resultMap id="BaseResultMap" type="com.xjrsoft.module.zy.form.pojo.entity.ZyFormListField">
    <!--@mbg.generated-->
    <!--@Table XJRSOFT.ZY_FORM_LIST_FIELD-->
    <result column="ID" jdbcType="VARCHAR" property="id" />
    <result column="LIST_ID" jdbcType="VARCHAR" property="listId" />
    <result column="FIELD_ID" jdbcType="VARCHAR" property="fieldId" />
    <result column="NAME" jdbcType="VARCHAR" property="name" />
    <result column="WIDTH" jdbcType="DECIMAL" property="width" />
    <result column="ALIGN" jdbcType="VARCHAR" property="align" />
    <result column="COMMAFY" jdbcType="CHAR" property="commafy" />
    <result column="PARTAKE_KEYWORD" jdbcType="CHAR" property="partakeKeyword" />
    <result column="PAGE_SUMMARY" jdbcType="CHAR" property="pageSummary" />
    <result column="SUMMARY" jdbcType="CHAR" property="summary" />
    <result column="PARENT_ID" jdbcType="VARCHAR" property="parentId" />
    <result column="COMPONT_ID" jdbcType="VARCHAR" property="compontId" />
    <result column="SORT" jdbcType="DECIMAL" property="sort" />
    <result column="FROZEN" jdbcType="VARCHAR" property="frozen" />
    <result column="COUNT" jdbcType="CHAR" property="count" />
    <result column="BUTTONS" jdbcType="VARCHAR" property="buttons" />
    <result column="FORMATTER" jdbcType="CLOB" property="formatter" />
    <result column="FILTERABLE" jdbcType="CHAR" property="filterable" />
    <result column="HIDDEN" jdbcType="CHAR" property="hidden" />
    <result column="PARAMS" jdbcType="CLOB" property="params" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, LIST_ID, FIELD_ID, "NAME", WIDTH, ALIGN, COMMAFY, PARTAKE_KEYWORD, PAGE_SUMMARY, 
    SUMMARY, PARENT_ID, COMPONT_ID, SORT, FROZEN, "COUNT", BUTTONS, FORMATTER, FILTERABLE, 
    HIDDEN, PARAMS
  </sql>
</mapper>