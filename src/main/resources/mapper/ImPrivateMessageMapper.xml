<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xjrsoft.module.im.mapper.ImPrivateMessageMapper">
  <resultMap id="BaseResultMap" type="com.xjrsoft.module.im.entity.ImPrivateMessage">
    <!--@mbg.generated-->
    <!--@Table im_private_message-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="send_id" jdbcType="BIGINT" property="sendId" />
    <result column="recv_id" jdbcType="BIGINT" property="recvId" />
    <result column="content" jdbcType="LONGVARCHAR" property="content" />
    <result column="type" jdbcType="BOOLEAN" property="type" />
    <result column="status" jdbcType="BOOLEAN" property="status" />
    <result column="send_time" jdbcType="TIMESTAMP" property="sendTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, send_id, recv_id, content, `type`, `status`, send_time
  </sql>
</mapper>