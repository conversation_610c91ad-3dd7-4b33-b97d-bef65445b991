<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xjrsoft.module.zy.form.mapper.ZyFormListMenuMapper">
  <resultMap id="BaseResultMap" type="com.xjrsoft.module.zy.form.pojo.entity.ZyFormListMenu">
    <!--@mbg.generated-->
    <!--@Table XJRSOFT.ZY_FORM_LIST_MENU-->
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="LIST_ID" jdbcType="VARCHAR" property="listId" />
    <result column="MENU_ID" jdbcType="VARCHAR" property="menuId" />
    <result column="NODE_ID" jdbcType="VARCHAR" property="nodeId" />
    <result column="CREATE_DATE" jdbcType="TIMESTAMP" property="createDate" />
    <result column="CREATE_BY" jdbcType="VARCHAR" property="createBy" />
    <result column="UPDATE_DATE" jdbcType="TIMESTAMP" property="updateDate" />
    <result column="UPDATE_BY" jdbcType="VARCHAR" property="updateBy" />
    <result column="REMARKS" jdbcType="VARCHAR" property="remarks" />
    <result column="DEL_FLAG" jdbcType="CHAR" property="delFlag" />
    <result column="SCHEME_ID" jdbcType="VARCHAR" property="schemeId" />
    <result column="VIEW_ID" jdbcType="VARCHAR" property="viewId" />
    <result column="LISTSCHEME_ID" jdbcType="VARCHAR" property="listschemeId" />
    <result column="TYPE" jdbcType="CHAR" property="type" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, LIST_ID, MENU_ID, NODE_ID, CREATE_DATE, CREATE_BY, UPDATE_DATE, UPDATE_BY, REMARKS, 
    DEL_FLAG, SCHEME_ID, VIEW_ID, LISTSCHEME_ID, "TYPE"
  </sql>
</mapper>