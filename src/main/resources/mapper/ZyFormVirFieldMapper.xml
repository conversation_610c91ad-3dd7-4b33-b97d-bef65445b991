<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xjrsoft.module.zy.form.mapper.ZyFormVirFieldMapper">
  <resultMap id="BaseResultMap" type="com.xjrsoft.module.zy.form.pojo.entity.ZyFormVirField">
    <!--@mbg.generated-->
    <!--@Table XJRSOFT.ZY_FORM_VIR_FIELD-->
    <result column="TABLE_ID" jdbcType="VARCHAR" property="tableId" />
    <result column="FIELD_CODE" jdbcType="VARCHAR" property="fieldCode" />
    <result column="FIELD_NAME" jdbcType="VARCHAR" property="fieldName" />
    <result column="REF_FIELD" jdbcType="VARCHAR" property="refField" />
    <result column="REF_TABLE_ID" jdbcType="VARCHAR" property="refTableId" />
    <result column="REF_CONDITION" jdbcType="VARCHAR" property="refCondition" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    TABLE_ID, FIELD_CODE, FIELD_NAME, REF_FIELD, REF_TABLE_ID, REF_CONDITION
  </sql>
</mapper>