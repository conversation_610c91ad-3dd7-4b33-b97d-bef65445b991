<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xjrsoft.module.im.mapper.ImGroupMapper">
  <resultMap id="BaseResultMap" type="com.xjrsoft.module.im.entity.ImGroup">
    <!--@mbg.generated-->
    <!--@Table im_group-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="owner_id" jdbcType="BIGINT" property="ownerId" />
    <result column="head_image" jdbcType="VARCHAR" property="headImage" />
    <result column="head_image_thumb" jdbcType="VARCHAR" property="headImageThumb" />
    <result column="notice" jdbcType="VARCHAR" property="notice" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="deleted" jdbcType="BOOLEAN" property="deleted" />
    <result column="created_time" jdbcType="TIMESTAMP" property="createdTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, `name`, owner_id, head_image, head_image_thumb, notice, remark, deleted, created_time
  </sql>
</mapper>