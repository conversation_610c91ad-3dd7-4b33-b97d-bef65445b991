<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xjrsoft.module.im.mapper.ImFriendMapper">
  <resultMap id="BaseResultMap" type="com.xjrsoft.module.im.entity.ImFriend">
    <!--@mbg.generated-->
    <!--@Table im_friend-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="friend_id" jdbcType="BIGINT" property="friendId" />
    <result column="friend_nick_name" jdbcType="VARCHAR" property="friendNickName" />
    <result column="friend_head_image" jdbcType="VARCHAR" property="friendHeadImage" />
    <result column="created_time" jdbcType="TIMESTAMP" property="createdTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, user_id, friend_id, friend_nick_name, friend_head_image, created_time
  </sql>
</mapper>