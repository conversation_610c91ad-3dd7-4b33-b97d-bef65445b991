<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xjrsoft.module.zy.form.mapper.ZyPrintItemDetailMapper">
  <resultMap id="BaseResultMap" type="com.xjrsoft.module.zy.form.pojo.entity.ZyPrintItemDetail">
    <!--@mbg.generated-->
    <!--@Table XJRSOFT.ZY_PRINT_ITEM_DETAIL-->
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="GLTJ" jdbcType="VARCHAR" property="gltj" />
    <result column="DATA_SOURCE_ID" jdbcType="VARCHAR" property="dataSourceId" />
    <result column="ITEM_ID" jdbcType="VARCHAR" property="itemId" />
    <result column="PARENT_ID" jdbcType="VARCHAR" property="parentId" />
    <result column="NAME" jdbcType="VARCHAR" property="name" />
    <result column="CODE" jdbcType="VARCHAR" property="code" />
    <result column="CONTENT" jdbcType="CLOB" property="content" />
    <result column="SORT_CODE" jdbcType="DECIMAL" property="sortCode" />
    <result column="REMARK" jdbcType="VARCHAR" property="remark" />
    <result column="CREATE_DATE" jdbcType="TIMESTAMP" property="createDate" />
    <result column="CREATE_USER_ID" jdbcType="DECIMAL" property="createUserId" />
    <result column="MODIFY_DATE" jdbcType="TIMESTAMP" property="modifyDate" />
    <result column="MODIFY_USER_ID" jdbcType="DECIMAL" property="modifyUserId" />
    <result column="DELETE_MARK" jdbcType="DECIMAL" property="deleteMark" />
    <result column="ENABLED_MARK" jdbcType="DECIMAL" property="enabledMark" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, GLTJ, DATA_SOURCE_ID, ITEM_ID, PARENT_ID, "NAME", CODE, CONTENT, SORT_CODE, REMARK, 
    CREATE_DATE, CREATE_USER_ID, MODIFY_DATE, MODIFY_USER_ID, DELETE_MARK, ENABLED_MARK
  </sql>
</mapper>