<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xjrsoft.module.zy.form.mapper.ZyFormCompontMapper">
  <resultMap id="BaseResultMap" type="com.xjrsoft.module.zy.form.pojo.entity.ZyFormCompont">
    <!--@mbg.generated-->
    <!--@Table XJRSOFT.ZY_FORM_COMPONT-->
    <result column="ID" jdbcType="VARCHAR" property="id" />
    <result column="TITLE" jdbcType="VARCHAR" property="title" />
    <result column="TYPE" jdbcType="CHAR" property="type" />
    <result column="SCHEME" jdbcType="CLOB" property="scheme" />
    <result column="SCALE" jdbcType="CHAR" property="scale" />
    <result column="CREATE_BY" jdbcType="VARCHAR" property="createBy" />
    <result column="CREATE_DATE" jdbcType="TIMESTAMP" property="createDate" />
    <result column="UPDATE_BY" jdbcType="VARCHAR" property="updateBy" />
    <result column="UPDATE_DATE" jdbcType="TIMESTAMP" property="updateDate" />
    <result column="VERSION" jdbcType="DECIMAL" property="version" />
    <result column="REMARKS" jdbcType="VARCHAR" property="remarks" />
    <result column="ICON" jdbcType="VARCHAR" property="icon" />
    <result column="METADATA" jdbcType="CLOB" property="metadata" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, TITLE, "TYPE", SCHEME, "SCALE", CREATE_BY, CREATE_DATE, UPDATE_BY, UPDATE_DATE, 
    VERSION, REMARKS, ICON, METADATA
  </sql>
</mapper>