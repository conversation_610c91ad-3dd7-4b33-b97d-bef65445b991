<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xjrsoft.module.zy.form.mapper.ZyFormEditWidgetMapper">
  <resultMap id="BaseResultMap" type="com.xjrsoft.module.zy.form.pojo.entity.ZyFormEditWidget">
    <!--@mbg.generated-->
    <!--@Table XJRSOFT.ZY_FORM_EDIT_WIDGET-->
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="EDIT_ID" jdbcType="VARCHAR" property="editId" />
    <result column="WIDGET_ID" jdbcType="VARCHAR" property="widgetId" />
    <result column="IDX" jdbcType="DECIMAL" property="idx" />
    <result column="HEIGHT" jdbcType="DECIMAL" property="height" />
    <result column="WIDTH" jdbcType="DECIMAL" property="width" />
    <result column="WIDGET_NAME" jdbcType="VARCHAR" property="widgetName" />
    <result column="SHOW_HEAD" jdbcType="VARCHAR" property="showHead" />
    <result column="GROUP_NO" jdbcType="VARCHAR" property="groupNo" />
    <result column="GROUP_NAME" jdbcType="VARCHAR" property="groupName" />
    <result column="SCOPE" jdbcType="CHAR" property="scope" />
    <result column="COCODE" jdbcType="VARCHAR" property="cocode" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, EDIT_ID, WIDGET_ID, IDX, HEIGHT, WIDTH, WIDGET_NAME, SHOW_HEAD, GROUP_NO, GROUP_NAME, 
    "SCOPE", COCODE
  </sql>
</mapper>