<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xjrsoft.module.fp.mapper.BizFpMapper">
  <resultMap id="BaseResultMap" type="com.xjrsoft.module.fp.entity.BizFp">
    <!--@mbg.generated-->
    <!--@Table BIZ_FP-->
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="INVOICE_CODE" jdbcType="VARCHAR" property="invoiceCode" />
    <result column="INVOICE_NUMBER" jdbcType="VARCHAR" property="invoiceNumber" />
    <result column="KPRQ" jdbcType="TIMESTAMP" property="kprq" />
    <result column="RULES_CODE" jdbcType="VARCHAR" property="rulesCode" />
    <result column="BUY_NAME" jdbcType="VARCHAR" property="buyName" />
    <result column="GFNSRSBH" jdbcType="VARCHAR" property="gfnsrsbh" />
    <result column="BUY_PHONE_ADDRESS" jdbcType="VARCHAR" property="buyPhoneAddress" />
    <result column="BUY_ACCOUNT" jdbcType="VARCHAR" property="buyAccount" />
    <result column="SALES_NAME" jdbcType="VARCHAR" property="salesName" />
    <result column="XFNSRSBH" jdbcType="VARCHAR" property="xfnsrsbh" />
    <result column="SALES_PHONE_ADDRESS" jdbcType="VARCHAR" property="salesPhoneAddress" />
    <result column="SALES_ACCOUNT" jdbcType="VARCHAR" property="salesAccount" />
    <result column="MONEY" jdbcType="VARCHAR" property="money" />
    <result column="SETTLEMENT_METHODS" jdbcType="VARCHAR" property="settlementMethods" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, INVOICE_CODE, INVOICE_NUMBER, KPRQ, RULES_CODE, BUY_NAME, GFNSRSBH, BUY_PHONE_ADDRESS, 
    BUY_ACCOUNT, SALES_NAME, XFNSRSBH, SALES_PHONE_ADDRESS, SALES_ACCOUNT, MONEY, SETTLEMENT_METHODS
  </sql>
</mapper>