<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xjrsoft.module.zy.form.mapper.ZyFormModifyMapper">
  <resultMap id="BaseResultMap" type="com.xjrsoft.module.zy.form.pojo.entity.ZyFormModify">
    <!--@mbg.generated-->
    <!--@Table XJRSOFT.ZY_FORM_MODIFY-->
    <result column="ID" jdbcType="VARCHAR" property="id" />
    <result column="NO" jdbcType="VARCHAR" property="no" />
    <result column="CREATE_DATE" jdbcType="TIMESTAMP" property="createDate" />
    <result column="UPDATE_DATE" jdbcType="TIMESTAMP" property="updateDate" />
    <result column="CREATE_BY" jdbcType="VARCHAR" property="createBy" />
    <result column="UPDATE_BY" jdbcType="VARCHAR" property="updateBy" />
    <result column="REASON" jdbcType="VARCHAR" property="reason" />
    <result column="BEFORE_TEXT" jdbcType="CLOB" property="beforeText" />
    <result column="AFTER_TEXT" jdbcType="CLOB" property="afterText" />
    <result column="EDIT_ID" jdbcType="VARCHAR" property="editId" />
    <result column="REF_TABLE" jdbcType="VARCHAR" property="refTable" />
    <result column="REF_ID" jdbcType="VARCHAR" property="refId" />
    <result column="IS_FINISHED" jdbcType="CHAR" property="isFinished" />
    <result column="ATTR_1" jdbcType="VARCHAR" property="attr1" />
    <result column="ATTR_2" jdbcType="VARCHAR" property="attr2" />
    <result column="ATTR_3" jdbcType="VARCHAR" property="attr3" />
    <result column="ATTR_4" jdbcType="VARCHAR" property="attr4" />
    <result column="ATTR_5" jdbcType="VARCHAR" property="attr5" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, "NO", CREATE_DATE, UPDATE_DATE, CREATE_BY, UPDATE_BY, REASON, BEFORE_TEXT, AFTER_TEXT, 
    EDIT_ID, REF_TABLE, REF_ID, IS_FINISHED, ATTR_1, ATTR_2, ATTR_3, ATTR_4, ATTR_5
  </sql>
</mapper>