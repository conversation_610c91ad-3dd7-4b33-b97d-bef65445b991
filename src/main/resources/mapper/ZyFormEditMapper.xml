<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xjrsoft.module.zy.form.mapper.ZyFormEditMapper">
  <resultMap id="BaseResultMap" type="com.xjrsoft.module.zy.form.pojo.entity.ZyFormEdit">
    <!--@mbg.generated-->
    <!--@Table XJRSOFT.ZY_FORM_EDIT-->
    <result column="ID" jdbcType="VARCHAR" property="id" />
    <result column="SCHEMEINFO_ID" jdbcType="VARCHAR" property="schemeinfoId" />
    <result column="NUM" jdbcType="VARCHAR" property="num" />
    <result column="NAME" jdbcType="VARCHAR" property="name" />
    <result column="LAYOUT" jdbcType="VARCHAR" property="layout" />
    <result column="TEMPLATE" jdbcType="VARCHAR" property="template" />
    <result column="SCHEME" jdbcType="CLOB" property="scheme" />
    <result column="CREATE_DATE" jdbcType="TIMESTAMP" property="createDate" />
    <result column="CREATE_BY" jdbcType="VARCHAR" property="createBy" />
    <result column="UPDATE_DATE" jdbcType="TIMESTAMP" property="updateDate" />
    <result column="UPDATE_BY" jdbcType="VARCHAR" property="updateBy" />
    <result column="REMARKS" jdbcType="VARCHAR" property="remarks" />
    <result column="DEL_FLAG" jdbcType="CHAR" property="delFlag" />
    <result column="OPEN_TYPE" jdbcType="CHAR" property="openType" />
    <result column="WIDTH" jdbcType="DECIMAL" property="width" />
    <result column="HEIGHT" jdbcType="DECIMAL" property="height" />
    <result column="LIST_ID" jdbcType="VARCHAR" property="listId" />
    <result column="UPLOAD_TYPE" jdbcType="CHAR" property="uploadType" />
    <result column="PROCESS" jdbcType="CHAR" property="process" />
    <result column="IS_MODIFY" jdbcType="CHAR" property="isModify" />
    <result column="BOARD_ID" jdbcType="VARCHAR" property="boardId" />
    <result column="RIGHT_WIDTH" jdbcType="VARCHAR" property="rightWidth" />
    <result column="BTN_NUM" jdbcType="DECIMAL" property="btnNum" />
    <result column="PARAMS" jdbcType="CLOB" property="params" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, SCHEMEINFO_ID, NUM, "NAME", LAYOUT, "TEMPLATE", SCHEME, CREATE_DATE, CREATE_BY, 
    UPDATE_DATE, UPDATE_BY, REMARKS, DEL_FLAG, OPEN_TYPE, WIDTH, HEIGHT, LIST_ID, UPLOAD_TYPE, 
    "PROCESS", IS_MODIFY, BOARD_ID, RIGHT_WIDTH, BTN_NUM, PARAMS
  </sql>
</mapper>