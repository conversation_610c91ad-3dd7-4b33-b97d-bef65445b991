<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xjrsoft.module.zy.form.mapper.ZyFormButtonMapper">
  <resultMap id="BaseResultMap" type="com.xjrsoft.module.zy.form.pojo.entity.ZyFormButton">
    <!--@mbg.generated-->
    <!--@Table XJRSOFT.ZY_FORM_BUTTON-->
    <result column="ID" jdbcType="VARCHAR" property="id" />
    <result column="SCHEMEINFO_ID" jdbcType="VARCHAR" property="schemeinfoId" />
    <result column="NUM" jdbcType="VARCHAR" property="num" />
    <result column="NAME" jdbcType="VARCHAR" property="name" />
    <result column="SCRIPT" jdbcType="VARCHAR" property="script" />
    <result column="PRODUCER" jdbcType="VARCHAR" property="producer" />
    <result column="ICON" jdbcType="VARCHAR" property="icon" />
    <result column="CREATE_DATE" jdbcType="TIMESTAMP" property="createDate" />
    <result column="CREATE_BY" jdbcType="VARCHAR" property="createBy" />
    <result column="UPDATE_DATE" jdbcType="TIMESTAMP" property="updateDate" />
    <result column="UPDATE_BY" jdbcType="VARCHAR" property="updateBy" />
    <result column="REMARKS" jdbcType="VARCHAR" property="remarks" />
    <result column="DEL_FLAG" jdbcType="CHAR" property="delFlag" />
    <result column="MODEL" jdbcType="CHAR" property="model" />
    <result column="ERRORTYPE" jdbcType="CHAR" property="errortype" />
    <result column="BUTTON_PARAMS" jdbcType="VARCHAR" property="buttonParams" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, SCHEMEINFO_ID, NUM, "NAME", SCRIPT, PRODUCER, ICON, CREATE_DATE, CREATE_BY, UPDATE_DATE, 
    UPDATE_BY, REMARKS, DEL_FLAG, MODEL, ERRORTYPE, BUTTON_PARAMS
  </sql>
</mapper>