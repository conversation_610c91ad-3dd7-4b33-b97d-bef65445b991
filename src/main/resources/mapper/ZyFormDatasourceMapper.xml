<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xjrsoft.module.zy.form.mapper.ZyFormDatasourceMapper">
  <resultMap id="BaseResultMap" type="com.xjrsoft.module.zy.form.pojo.entity.ZyFormDatasource">
    <!--@mbg.generated-->
    <!--@Table XJRSOFT.ZY_FORM_DATASOURCE-->
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="CODE" jdbcType="VARCHAR" property="code" />
    <result column="NAME" jdbcType="VARCHAR" property="name" />
    <result column="DBID" jdbcType="VARCHAR" property="dbid" />
    <result column="QUERY_SQL" jdbcType="VARCHAR" property="querySql" />
    <result column="DESCRIPTION" jdbcType="VARCHAR" property="description" />
    <result column="CREATE_DATE" jdbcType="TIMESTAMP" property="createDate" />
    <result column="CREATE_BY" jdbcType="VARCHAR" property="createBy" />
    <result column="UPDATE_DATE" jdbcType="TIMESTAMP" property="updateDate" />
    <result column="UPDATE_BY" jdbcType="VARCHAR" property="updateBy" />
    <result column="REMARKS" jdbcType="VARCHAR" property="remarks" />
    <result column="DEL_FLAG" jdbcType="CHAR" property="delFlag" />
    <result column="MODULE" jdbcType="VARCHAR" property="module" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, CODE, "NAME", DBID, QUERY_SQL, DESCRIPTION, CREATE_DATE, CREATE_BY, UPDATE_DATE, 
    UPDATE_BY, REMARKS, DEL_FLAG, "MODULE"
  </sql>
</mapper>