<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xjrsoft.module.zy.form.mapper.ZyFormListButtonMapper">
  <resultMap id="BaseResultMap" type="com.xjrsoft.module.zy.form.pojo.entity.ZyFormListButton">
    <!--@mbg.generated-->
    <!--@Table XJRSOFT.ZY_FORM_LIST_BUTTON-->
    <result column="ID" jdbcType="VARCHAR" property="id" />
    <result column="LIST_ID" jdbcType="VARCHAR" property="listId" />
    <result column="BUTTON_ID" jdbcType="VARCHAR" property="buttonId" />
    <result column="FORMULA_ID" jdbcType="VARCHAR" property="formulaId" />
    <result column="MOMENT" jdbcType="VARCHAR" property="moment" />
    <result column="TYPE" jdbcType="CHAR" property="type" />
    <result column="SORT" jdbcType="DECIMAL" property="sort" />
    <result column="BUTTON_NAME" jdbcType="VARCHAR" property="buttonName" />
    <result column="OPEN_TYPE" jdbcType="VARCHAR" property="openType" />
    <result column="BUTTON_URL" jdbcType="VARCHAR" property="buttonUrl" />
    <result column="PARAMS" jdbcType="CLOB" property="params" />
    <result column="AUTH" jdbcType="CLOB" property="auth" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, LIST_ID, BUTTON_ID, FORMULA_ID, MOMENT, "TYPE", SORT, BUTTON_NAME, OPEN_TYPE, 
    BUTTON_URL, PARAMS, AUTH
  </sql>
</mapper>