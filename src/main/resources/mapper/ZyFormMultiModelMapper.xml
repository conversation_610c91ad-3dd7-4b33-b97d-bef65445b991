<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xjrsoft.module.zy.form.mapper.ZyFormMultiModelMapper">
  <resultMap id="BaseResultMap" type="com.xjrsoft.module.zy.form.pojo.entity.ZyFormMultiModel">
    <!--@mbg.generated-->
    <!--@Table XJRSOFT.ZY_FORM_MULTI_MODEL-->
    <result column="ID" jdbcType="VARCHAR" property="id" />
    <result column="CREATE_DATE" jdbcType="TIMESTAMP" property="createDate" />
    <result column="CREATE_BY" jdbcType="VARCHAR" property="createBy" />
    <result column="UPDATE_DATE" jdbcType="TIMESTAMP" property="updateDate" />
    <result column="UPDATE_BY" jdbcType="VARCHAR" property="updateBy" />
    <result column="REMARKS" jdbcType="VARCHAR" property="remarks" />
    <result column="DEL_FLAG" jdbcType="CHAR" property="delFlag" />
    <result column="SCHEME_ID" jdbcType="VARCHAR" property="schemeId" />
    <result column="MODEL_ID" jdbcType="VARCHAR" property="modelId" />
    <result column="IS_DEFAULT" jdbcType="CHAR" property="isDefault" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, CREATE_DATE, CREATE_BY, UPDATE_DATE, UPDATE_BY, REMARKS, DEL_FLAG, SCHEME_ID, 
    MODEL_ID, IS_DEFAULT
  </sql>
</mapper>