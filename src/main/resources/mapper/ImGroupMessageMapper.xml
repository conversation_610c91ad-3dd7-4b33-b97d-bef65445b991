<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xjrsoft.module.im.mapper.ImGroupMessageMapper">
  <resultMap id="BaseResultMap" type="com.xjrsoft.module.im.entity.ImGroupMessage">
    <!--@mbg.generated-->
    <!--@Table im_group_message-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="group_id" jdbcType="BIGINT" property="groupId" />
    <result column="send_id" jdbcType="BIGINT" property="sendId" />
    <result column="send_nick_name" jdbcType="VARCHAR" property="sendNickName" />
    <result column="recv_ids" jdbcType="VARCHAR" property="recvIds" />
    <result column="content" jdbcType="LONGVARCHAR" property="content" />
    <result column="at_user_ids" jdbcType="VARCHAR" property="atUserIds" />
    <result column="receipt" jdbcType="TINYINT" property="receipt" />
    <result column="receipt_ok" jdbcType="TINYINT" property="receiptOk" />
    <result column="type" jdbcType="BOOLEAN" property="type" />
    <result column="status" jdbcType="BOOLEAN" property="status" />
    <result column="send_time" jdbcType="TIMESTAMP" property="sendTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, group_id, send_id, send_nick_name, recv_ids, content, at_user_ids, receipt, receipt_ok, 
    `type`, `status`, send_time
  </sql>
</mapper>