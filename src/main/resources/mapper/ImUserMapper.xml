<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xjrsoft.module.im.mapper.ImUserMapper">
  <resultMap id="BaseResultMap" type="com.xjrsoft.module.im.entity.ImUser">
    <!--@mbg.generated-->
    <!--@Table im_user-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="user_name" jdbcType="VARCHAR" property="userName" />
    <result column="nick_name" jdbcType="VARCHAR" property="nickName" />
    <result column="head_image" jdbcType="VARCHAR" property="headImage" />
    <result column="head_image_thumb" jdbcType="VARCHAR" property="headImageThumb" />
    <result column="password" jdbcType="VARCHAR" property="password" />
    <result column="sex" jdbcType="BOOLEAN" property="sex" />
    <result column="type" jdbcType="SMALLINT" property="type" />
    <result column="signature" jdbcType="VARCHAR" property="signature" />
    <result column="last_login_time" jdbcType="TIMESTAMP" property="lastLoginTime" />
    <result column="created_time" jdbcType="TIMESTAMP" property="createdTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, user_name, nick_name, head_image, head_image_thumb, `password`, sex, `type`, 
    signature, last_login_time, created_time
  </sql>
</mapper>