<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xjrsoft.module.zy.form.mapper.XjrDictionaryDetailMapper">
  <resultMap id="BaseResultMap" type="com.xjrsoft.module.zy.form.pojo.entity.XjrDictionaryDetail">
    <!--@mbg.generated-->
    <!--@Table XJRSOFT.XJR_DICTIONARY_DETAIL-->
    <id column="ID" jdbcType="DECIMAL" property="id" />
    <result column="NAME" jdbcType="VARCHAR" property="name" />
    <result column="CODE" jdbcType="VARCHAR" property="code" />
    <result column="ITEM_ID" jdbcType="DECIMAL" property="itemId" />
    <result column="VALUE" jdbcType="VARCHAR" property="value" />
    <result column="SORT_CODE" jdbcType="DECIMAL" property="sortCode" />
    <result column="REMARK" jdbcType="VARCHAR" property="remark" />
    <result column="TENANT_ID" jdbcType="DECIMAL" property="tenantId" />
    <result column="CREATE_DATE" jdbcType="TIMESTAMP" property="createDate" />
    <result column="CREATE_USER_ID" jdbcType="DECIMAL" property="createUserId" />
    <result column="MODIFY_DATE" jdbcType="TIMESTAMP" property="modifyDate" />
    <result column="MODIFY_USER_ID" jdbcType="DECIMAL" property="modifyUserId" />
    <result column="DELETE_MARK" jdbcType="DECIMAL" property="deleteMark" />
    <result column="ENABLED_MARK" jdbcType="DECIMAL" property="enabledMark" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, "NAME", CODE, ITEM_ID, "VALUE", SORT_CODE, REMARK, TENANT_ID, CREATE_DATE, CREATE_USER_ID, 
    MODIFY_DATE, MODIFY_USER_ID, DELETE_MARK, ENABLED_MARK
  </sql>
</mapper>