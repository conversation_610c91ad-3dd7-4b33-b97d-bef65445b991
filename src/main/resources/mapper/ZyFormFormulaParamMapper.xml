<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xjrsoft.module.zy.form.mapper.ZyFormFormulaParamMapper">
  <resultMap id="BaseResultMap" type="com.xjrsoft.module.zy.form.pojo.entity.ZyFormFormulaParam">
    <!--@mbg.generated-->
    <!--@Table XJRSOFT.ZY_FORM_FORMULA_PARAM-->
    <result column="ID" jdbcType="VARCHAR" property="id" />
    <result column="CREATE_DATE" jdbcType="TIMESTAMP" property="createDate" />
    <result column="CREATE_BY" jdbcType="VARCHAR" property="createBy" />
    <result column="UPDATE_DATE" jdbcType="TIMESTAMP" property="updateDate" />
    <result column="UPDATE_BY" jdbcType="VARCHAR" property="updateBy" />
    <result column="REMARKS" jdbcType="VARCHAR" property="remarks" />
    <result column="DEL_FLAG" jdbcType="CHAR" property="delFlag" />
    <result column="FORMULA_ID" jdbcType="VARCHAR" property="formulaId" />
    <result column="NAME" jdbcType="VARCHAR" property="name" />
    <result column="TYPE" jdbcType="VARCHAR" property="type" />
    <result column="SYS_VALUE" jdbcType="VARCHAR" property="sysValue" />
    <result column="FORM_VALUE" jdbcType="VARCHAR" property="formValue" />
    <result column="SORT" jdbcType="DECIMAL" property="sort" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, CREATE_DATE, CREATE_BY, UPDATE_DATE, UPDATE_BY, REMARKS, DEL_FLAG, FORMULA_ID,
    "NAME", "TYPE", SYS_VALUE, FORM_VALUE, SORT
  </sql>
</mapper>
