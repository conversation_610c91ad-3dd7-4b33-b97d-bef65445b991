<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xjrsoft.module.zy.form.mapper.SysTemplateMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.xjrsoft.module.zy.form.pojo.entity.SysTemplate">
        <id column="ID" property="id" />
        <result column="NAME" property="name" />
        <result column="CONTENT" property="content" />
        <result column="NAME_ALIAS" property="nameAlias" />
        <result column="CREATE_BY" property="createBy" />
        <result column="CREATE_DATE" property="createDate" />
        <result column="UPDATE_BY" property="updateBy" />
        <result column="UPDATE_DATE" property="updateDate" />
        <result column="REMARKS" property="remarks" />
        <result column="DEL_FLAG" property="delFlag" />
        <result column="MODULE" property="module" />
        <result column="REPORT_ID" property="reportId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, NAME, CONTENT, NAME_ALIAS, CREATE_BY, CREATE_DATE, UPDATE_BY, UPDATE_DATE, REMARKS, DEL_FLAG, MODULE, REPORT_ID
    </sql>

</mapper>
