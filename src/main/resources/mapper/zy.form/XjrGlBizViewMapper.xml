<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xjrsoft.module.zy.form.mapper.XjrGlBizViewMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.xjrsoft.module.zy.form.pojo.entity.XjrGlBizView">
        <result column="CREATE_DATE" property="createDate" />
        <result column="NAME" property="name" />
        <result column="SORT_CODE" property="sortCode" />
        <result column="REMARK" property="remark" />
        <result column="CREATE_USER_ID" property="createUserId" />
        <result column="MODIFY_DATE" property="modifyDate" />
        <result column="MODIFY_USER_ID" property="modifyUserId" />
        <result column="DELETE_MARK" property="deleteMark" />
        <result column="ENABLED_MARK" property="enabledMark" />
        <result column="ID" property="id" />
        <result column="BIZ_ID" property="bizId" />
        <result column="VIEW_ID" property="viewId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        CREATE_DATE,
        NAME, SORT_CODE, REMARK, CREATE_USER_ID, MODIFY_DATE, MODIFY_USER_ID, DELETE_MARK, ENABLED_MARK, ID, BIZ_ID, VIEW_ID
    </sql>

</mapper>
