<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xjrsoft.module.zy.form.mapper.SysOfficeMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.xjrsoft.module.zy.form.pojo.entity.SysOffice">
        <id column="ID" property="id" />
        <result column="CREATE_BY" property="createBy" />
        <result column="CREATE_DATE" property="createDate" />
        <result column="UPDATE_BY" property="updateBy" />
        <result column="UPDATE_DATE" property="updateDate" />
        <result column="DEL_FLAG" property="delFlag" />
        <result column="PARENT_ID" property="parentId" />
        <result column="PARENT_IDS" property="parentIds" />
        <result column="NAME" property="name" />
        <result column="SORT" property="sort" />
        <result column="AREA_ID" property="areaId" />
        <result column="CODE" property="code" />
        <result column="TYPE" property="type" />
        <result column="GRADE" property="grade" />
        <result column="ADDRESS" property="address" />
        <result column="ZIP_CODE" property="zipCode" />
        <result column="MASTER" property="master" />
        <result column="PHONE" property="phone" />
        <result column="FAX" property="fax" />
        <result column="EMAIL" property="email" />
        <result column="USEABLE" property="useable" />
        <result column="PRIMARY_PERSON" property="primaryPerson" />
        <result column="DEPUTY_PERSON" property="deputyPerson" />
        <result column="REMARKS" property="remarks" />
        <result column="DEPUTY_LEADER" property="deputyLeader" />
        <result column="DEPUTY_SUB_LEADER" property="deputySubLeader" />
        <result column="YEAR" property="year" />
        <result column="AGENCY_TYPE" property="agencyType" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        CREATE_BY,
        CREATE_DATE,
        UPDATE_BY,
        UPDATE_DATE,
        DEL_FLAG,
        ID, PARENT_ID, PARENT_IDS, NAME, SORT, AREA_ID, CODE, TYPE, GRADE, ADDRESS, ZIP_CODE, MASTER, PHONE, FAX, EMAIL, USEABLE, PRIMARY_PERSON, DEPUTY_PERSON, REMARKS, DEPUTY_LEADER, DEPUTY_SUB_LEADER, YEAR, AGENCY_TYPE
    </sql>

</mapper>
