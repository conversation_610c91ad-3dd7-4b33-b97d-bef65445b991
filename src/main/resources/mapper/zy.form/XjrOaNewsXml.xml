<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xjrsoft.module.oa.mapper.NewsMapper">
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.xjrsoft.module.oa.entity.News">
        <id column="ID" property="id"/>
        <result column="TYPE_ID" property="typeId"/>
        <result column="CATEGORY_ID" property="categoryId"/>
        <result column="CATEGORY" property="category"/>
        <result column="FULL_HEAD" property="fullHead"/>
        <result column="FULL_HEAD_COLOR" property="fullHeadColor"/>
        <result column="BRIEF_HEAD" property="briefHead"/>
        <result column="AUTHOR_NAME" property="authorName"/>
        <result column="COMPILE_NAME" property="compileName"/>
        <result column="TAG_WORD" property="tagWord"/>
        <result column="KEYWORD" property="keyword"/>
        <result column="SOURCE_NAME" property="sourceName"/>
        <result column="SOURCE_ADDRESS" property="sourceAddress"/>
        <result column="NEWS_CONTENT" property="newsContent"/>
        <result column="PV" property="pv"/>
        <result column="RELEASE_TIME" property="releaseTime"/>
        <result column="SORT_CODE" property="sortCode"/>
        <result column="REMARK" property="remark"/>
        <result column="CREATE_DATE" property="createDate"/>
        <result column="CREATE_USER_ID" property="createUserId"/>
        <result column="MODIFY_DATE" property="modifyDate"/>
        <result column="MODIFY_USER_ID" property="modifyUserId"/>
        <result column="DELETE_MARK" property="deleteMark"/>
        <result column="ENABLED_MARK" property="enabledMark"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID,
        TYPE_ID,
        CATEGORY_ID,
        CATEGORY,
        FULL_HEAD,
        FULL_HEAD_COLOR,
        BRIEF_HEAD,
        AUTHOR_NAME,
        COMPILE_NAME,
        TAG_WORD,
        KEYWORD,
        SOURCE_NAME,
        SOURCE_ADDRESS,
        NEWS_CONTENT,
        PV,
        RELEASE_TIME,
        SORT_CODE,
        REMARK,
        CREATE_DATE,
        CREATE_USER_ID,
        MODIFY_DATE,
        MODIFY_USER_ID,
        DELETE_MARK,
        ENABLED_MARK
    </sql>

    <select id="pageNews" resultMap="BaseResultMap">
        select news.*
        from xjr_oa_news news
        <where>
            <if test="reqVO.type != null and reqVO.type != ''">
                and news.TYPE_ID = #{reqVO.type}
            </if>
            <if test="reqVO.yd != null">
                and exists(select 1
                           from ZY_NEW_STATUS read
                           where read.ywid = news.id and read.create_by = #{reqVO.userId})
            </if>
            <if test="reqVO.wd != null">
                and not exists(select 1
                               from ZY_NEW_STATUS read
                               where read.ywid = news.id and read.create_by = #{reqVO.userId})
            </if>
        </where>
    </select>
</mapper>
