<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xjrsoft.module.zy.form.mapper.XjrUserGzhjMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.xjrsoft.module.zy.form.pojo.entity.XjrUserGzhj">
        <id column="ID" property="id" />
        <result column="CREATE_DATE" property="createDate" />
        <result column="NAME" property="name" />
        <result column="CODE" property="code" />
        <result column="REMARK" property="remark" />
        <result column="CREATE_USER_ID" property="createUserId" />
        <result column="MODIFY_DATE" property="modifyDate" />
        <result column="MODIFY_USER_ID" property="modifyUserId" />
        <result column="DELETE_MARK" property="deleteMark" />
        <result column="ENABLED_MARK" property="enabledMark" />
        <result column="DW" property="dw" />
        <result column="BM" property="bm" />
        <result column="USER_NAME" property="userName" />
        <result column="USER_ID" property="userId" />
        <result column="BM_CODE" property="bmCode" />
        <result column="DW_CODE" property="dwCode" />
        <result column="YWRQ" property="ywrq" />
        <result column="ND" property="nd" />
        <result column="QJ" property="qj" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        CREATE_DATE,
        NAME, CODE, REMARK, CREATE_USER_ID, MODIFY_DATE, MODIFY_USER_ID, DELETE_MARK, ENABLED_MARK, DW, BM, ID, USER_NAME, USER_ID, BM_CODE, DW_CODE, YWRQ, ND, QJ
    </sql>

</mapper>
