<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xjrsoft.module.zy.form.mapper.SysReportConfigMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.xjrsoft.module.zy.form.pojo.entity.SysReportConfig">
        <id column="ID" property="id" />
        <result column="CREATE_BY" property="createBy" />
        <result column="CREATE_DATE" property="createDate" />
        <result column="UPDATE_BY" property="updateBy" />
        <result column="UPDATE_DATE" property="updateDate" />
        <result column="NAME" property="name" />
        <result column="MODEL" property="model" />
        <result column="DATASOURCE_ID" property="datasourceId" />
        <result column="REMARKS" property="remarks" />
        <result column="IS_DEFAULT_LOAD" property="isDefaultLoad" />
        <result column="IS_SHOW_GLOBAL_LIKE" property="isShowGlobalLike" />
        <result column="IS_SHOW_BTN" property="isShowBtn" />
        <result column="DBL_CLICK_URL" property="dblClickUrl" />
        <result column="SHOW_TYPE" property="showType" />
        <result column="WIDTH" property="width" />
        <result column="HEIGHT" property="height" />
        <result column="MULTI_QUERY_LENGTH" property="multiQueryLength" />
        <result column="FORZEN_LENGTH" property="forzenLength" />
        <result column="EXTERNAL_URL" property="externalUrl" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        CREATE_BY,
        CREATE_DATE,
        UPDATE_BY,
        UPDATE_DATE,
        ID, NAME, MODEL, DATASOURCE_ID, REMARKS, IS_DEFAULT_LOAD, IS_SHOW_GLOBAL_LIKE, IS_SHOW_BTN, DBL_CLICK_URL, SHOW_TYPE, WIDTH, HEIGHT, MULTI_QUERY_LENGTH, FORZEN_LENGTH, EXTERNAL_URL
    </sql>

</mapper>
