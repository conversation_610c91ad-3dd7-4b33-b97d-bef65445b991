<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xjrsoft.module.zy.form.mapper.ZyQqLogMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.xjrsoft.module.zy.form.pojo.entity.ZyQqLog">
        <result column="CREATE_DATE" property="createDate" />
        <result column="CREATE_BY" property="createBy" />
        <result column="UPDATE_DATE" property="updateDate" />
        <result column="UPDATE_BY" property="updateBy" />
        <result column="DEL_FLAG" property="delFlag" />
        <result column="ID" property="id" />
        <result column="BODY" property="body" />
        <result column="PARAM" property="param" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        CREATE_DATE,
        CREATE_BY,
        UPDATE_DATE,
        UPDATE_BY,
        DEL_FLAG,
        ID, BODY, PARAM
    </sql>

</mapper>
