<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xjrsoft.module.zy.form.mapper.SysBillPrintDsMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.xjrsoft.module.zy.form.pojo.entity.SysBillPrintDs">
        <id column="ID" property="id" />
        <result column="CREATE_BY" property="createBy" />
        <result column="CREATE_DATE" property="createDate" />
        <result column="UPDATE_BY" property="updateBy" />
        <result column="UPDATE_DATE" property="updateDate" />
        <result column="DEL_FLAG" property="delFlag" />
        <result column="PAGE_ID" property="pageId" />
        <result column="NAME" property="name" />
        <result column="PRIMARY_TABLE" property="primaryTable" />
        <result column="IS_USE" property="isUse" />
        <result column="REMARKS" property="remarks" />
        <result column="ALIAS_NAME" property="aliasName" />
        <result column="PRIMARY_FIELD" property="primaryField" />
        <result column="F_SQL" property="fSql" />
        <result column="IS_USE_COMMENT" property="isUseComment" />
        <result column="TABLE_SQL" property="tableSql" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        CREATE_BY,
        CREATE_DATE,
        UPDATE_BY,
        UPDATE_DATE,
        DEL_FLAG,
        PAGE_ID, ID, NAME, PRIMARY_TABLE, IS_USE, REMARKS, ALIAS_NAME, PRIMARY_FIELD, F_SQL, IS_USE_COMMENT, TABLE_SQL
    </sql>

</mapper>
