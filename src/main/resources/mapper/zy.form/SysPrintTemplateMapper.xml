<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xjrsoft.module.zy.form.mapper.SysPrintTemplateMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.xjrsoft.module.zy.form.pojo.entity.SysPrintTemplate">
        <id column="ID" property="id" />
        <result column="CREATE_BY" property="createBy" />
        <result column="CREATE_DATE" property="createDate" />
        <result column="UPDATE_BY" property="updateBy" />
        <result column="UPDATE_DATE" property="updateDate" />
        <result column="DEL_FLAG" property="delFlag" />
        <result column="NAME" property="name" />
        <result column="TYPE" property="type" />
        <result column="MODULE" property="module" />
        <result column="PAGE_ID" property="pageId" />
        <result column="SVCOCODE" property="svcocode" />
        <result column="IS_DEFAULT" property="isDefault" />
        <result column="REMARKS" property="remarks" />
        <result column="ATTR" property="attr" />
        <result column="FILE_PATH" property="filePath" />
        <result column="DATASET_ID" property="datasetId" />
        <result column="INIT_SCRIPT" property="initScript" />
        <result column="USE_BY" property="useBy" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        CREATE_BY,
        CREATE_DATE,
        UPDATE_BY,
        UPDATE_DATE,
        DEL_FLAG,
        ID, NAME, TYPE, MODULE, PAGE_ID, SVCOCODE, IS_DEFAULT, REMARKS, ATTR, FILE_PATH, DATASET_ID, INIT_SCRIPT, USE_BY
    </sql>

</mapper>
