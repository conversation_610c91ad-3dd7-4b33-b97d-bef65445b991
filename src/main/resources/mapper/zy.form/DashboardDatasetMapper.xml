<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xjrsoft.module.zy.form.mapper.DashboardDatasetMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.xjrsoft.module.zy.form.pojo.entity.DashboardDataset">
        <result column="CREATE_BY" property="createBy" />
        <result column="CREATE_DATE" property="createDate" />
        <result column="UPDATE_BY" property="updateBy" />
        <result column="UPDATE_DATE" property="updateDate" />
        <result column="DEL_FLAG" property="delFlag" />
        <result column="ID" property="id" />
        <result column="CATEGORY_NAME" property="categoryName" />
        <result column="DATASET_NAME" property="datasetName" />
        <result column="DATA_JSON" property="dataJson" />
        <result column="REMARKS" property="remarks" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        CREATE_BY,
        CREATE_DATE,
        UPDATE_BY,
        UPDATE_DATE,
        DEL_FLAG,
        ID, CATEGORY_NAME, DATASET_NAME, DATA_JSON, REMARKS
    </sql>

</mapper>
