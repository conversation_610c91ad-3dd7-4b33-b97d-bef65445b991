<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xjrsoft.module.zy.form.mapper.SysBillPrintDsDetailMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.xjrsoft.module.zy.form.pojo.entity.SysBillPrintDsDetail">
        <id column="ID" property="id" />
        <result column="CREATE_BY" property="createBy" />
        <result column="CREATE_DATE" property="createDate" />
        <result column="UPDATE_BY" property="updateBy" />
        <result column="UPDATE_DATE" property="updateDate" />
        <result column="DEL_FLAG" property="delFlag" />
        <result column="BILL_PRINT_ID" property="billPrintId" />
        <result column="PRIMARY_FIELD" property="primaryField" />
        <result column="FOREIGN_TABLE" property="foreignTable" />
        <result column="FOREIGN_FIELD" property="foreignField" />
        <result column="REMARKS" property="remarks" />
        <result column="ALIAS_NAME" property="aliasName" />
        <result column="RECORD_SIZE" property="recordSize" />
        <result column="F_SQL" property="fSql" />
        <result column="TABLE_SQL" property="tableSql" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        CREATE_BY,
        CREATE_DATE,
        UPDATE_BY,
        UPDATE_DATE,
        DEL_FLAG,
        ID, BILL_PRINT_ID, PRIMARY_FIELD, FOREIGN_TABLE, FOREIGN_FIELD, REMARKS, ALIAS_NAME, RECORD_SIZE, F_SQL, TABLE_SQL
    </sql>

</mapper>
