<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xjrsoft.module.im.mapper.ImGroupMemberMapper">
  <resultMap id="BaseResultMap" type="com.xjrsoft.module.im.entity.ImGroupMember">
    <!--@mbg.generated-->
    <!--@Table im_group_member-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="group_id" jdbcType="BIGINT" property="groupId" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="alias_name" jdbcType="VARCHAR" property="aliasName" />
    <result column="head_image" jdbcType="VARCHAR" property="headImage" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="quit" jdbcType="BOOLEAN" property="quit" />
    <result column="quit_time" jdbcType="TIMESTAMP" property="quitTime" />
    <result column="created_time" jdbcType="TIMESTAMP" property="createdTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, group_id, user_id, alias_name, head_image, remark, quit, quit_time, created_time
  </sql>
</mapper>