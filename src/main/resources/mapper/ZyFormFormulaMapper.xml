<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xjrsoft.module.zy.form.mapper.ZyFormFormulaMapper">
  <resultMap id="BaseResultMap" type="com.xjrsoft.module.zy.form.pojo.entity.ZyFormFormula">
    <!--@mbg.generated-->
    <!--@Table XJRSOFT.ZY_FORM_FORMULA-->
    <result column="ID" jdbcType="VARCHAR" property="id" />
    <result column="CREATE_DATE" jdbcType="TIMESTAMP" property="createDate" />
    <result column="CREATE_BY" jdbcType="VARCHAR" property="createBy" />
    <result column="UPDATE_DATE" jdbcType="TIMESTAMP" property="updateDate" />
    <result column="UPDATE_BY" jdbcType="VARCHAR" property="updateBy" />
    <result column="REMARKS" jdbcType="VARCHAR" property="remarks" />
    <result column="DEL_FLAG" jdbcType="CHAR" property="delFlag" />
    <result column="SCHEME_ID" jdbcType="VARCHAR" property="schemeId" />
    <result column="NAME" jdbcType="VARCHAR" property="name" />
    <result column="TYPE" jdbcType="VARCHAR" property="type" />
    <result column="HANDLE" jdbcType="VARCHAR" property="handle" />
    <result column="DESCRIPTION" jdbcType="VARCHAR" property="description" />
    <result column="PROC_NAME" jdbcType="VARCHAR" property="procName" />
    <result column="EXPRESSION_SOURCE" jdbcType="VARCHAR" property="expressionSource" />
    <result column="EXPRESSION_COMPILE" jdbcType="VARCHAR" property="expressionCompile" />
    <result column="MESSAGE" jdbcType="VARCHAR" property="message" />
    <result column="SUBTABLE" jdbcType="VARCHAR" property="subtable" />
    <result column="DATASOURCEID" jdbcType="VARCHAR" property="datasourceid" />
    <result column="SCRIPT" jdbcType="CLOB" property="script" />
    <result column="EXT_PARAM" jdbcType="CLOB" property="extParam" />
    <result column="USERS" jdbcType="VARCHAR" property="users" />
    <result column="OFFICES" jdbcType="VARCHAR" property="offices" />
    <result column="ROLES" jdbcType="VARCHAR" property="roles" />
    <result column="PLATFORM" jdbcType="CHAR" property="platform" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, CREATE_DATE, CREATE_BY, UPDATE_DATE, UPDATE_BY, REMARKS, DEL_FLAG, SCHEME_ID,
    "NAME", "TYPE", HANDLE, DESCRIPTION, PROC_NAME, EXPRESSION_SOURCE, EXPRESSION_COMPILE,
    MESSAGE, SUBTABLE, DATASOURCEID, SCRIPT, EXT_PARAM, USERS, OFFICES, ROLES, PLATFORM
  </sql>
</mapper>
