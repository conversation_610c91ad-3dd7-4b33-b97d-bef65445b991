<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xjrsoft.module.zy.form.mapper.ZyFormAttachmentMapper">
  <resultMap id="BaseResultMap" type="com.xjrsoft.module.zy.form.pojo.entity.ZyFormAttachment">
    <!--@mbg.generated-->
    <!--@Table XJRSOFT.ZY_FORM_ATTACHMENT-->
    <result column="FOLDER_ID" jdbcType="VARCHAR" property="folderId" />
    <result column="FILE_ID" jdbcType="VARCHAR" property="fileId" />
    <result column="FILE_NAME" jdbcType="VARCHAR" property="fileName" />
    <result column="FILE_PATH" jdbcType="VARCHAR" property="filePath" />
    <result column="FILE_TYPE" jdbcType="VARCHAR" property="fileType" />
    <result column="FILE_STATUS" jdbcType="VARCHAR" property="fileStatus" />
    <result column="FILE_SIZE" jdbcType="DECIMAL" property="fileSize" />
    <result column="CREATE_BY" jdbcType="VARCHAR" property="createBy" />
    <result column="CREATE_DATE" jdbcType="TIMESTAMP" property="createDate" />
    <result column="FILE_CATEGORY" jdbcType="VARCHAR" property="fileCategory" />
    <result column="REMARKS" jdbcType="VARCHAR" property="remarks" />
    <result column="FILTER_VALUE" jdbcType="VARCHAR" property="filterValue" />
    <result column="COMPANY_CODE" jdbcType="VARCHAR" property="companyCode" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    FOLDER_ID, FILE_ID, FILE_NAME, FILE_PATH, FILE_TYPE, FILE_STATUS, FILE_SIZE, CREATE_BY, 
    CREATE_DATE, FILE_CATEGORY, REMARKS, FILTER_VALUE, COMPANY_CODE
  </sql>
</mapper>