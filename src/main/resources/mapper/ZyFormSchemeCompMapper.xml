<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xjrsoft.module.zy.form.mapper.ZyFormSchemeCompMapper">
  <resultMap id="BaseResultMap" type="com.xjrsoft.module.zy.form.pojo.entity.ZyFormSchemeComp">
    <!--@mbg.generated-->
    <!--@Table XJRSOFT.ZY_FORM_SCHEME_COMP-->
    <result column="SCHEME_ID" jdbcType="VARCHAR" property="schemeId" />
    <result column="COMPO_ID" jdbcType="VARCHAR" property="compoId" />
    <result column="VERSION" jdbcType="DECIMAL" property="version" />
    <result column="TABLE_NAME" jdbcType="VARCHAR" property="tableName" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    SCHEME_ID, COMPO_ID, VERSION, "TABLE_NAME"
  </sql>
</mapper>