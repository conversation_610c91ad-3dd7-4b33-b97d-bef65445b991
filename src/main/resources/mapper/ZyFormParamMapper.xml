<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xjrsoft.module.zy.form.mapper.ZyFormParamMapper">
  <resultMap id="BaseResultMap" type="com.xjrsoft.module.zy.form.pojo.entity.ZyFormParam">
    <!--@mbg.generated-->
    <!--@Table XJRSOFT.ZY_FORM_PARAM-->
    <result column="ID" jdbcType="VARCHAR" property="id" />
    <result column="NAME" jdbcType="VARCHAR" property="name" />
    <result column="CREATE_DATE" jdbcType="TIMESTAMP" property="createDate" />
    <result column="CREATE_BY" jdbcType="VARCHAR" property="createBy" />
    <result column="UPDATE_DATE" jdbcType="TIMESTAMP" property="updateDate" />
    <result column="UPDATE_BY" jdbcType="VARCHAR" property="updateBy" />
    <result column="REMARKS" jdbcType="VARCHAR" property="remarks" />
    <result column="SHORT_VALUE" jdbcType="VARCHAR" property="shortValue" />
    <result column="SHORT_DESC" jdbcType="VARCHAR" property="shortDesc" />
    <result column="DEL_FLAG" jdbcType="CHAR" property="delFlag" />
    <result column="VALUE" jdbcType="CLOB" property="value" />
    <result column="IS_USED" jdbcType="CHAR" property="isUsed" />
    <result column="TYPE" jdbcType="VARCHAR" property="type" />
    <result column="FORM_ID" jdbcType="CLOB" property="formId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, "NAME", CREATE_DATE, CREATE_BY, UPDATE_DATE, UPDATE_BY, REMARKS, SHORT_VALUE, 
    SHORT_DESC, DEL_FLAG, "VALUE", IS_USED, "TYPE", FORM_ID
  </sql>
</mapper>