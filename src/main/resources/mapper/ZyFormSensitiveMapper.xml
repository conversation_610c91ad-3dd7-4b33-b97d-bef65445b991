<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xjrsoft.module.zy.form.mapper.ZyFormSensitiveMapper">
  <resultMap id="BaseResultMap" type="com.xjrsoft.module.zy.form.pojo.entity.ZyFormSensitive">
    <!--@mbg.generated-->
    <!--@Table XJRSOFT.ZY_FORM_SENSITIVE-->
    <result column="ID" jdbcType="VARCHAR" property="id" />
    <result column="NAME" jdbcType="VARCHAR" property="name" />
    <result column="CREATE_DATE" jdbcType="TIMESTAMP" property="createDate" />
    <result column="CREATE_BY" jdbcType="VARCHAR" property="createBy" />
    <result column="UPDATE_DATE" jdbcType="TIMESTAMP" property="updateDate" />
    <result column="UPDATE_BY" jdbcType="VARCHAR" property="updateBy" />
    <result column="REMARKS" jdbcType="VARCHAR" property="remarks" />
    <result column="DEL_FLAG" jdbcType="CHAR" property="delFlag" />
    <result column="TABLEPARAMS" jdbcType="CLOB" property="tableparams" />
    <result column="DATASOURCEPARAMS" jdbcType="CLOB" property="datasourceparams" />
    <result column="AUTH" jdbcType="CLOB" property="auth" />
    <result column="HANDLER_TYPE" jdbcType="VARCHAR" property="handlerType" />
    <result column="IS_USED" jdbcType="CHAR" property="isUsed" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, "NAME", CREATE_DATE, CREATE_BY, UPDATE_DATE, UPDATE_BY, REMARKS, DEL_FLAG, TABLEPARAMS, 
    DATASOURCEPARAMS, AUTH, HANDLER_TYPE, IS_USED
  </sql>
</mapper>