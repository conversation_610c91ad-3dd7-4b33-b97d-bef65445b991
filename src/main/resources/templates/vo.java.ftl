package ${package};

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalTime;
import java.time.LocalDateTime;
import java.math.BigDecimal;
import java.util.List;
<#--子表遍历-->
<#if childTables?? >
    <#list childTables as childTable>
import com.xjrsoft.module.${outputArea}.entity.${childTable.tableName?cap_first};
    </#list>
</#if>

/**
* @title: ${tableComment}
* <AUTHOR>
* @Date: ${date}
* @Version 1.0
*/
@Data
public class ${entityClass}Vo {

<#--属性遍历-->
<#list fields as field>
    /**
    * ${(field.fieldComment)!''}
    */
    @ApiModelProperty("${(field.fieldComment)!''}")
    private ${field.fieldType} ${field.fieldName};
</#list>


<#--子表遍历-->
<#if childTables?? >
<#list childTables as childTable>
    /**
    * ${childTable.tableName}
    */
    @ApiModelProperty("${childTable.tableName}子表")
    private List<${childTable.tableName?cap_first}Vo> ${childTable.tableName}List;
</#list>
</#if>

}
