package ${package};

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import com.github.yulichang.annotation.EntityMapping;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.io.Serializable;
import java.time.LocalTime;
import java.time.LocalDateTime;
import java.math.BigDecimal;
import java.util.List;


/**
* @title: ${tableComment}
* <AUTHOR>
* @Date: ${date}
* @Version 1.0
*/
@Data
@TableName("${tableName}")
@ApiModel(value = "${tableComment}对象", description = "${tableComment}")
public class ${entityClass} implements Serializable {

    private static final long serialVersionUID = 1L;

<#--属性遍历-->
<#list fields as field>
    /**
    * ${(field.fieldComment)!''}
    */
    @ApiModelProperty("${(field.fieldComment)!''}")
    <#if field.autoInsert?? >
    @TableField(fill = FieldFill.INSERT)
    </#if>
    <#if field.autoUpdate?? >
    @TableField(fill = FieldFill.UPDATE)
    </#if>
    <#if field.deleteMark?? >
    @TableLogic
    </#if>
    <#if field.pk>
    @TableId
    </#if>
    private ${field.fieldType} ${field.fieldName};
</#list>

<#--子表遍历-->
<#if childTables?? >
<#list childTables as childTable>
    /**
    * ${childTable.tableName}
    */
    @ApiModelProperty("${childTable.tableName}子表")
    @TableField(exist = false)
    @EntityMapping(thisField = "${childTable.relationTableField}", joinField = "${childTable.relationField}")
    private List<${childTable.tableName?cap_first}> ${childTable.tableName}List;
</#list>
</#if>

}