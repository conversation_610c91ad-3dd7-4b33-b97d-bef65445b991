package ${package};

<#if isImport || isExport>
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.write.style.ContentStyle;
</#if>
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import com.xjrsoft.common.annotation.Trans;
import com.xjrsoft.common.enums.TransType;
import java.time.LocalTime;
import java.time.LocalDateTime;
import java.math.BigDecimal;

/**
* @title: ${tableComment}
* <AUTHOR>
* @Date: ${date}
* @Version 1.0
*/
@Data
public class ${entityClass}${isPage?string("Page","List")}Vo {

<#--属性遍历-->
<#list fields as field>
    /**
    * ${(field.fieldComment)!''}
    */
    <#if !(field.pk || field.fieldName == "ruleUserId")>
    <#if isImport || isExport>
<#--    所有单元格设置成文本格式-->
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("${field.label}<#if field.required>${requiredSuffix}</#if>")
    </#if>
    <#else>
    <#if isImport || isExport>
    @ExcelIgnore
    </#if>
    </#if>
    @ApiModelProperty("${(field.fieldComment)!''}")
    <#if field.fieldType == "LocalDateTime"  && field.pattern??>
    @JsonFormat(pattern = "${field.pattern}")
    </#if>
    <#if field.datasourceType??>
        <#assign multi = "">
        <#if field.multi><#assign multi = ", isMulti = true"></#if>
        <#if field.datasourceType = "dic">
    @Trans(type = TransType.DIC, id = "${field.datasourceId}"${multi})
        </#if>
        <#if field.datasourceType = "api">
    @Trans(type = TransType.API, id = "${field.datasourceId}"${multi})
        </#if>
    </#if>
    <#if field.componentType??>
        <#if field.componentType = "user">
    @Trans(type = TransType.USER)
        </#if>
        <#if field.componentType = "organization">
    @Trans(type = TransType.DEPT)
        </#if>
        <#if field.componentType = "area">
    @Trans(type = TransType.AREA)
        </#if>
        <#if field.componentType = "cascader">
    @Trans(type = TransType.CASCADE, id = "${field.datasourceId}", separator = "${field.separator}", showFormat = "${field.showFormat}")
        </#if>
    </#if>
    private ${field.fieldType} ${field.fieldName};
</#list>

}
