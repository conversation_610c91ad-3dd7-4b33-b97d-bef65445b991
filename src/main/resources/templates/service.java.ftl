package ${package};

import com.baomidou.mybatisplus.extension.service.IService;
import com.github.yulichang.base.MPJBaseService;
import com.xjrsoft.module.${outputArea}.entity.${entityClass};
import lombok.Data;
import java.util.List;

/**
* @title: ${tableComment}
* <AUTHOR>
* @Date: ${date}
* @Version 1.0
*/

public interface I${className}Service extends ${isMulti?string("MPJBaseService","IService")}<${entityClass}> {
<#if isMulti>
    /**
    * 新增
    *
    * @param ${entityClass?uncap_first}
    * @return
    */
    Boolean add(${entityClass} ${entityClass?uncap_first});

    /**
    * 更新
    *
    * @param ${entityClass?uncap_first}
    * @return
    */
    Boolean update(${entityClass} ${entityClass?uncap_first});

    /**
    * 删除
    *
    * @param ids
    * @return
    */
    Boolean delete(List<${pkType}> ids);
</#if>
}
