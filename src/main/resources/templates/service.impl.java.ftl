package ${package};

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.yulichang.base.MPJBaseServiceImpl;
<#if isMulti>
        <#--子表遍历-->
    <#list childTables as childTable>
import com.xjrsoft.module.${outputArea}.entity.${childTable.tableName?cap_first};
import com.xjrsoft.module.${outputArea}.mapper.${childTable.tableName?cap_first}Mapper;
    </#list>
</#if>
import com.xjrsoft.module.${outputArea}.entity.${entityClass};
import com.xjrsoft.module.${outputArea}.mapper.${entityClass}Mapper;
import com.xjrsoft.module.${outputArea}.service.I${className}Service;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
<#if databaseId?? && databaseId != "master">
import com.baomidou.dynamic.datasource.annotation.DS;
</#if>
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;

/**
* @title: ${tableComment}
* <AUTHOR>
* @Date: ${date}
* @Version 1.0
*/
@Service
@AllArgsConstructor
<#if databaseId?? && databaseId != "master">
@DS("${databaseId}")
</#if>
public class ${className}ServiceImpl extends ${isMulti?string("MPJBaseService","Service")}Impl<${entityClass}Mapper, ${entityClass}> implements I${className}Service {
<#if isMulti>
    private final ${entityClass}Mapper ${className?uncap_first}${entityClass}Mapper;

    <#--子表遍历-->
    <#list childTables as childTable>
    private final ${childTable.tableName?cap_first}Mapper ${className?uncap_first}${childTable.tableName?cap_first}Mapper;
    </#list>


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean add(${entityClass} ${entityClass?uncap_first}) {
        ${className?uncap_first}${entityClass}Mapper.insert(${entityClass?uncap_first});
    <#--子表遍历-->
    <#list childTables as childTable>
        for (${childTable.tableName?cap_first} ${childTable.tableName} : ${entityClass?uncap_first}.get${childTable.tableName?cap_first}List()) {
            ${childTable.tableName}.set${childTable.relationField?cap_first}(${entityClass?uncap_first}.get${childTable.relationTableField?cap_first}());
            ${className?uncap_first}${childTable.tableName?cap_first}Mapper.insert(${childTable.tableName});
        }
    </#list>

        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean update(${entityClass} ${entityClass?uncap_first}) {
        ${className?uncap_first}${entityClass}Mapper.updateById(${entityClass?uncap_first});
    <#--子表遍历-->
    <#list childTables as childTable>
        //********************************* ${childTable.tableName?cap_first}  增删改  开始 *******************************************/
        {
            // 查出所有子级的id
            List<${childTable.tableName?cap_first}> ${childTable.tableName}List = ${className?uncap_first}${childTable.tableName?cap_first}Mapper.selectList(Wrappers.lambdaQuery(${childTable.tableName?cap_first}.class).eq(${childTable.tableName?cap_first}::get${childTable.relationField?cap_first}, ${entityClass?uncap_first}.get${childTable.relationTableField?cap_first}()).select(${childTable.tableName?cap_first}::get${childTable.pkField?cap_first}));
            List<${childTable.pkType}> ${childTable.tableName}Ids = ${childTable.tableName}List.stream().map(${childTable.tableName?cap_first}::get${childTable.pkField?cap_first}).collect(Collectors.toList());
            //原有子表单 没有被删除的主键
            List<${childTable.pkType}> ${childTable.tableName}OldIds = ${entityClass?uncap_first}.get${childTable.tableName?cap_first}List().stream().map(${childTable.tableName?cap_first}::get${childTable.pkField?cap_first}).filter(Objects::nonNull).collect(Collectors.toList());
            //找到需要删除的id
            List<${childTable.pkType}> ${childTable.tableName}RemoveIds = ${childTable.tableName}Ids.stream().filter(item -> !${childTable.tableName}OldIds.contains(item)).collect(Collectors.toList());

            for (${childTable.tableName?cap_first} ${childTable.tableName} : ${entityClass?uncap_first}.get${childTable.tableName?cap_first}List()) {
                //如果不等于空则修改
                if (${childTable.tableName}.get${childTable.pkField?cap_first}() != null) {
                    ${className?uncap_first}${childTable.tableName?cap_first}Mapper.updateById(${childTable.tableName});
                }
                //如果等于空 则新增
                else {
                    //已经不存在的id 删除
                    ${childTable.tableName}.set${childTable.relationField?cap_first}(${entityClass?uncap_first}.get${childTable.relationTableField?cap_first}());
                    ${className?uncap_first}${childTable.tableName?cap_first}Mapper.insert(${childTable.tableName});
                }
            }
            //已经不存在的id 删除
            if(${childTable.tableName}RemoveIds.size() > 0){
                ${className?uncap_first}${childTable.tableName?cap_first}Mapper.deleteBatchIds(${childTable.tableName}RemoveIds);
            }
        }
        //********************************* ${childTable.tableName?cap_first}  增删改  结束 *******************************************/

    </#list>
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean delete(List<${pkType}> ids) {
        ${className?uncap_first}${entityClass}Mapper.deleteBatchIds(ids);
    <#--子表遍历-->
    <#list childTables as childTable>
        ${className?uncap_first}${childTable.tableName?cap_first}Mapper.delete(Wrappers.lambdaQuery(${childTable.tableName?cap_first}.class).in(${childTable.tableName?cap_first}::get${childTable.relationField?cap_first}, ids));
    </#list>

        return true;
    }
</#if>
}
