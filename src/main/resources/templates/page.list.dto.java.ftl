package ${package};

${isPage?string("import com.xjrsoft.common.page.PageInput;","import com.xjrsoft.common.page.ListInput;")}
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalTime;
import java.time.LocalDateTime;
import java.math.BigDecimal;


/**
* @title: ${tableComment}
* <AUTHOR>
* @Date: ${date}
* @Version 1.0
*/
@Data
@EqualsAndHashCode(callSuper = false)
public class ${entityClass}${isPage?string("Page","List")}Dto ${isPage?string("extends PageInput","extends ListInput")} {

<#--属性遍历-->
<#list fields as field>
    /**
    * ${(field.fieldComment)!''}
    */
    @ApiModelProperty("${(field.fieldComment)!''}")
    <#if field.fieldType == "LocalDateTime" >
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    </#if>
    private ${field.fieldType} ${field.fieldName};
</#list>

}
