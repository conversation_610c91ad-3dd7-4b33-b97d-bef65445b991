package ${package};

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.io.Serializable;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.time.LocalTime;
import java.time.LocalDateTime;
import java.math.BigDecimal;
import java.util.List;
<#--子表遍历-->
<#if childTables?? >
    <#list childTables as childTable>
import com.xjrsoft.module.${outputArea}.entity.${childTable.tableName?cap_first};
    </#list>
</#if>



/**
* @title: ${tableComment}
* <AUTHOR>
* @Date: ${date}
* @Version 1.0
*/
@Data
public class Add${entityClass}Dto implements Serializable {

    private static final long serialVersionUID = 1L;

<#--属性遍历-->
<#list fields as field>
    /**
    * ${(field.fieldComment)!''}
    */
    @ApiModelProperty("${(field.fieldComment)!''}")
    <#if field.fieldType == "LocalDateTime" && field.pattern??>
    @JsonFormat(pattern = "${field.pattern}")
    </#if>
    private ${field.fieldType} ${field.fieldName};
</#list>

<#--子表遍历-->
<#if childTables?? >
<#list childTables as childTable>
    /**
    * ${childTable.tableName}
    */
    @ApiModelProperty("${childTable.tableName}子表")
    private List<Add${childTable.tableName?cap_first}Dto> ${childTable.tableName?uncap_first}List;
</#list>
</#if>
}
