package com.xjrsoft.encrypt;

/**
   *  国密证书工具类
 * <AUTHOR>
 */

import cn.com.jit.ida.util.pki.PKIException;
import cn.com.jit.ida.util.pki.Parser;
import cn.com.jit.ida.util.pki.asn1.ASN1InputStream;
import cn.com.jit.ida.util.pki.asn1.ASN1Sequence;
import cn.com.jit.ida.util.pki.asn1.ASN1Set;
import cn.com.jit.ida.util.pki.asn1.pkcs.pkcs7.ContentInfo;
import cn.com.jit.ida.util.pki.asn1.pkcs.pkcs7.IssuerAndSerialNumber;
import cn.com.jit.ida.util.pki.asn1.pkcs.pkcs7.SignedData;
import cn.com.jit.ida.util.pki.asn1.x509.SubjectKeyIdentifier;
import cn.com.jit.ida.util.pki.asn1.x509.SubjectPublicKeyInfo;
import cn.com.jit.ida.util.pki.asn1.x509.X509CertificateStructure;
import cn.com.jit.ida.util.pki.asn1.x509.X509Name;
import cn.com.jit.ida.util.pki.cert.X509Cert;
import cn.com.jit.ida.util.pki.cipher.JKey;
import cn.com.jit.ida.util.pki.encoders.Base64;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.math.BigInteger;
import java.security.cert.CertificateEncodingException;
import java.security.cert.CertificateException;
import java.security.cert.CertificateFactory;
import java.security.cert.X509Certificate;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.Iterator;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
   *  证书工具类
 * <AUTHOR>
 *
 */
public class CertUtil {

	private static final Log log = LogFactory.getLog(CertUtil.class);

	/**
	 * 将Java格式的证书转成PKITool使用的证书格式
	 *
	 * @param cert
	 *            X509Certificate对象
	 * @return 返回pki转换后的证书
	 */
	public static final X509Cert getPKICert(X509Certificate cert) {
		X509Cert pkiCert = null;

		try {
			pkiCert = new X509Cert(cert.getEncoded());
			if (log.isDebugEnabled()) {
				log.debug("[PKICert DN] : " + pkiCert.getSubject());
			}
		} catch (CertificateEncodingException e) {
			log.error("PKI[Cert] - > X509Certificate is failed!", e);
		} catch (PKIException e) {
			log.error("PKI[Cert] - > X509Certificate is failed!", e);
		}
		return pkiCert;
	}

	/**
	 * 将PKITool的证书转换成Java标准证书
	 *
	 * @param cert
	 * @return X509Certificate
	 * @throws CertificateException
	 * @throws PKIException
	 */
	public static final X509Certificate getX509Certificate(X509Cert cert) {
		byte[] encoded;
		CertificateFactory factory;
		try {
			encoded = cert.getEncoded();
			factory = CertificateFactory.getInstance("X.509");
			return (X509Certificate) factory.generateCertificate(new ByteArrayInputStream(encoded));
		} catch (PKIException e) {
			log.error("Get X509Certificate is failed!", e);
		} catch (CertificateException e) {
			log.error("Get X509Certificate is failed!", e);
		}
		return null;
	}

	/**
	 * 清理base64编码中的特殊字符，解码。
	 *
	 * @param data
	 *            base64编码的签名结果
	 * @return 二进制签名结果
	 */
	public static byte[] cleanAndDecodeBase64(byte[] data) {

		if (data == null) {
			throw new NullPointerException();
		}

		if (Parser.isBase64Encode(data)) {
			if (log.isDebugEnabled()) {
				log.debug("decoding base64 string");
			}
			byte[] buf = new byte[data.length];
			System.arraycopy(data, 0, buf, 0, data.length);
			buf = Parser.convertBase64(buf);
			buf = Base64.decode(buf);

			return buf;
		}

		return data;
	}

	/**
	 * 根据指定的cert证书获得对应的IssuerAndSerialNumber字符串
	 *
	 * @param cert
	 * @return string
	 */
	public static String getIssuerAndSerialNumberString(X509Cert cert) {
		BigInteger sn = cert.getSerialNumber();
		X509Name issuer = new X509Name(cert.getIssuer());
		IssuerAndSerialNumber iasn = new IssuerAndSerialNumber(issuer, sn);
		StringBuilder sb = new StringBuilder();
		sb.append(iasn.getName().toString());
		sb.append(iasn.getCertificateSerialNumber().getValue().toString());
		return sb.toString();
	}

	/**
	 * 获取证书SubjectKeyIdentifier对象内容的base64编码
	 *
	 * @param cert
	 * @return Base64 String
	 * @throws PKIException
	 */
	public static String getSubjectKeyIdentifierBase64String(X509Cert cert) throws PKIException {
		JKey publicKey = cert.getPublicKey();
		SubjectPublicKeyInfo subjectPublicKeyInfo = Parser.key2SPKI(publicKey);
		SubjectKeyIdentifier id = new SubjectKeyIdentifier(subjectPublicKeyInfo);
		byte[] temp = id.getKeyIdentifier();
		byte[] tempBase64 = Base64.encode(temp);
		return new String(tempBase64);
	}

	/**
	 * 根据签名包取证书集，通常长度为1
	 *
	 * @param signedData
	 *            签名包
	 * @return X509Cert证书集
	 */
	@SuppressWarnings("unchecked")
	public static HashMap<String, X509Cert> getX509Certs(SignedData signedData) {
		HashMap<String, X509Cert> tmap = new HashMap<String, X509Cert>(1);
		String tIssuerAndSN = "";
		// 获取签名者公钥证书
		ASN1Set certSet = signedData.getCertificates();
		Enumeration enumeration = certSet.getObjects();
		X509Cert cert = null;
		Object tobject = null;
		while (enumeration.hasMoreElements()) {
			tobject = enumeration.nextElement();
			if (tobject != null) {
				try {
					X509CertificateStructure certStruc = X509CertificateStructure.getInstance(tobject);
					cert = new X509Cert(certStruc);
					tIssuerAndSN = cert.getIssuer() + cert.getSerialNumber().toString();
					tmap.put(tIssuerAndSN, cert);
				} catch (Exception e) {
				}
			}
		}
		return tmap;
	}

	/**
	 * 根据P7签名包获取签名数据
	 *
	 * @param signedContent
	 *            P7签名包
	 * @return 签名数据
	 * @throws IOException
	 */
	public static SignedData getSignedData(byte[] signedContent) throws IOException {
		SignedData signedData = null;
		ASN1InputStream asn1Input = null;
		ByteArrayInputStream byteInput = null;
		ASN1Sequence sequence = null;
		ContentInfo contentInfo = null;
		byteInput = new ByteArrayInputStream(signedContent);
		asn1Input = new ASN1InputStream(byteInput);
		sequence = (ASN1Sequence) asn1Input.readObject();
		contentInfo = ContentInfo.getInstance(sequence);
		// 获取SignedData对象
		signedData = SignedData.getInstance(contentInfo.getContent());
		return signedData;
	}

	/**
	 * 根据P7签名包取证书集，通常长度为1
	 *
	 * @param signedData
	 *            P7签名包
	 * @return X509Cert证书集
	 * @throws IOException
	 */
	public static HashMap<String, X509Cert> getX509Certs(byte[] signedContent) throws IOException {
		return getX509Certs(getSignedData(signedContent));
	}

	/**
	 * 获取证书中身份证号码
	 *
	 * @param signedData
	 * @return
	 * @throws IOException
	 */
	public static String getX509CertSfzh(String signedData) throws IOException {
		byte[] signedContent = Base64.decode(signedData);
		HashMap<String, X509Cert> X509CertsMap = getX509Certs(signedContent);
		Iterator X509CertsIterator = X509CertsMap.keySet().iterator();
		X509Cert pkiCert = null;
		while (X509CertsIterator.hasNext()) {
			String tIssuerAndSN = (String) X509CertsIterator.next();
			pkiCert = X509CertsMap.get(tIssuerAndSN);
		}
		if (pkiCert != null) {
			Pattern p = Pattern.compile("CN.*([0-9]{17}[0-9xX]{1}).*CN");
			Matcher m = p.matcher(pkiCert.getSubject());
			if (m.find()) {
				return m.group(1);
			}else{
				p = Pattern.compile("CN.*([0-9]{15}).*CN");
				m = p.matcher(pkiCert.getSubject());
				if(m.find()){
					return m.group(1);
				}
			}
		}
		return null;
	}

	/**
	 * 获取证书中证书编号
	 *
	 * @param signedData
	 * @return
	 * @throws IOException
	 */
	public static String getX509CertZsbh(String signedData) throws IOException {
		byte[] signedContent = Base64.decode(signedData);
		HashMap<String, X509Cert> X509CertsMap = getX509Certs(signedContent);
		Iterator X509CertsIterator = X509CertsMap.keySet().iterator();
		X509Cert pkiCert = null;
		while (X509CertsIterator.hasNext()) {
			String tIssuerAndSN = (String) X509CertsIterator.next();
			pkiCert = X509CertsMap.get(tIssuerAndSN);
		}
		if (pkiCert != null) {
			Pattern p = Pattern.compile("CN.*([0-9]{17}[0-9xX]{1}).*CN");
			Matcher m = p.matcher(pkiCert.getSubject());
			if (m.find()) {
				return m.group(1);
			}else{
				p = Pattern.compile("CN.*([0-9]{15}).*CN");
				m = p.matcher(pkiCert.getSubject());
				if(m.find()){
					return m.group(1);
				}
			}
		}
		return null;
	}

	/**
	 * 生产验证码
	 *
	 * @return
	 */
	public static String generateVerifyCode() {
		String num = "1234567890abcdefghijklmnopqrstopqrstuvwxyz";
		int size = 6;
		char[] charArray = num.toCharArray();
		StringBuffer sb = new StringBuffer();
		for (int i = 0; i < size; i++) {
			sb.append(charArray[((int) (Math.random() * 10000) % charArray.length)]);
		}
		return sb.toString();
	}
}
