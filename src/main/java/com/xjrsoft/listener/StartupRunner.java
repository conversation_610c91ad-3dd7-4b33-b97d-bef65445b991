package com.xjrsoft.listener;

import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;

/**
 * @ClassName StartupRunner
 * @Description TODO
 * <AUTHOR>
 * @Date 2025/4/23 19:09
 * @Version 1.0
 */
@Component
@Log4j2
public class StartupRunner implements ApplicationRunner {
    @Value("${print.ip:}")
    private String printIp;
    @Value("${print.port:}")
    private Integer port;

    @Override
    public void run(ApplicationArguments args) throws Exception {
//        if (StringUtils.isNotBlank(printIp)) {
//            new Thread(() -> {
//                while (true) {
//                    try {
//                        Thread.sleep(10000);
//                    } catch (InterruptedException e) {
//                        e.printStackTrace();
//                    }
//                    if (!LeidenDeviceManager.getInstance().isConnect()) {
//                        //打印ip
//                        log.info(printIp);
//                        log.info(port);
//                        RfidUtil.connect(printIp, port, new LeidenConnectResultCallBack() {
//
//                            @Override
//                            public void success(LeidenDevice leidenDevice) {
//                                log.info("连接成功，connect success");
//                            }
//
//                            @Override
//                            public void close(LeidenDevice leidenDevice) {
//                                log.info("连接关闭，connect close");
//                            }
//
//                            @Override
//                            public void fail(LeidenDevice leidenDevice) {
//                                log.info("连接失败 connect fail");
//                            }
//                        });
//                    }else {
//                        log.info("已连接 connect success 2");
//
//                    }
//                }
//            }).start();
//        }

    }
}
