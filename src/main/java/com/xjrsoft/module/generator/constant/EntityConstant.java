package com.xjrsoft.module.generator.constant;

import lombok.Data;

/**
 * @title: 实体类模板引擎常量
 * <AUTHOR>
 * @Date: 2022/4/17 14:29
 * @Version 1.0
 */

public interface EntityConstant {
    /**
     * @des 包名
     * */
    String PACKAGE = "package";

    /**
     * @des 创建人
     * */
    String AUTH_NAME = "author";

    /**
     * @des 创建时间
     * */
    String DATE = "date";

    /**
     * @des 表备注
     * */
    String TABLE_COMMENT = "tableComment";

    /**
     * @des 表名
     * */
    String TABLE_NAME = "tableName";

    /**
     * @des 实体类名
     * */
    String ENTITY_CLASS_NAME = "entityClass";

    /**
     * @des 功能类名
     * */
    String FUNCTION_CLASS_NAME = "className";


    /**
     * @des 表里面所有列
     * */
    String TABLE_FIELDS = "fields";

    /**
     * 主键名
     */
    String PK_FIELD = "pkField";

    /**
     * 主键类型
     */
    String PK_TYPE = "pkType";

    /**
     * @des 是否分页
     * */
    String IS_PAGE = "isPage";

    /**
     * @des 是否多表
     * */
    String IS_MULTI = "isMulti";

    /**
     * @des 默认排序类型
     * */
    String ORDER_TYPE = "orderType";

    /**
     * @des 默认排序字段
     * */
    String ORDER_FIELD = "orderField";


    /**
     * @des 子表
     * */
    String CHILD_TABLES = "childTables";

    /**
     * @des 主表主键
     * */
    String PARENT_TABLE_KEY = "parentKey";


    /**
     * @des 子表所关联的主表字段
     * */
    String PARENT_RELATION_FIELD_KEY = "relationTableField";



    /**
     * @des 输出区域
     * */
    String OUTPUT_AREA = "outputArea";

    /**
     * 导入按钮
     */
    String IS_IMPORT = "isImport";

    /**
     * 导出按钮
     */
    String IS_EXPORT = "isExport";

    /**
     * 导出excel必填字段标识符
     */
    String REQUIRED_SUFFIX = "requiredSuffix";

    /**
     * 数据源id
     */
    String DATASOURCE_ID = "databaseId";

    /**
     * 权限字段
     */
    String IS_DATA_AUTH = "isDataAuth";

    /**
     * 自动编码值，逗号隔开
     */
    String CODE_RULES = "codeRules";
}
