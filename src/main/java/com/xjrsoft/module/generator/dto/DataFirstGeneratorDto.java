package com.xjrsoft.module.generator.dto;

import com.xjrsoft.module.generator.entity.FrontCode;
import com.xjrsoft.module.generator.entity.MenuConfig;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 数据优先生成代码
 *
 * @Author: tzx
 * @Date: 2022/6/2 15:45
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class DataFirstGeneratorDto extends DataFirstPreviewDto {
    /**
     * 代码模板id
     */
    private Long id;
    /**
     * 前端代码
     */
    private FrontCode frontCode;
    /**
     * 分类id
     */
    private Long categoryId;

    /**
     * 代码模板名称
     */
    private String name;

    /**
     * 菜单配置
     */
    @NotNull(message = "菜单配置不能为空！")
    @Valid
    private MenuConfig menuConfig;
}
