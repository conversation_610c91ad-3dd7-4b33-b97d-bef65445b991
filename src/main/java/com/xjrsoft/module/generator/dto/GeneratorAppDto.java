package com.xjrsoft.module.generator.dto;

import com.xjrsoft.module.generator.entity.FrontCode;
import lombok.Data;

/**
 * @Author: tzx
 * @Date: 2023/7/19 17:46
 */
@Data
public class GeneratorAppDto {


    /**
     * 功能类名
     */
    private String className;


    /**
     * 输出区域
     */
    private String outputArea;

    /**
     * 输出区域
     */
    private String outputValue;

    /**
     * components\system-form\index.vue 文件替换内容
     */
    private String tagString;


    /**
     * 列表页面代码
     */
    private String listCode;
    /**
     * 表单页面代码
     */
    private String formCode;
    /**
     * 接口请求代码
     */
    private String apiCode;

    /**
     * 配置json 代码
     */
    private String configJsonCode;

    /**
     * 表单容器代码
     */
    private String containerCode;
}
