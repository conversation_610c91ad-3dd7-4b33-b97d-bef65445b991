package com.xjrsoft.module.generator.dto;

import com.xjrsoft.common.model.generator.FormConfig;
import com.xjrsoft.module.generator.entity.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;

/**
 * @Author: tzx
 * @Date: 2023/9/1 17:43
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class BatchGeneratorDto extends  DataFirstPreviewDto {
    /**
     * 代码模板id
     */
    private Long id;
    /**
     * 前端代码
     */
    private FrontCode frontCode;
    /**
     * 分类id
     */
    private Long categoryId;

    /**
     * 代码模板名称
     */
    private String name;



}
