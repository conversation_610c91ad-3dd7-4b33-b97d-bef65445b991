package com.xjrsoft.module.generator.dto;

import com.xjrsoft.common.model.generator.FormConfig;
import com.xjrsoft.module.generator.entity.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;

/**
 * @title: 预览代码
 * <AUTHOR>
 * @Date: 2022/4/12 21:08
 * @Version 1.0
 */
@Data
public class DataFirstPreviewDto {
    /**
     * 数据库id
     */
    @NotNull(message = "数据库id不能为空！")
    private String databaseId;

    /**
     * 表关联信息
     */
    @NotNull(message = "表配置不能为空！")
    @Valid
    private List<TableConfig> tableConfigs;

    /**
     * 表单设计json
     */
    @NotNull(message = "表单设计不能为空！")
    private FormConfig formJson;

    /**
     * 列表配置
     */
    @NotNull(message = "列表配置不能为空！")
    @Valid
    private ListConfig listConfig;

    /**
     * 输出配置
     */
    @NotNull(message = "输出配置不能为空！")
    @Valid
    private OutputConfig outputConfig;

    @ApiModelProperty("表单事件")
    private Map<String, Object> formEventConfig;

    /**
     * 是否配置数据权限
     */
    private Boolean isDataAuth;


}
