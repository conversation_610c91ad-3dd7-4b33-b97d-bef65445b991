package com.xjrsoft.module.generator.utils;

import cn.hutool.extra.spring.SpringUtil;
import com.baomidou.mybatisplus.annotation.DbType;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.xjrsoft.common.utils.UserUtil;
import com.xjrsoft.module.organization.entity.Department;
import com.xjrsoft.module.organization.entity.User;
import com.xjrsoft.module.organization.service.IDepartmentService;
import com.xjrsoft.module.organization.service.IUserService;
import com.xjrsoft.module.system.vo.UserHj;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * @Author: tzx
 * @Date: 2022/5/5 11:54
 */
@Log4j2
public class SqlUtil {


    public static List<String> buildAddDataAuthFieldSqls(DbType dbType, String tableName) {
        List<String> sqlList = new ArrayList<>();
        switch (dbType) {
            case MYSQL:
                sqlList.add("ALTER TABLE " + tableName + " ADD rule_user_id bigint NULL COMMENT '权限所属人员id'");
                break;
            case SQL_SERVER:
            case SQL_SERVER2005:
                sqlList.add("ALTER TABLE " + tableName + " ADD rule_user_id bigint NULL");
                sqlList.add("EXEC sp_addextendedproperty 'MS_Description', '权限所属人员id', 'user', 'dbo', 'table', '" + tableName + "', 'column', 'rule_user_id'");
                break;
        }
        return sqlList;
    }

    public static String dsfou(String type) {
        UserHj gzhjObj = UserUtil.getGzhjObj();
        log.info("获取用户信息：{}", gzhjObj);
        IUserService userService = SpringUtil.getBean(IUserService.class);
        IDepartmentService departmentService = SpringUtil.getBean(IDepartmentService.class);
        User user = userService.getById(gzhjObj);
        if (StringUtils.isNotEmpty(user.getUseDepartmentIds())) {
            List<Department> departmentList = departmentService.list(new LambdaQueryWrapper<>(Department.class)
                    .in(Department::getId, user.getUseDepartmentIds().split(","))
                    .eq(Department::getEnabledMark, 1)
                    .eq(Department::getDepartmentType, type));
            List<String> departmentIdList = departmentList.stream().map(department -> department.getId()).collect(Collectors.toList());
            if (!departmentIdList.isEmpty()) {
                // in 字符串
                String inClause = departmentIdList.stream()
                        .map(id -> "'" + id + "'") // 给每个ID添加单引号
                        .collect(Collectors.joining(","));
                return "o.id IN ("+inClause+")";
            }
        }
        return "1=0";
    }

    public static String removeSqlComment(String sql) {
        // 匹配单行注释
        Pattern singleLinePattern = Pattern.compile("--.*?\\n");
        Matcher singleLineMatcher = singleLinePattern.matcher(sql);
        sql = singleLineMatcher.replaceAll("\n"); // 使用换行符替换单行注释

        // 匹配多行注释，注意这里需要小心处理嵌套的多行注释
        // 这个简单的实现假设没有嵌套的多行注释
        Pattern multiLinePattern = Pattern.compile("/\\*.*?\\*/", Pattern.DOTALL);
        Matcher multiLineMatcher = multiLinePattern.matcher(sql);
        sql = multiLineMatcher.replaceAll(""); // 直接移除多行注释

        return sql.trim(); // 返回去除前后空白的字符串
    }
    public static String textWithChinesePunctuation(String sql) {
        // 中文标点到英文标点的映射
        Map<String, String> punctuationMap = new HashMap<>();
        punctuationMap.put("，", ",");
        punctuationMap.put("。", ".");
        punctuationMap.put("！", "!");
        punctuationMap.put("？", "?");
        punctuationMap.put("：", ":");
        punctuationMap.put("；", ";");
        punctuationMap.put("……", "...");
        punctuationMap.put("（", "(");
        punctuationMap.put("）", ")");

        // 使用映射进行替换
        for (Map.Entry<String, String> entry : punctuationMap.entrySet()) {
            sql = sql.replaceAll(entry.getKey(), entry.getValue());
        }
        return sql;
    }
}
