package com.xjrsoft.module.generator.entity;

import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import java.util.List;

/**
 * @title: 输出配置
 * <AUTHOR>
 * @Date: 2022/4/14 21:45
 * @Version 1.0
 */
@Data
public class OutputConfig {
    /**
     * 创建人
     */
    @NotNull(message = "创建人不能为空！")
    @Length(max = 20, message = "创建人不能超过20字符！")
    private String creator;

    /**
     * 功能类名（必须英文字母开头 首字母不能使用字符以及数字）
     */
    @NotNull(message = "功能名称不能为空！")
    @Length(max = 20, message = "功能名称不能超过20字符！")
    @Pattern(regexp = "^[a-zA-Z][a-zA-Z0-9]*$", message = "功能名称只能是数字和字母组成，必须英文字母开头 首字母不能使用字符以及数字！")
    private String className;


    /**
     * 描述 注释
     */
    @NotNull(message = "描述不能为空！")
    @Length(max = 200, message = "描述不能超过200字符！")
    private String comment;

    private String remarks;

    /**
     * 输出区域（数据字典显示,数据字典id）
     */
    @NotNull(message = "输出区域不能为空！")
    private String outputArea;

    /**
     * 输出区域数据字典值
     */
    private String outputValue;

    /**
     * 只生成前端源码
     */
    private Boolean onlyFront = false;

    /**
     * 只生成接口
     */
    private Boolean onlyInterface = false;

    /**
     * 是否生产移动端
     */
    private Boolean isApp = false;

    /**
     * 是否生成菜单
     */
    private Boolean isMenu = true;

    /**
     * 是否配置数据权限
     */
    private Boolean isDataAuth;
    /**
     * 选择的数据权限id集合
     */
    private List<String> dataAuthList;

    /**
     * 0-数据优先模板,1-界面优先模板,2-简易模板
     */
    private Integer type;
}
