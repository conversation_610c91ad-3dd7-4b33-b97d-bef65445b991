package com.xjrsoft.module.generator.entity;

import lombok.Data;

/**
 * @title: 表结构配置的字段配置
 * <AUTHOR>
 * @Date: 2022/4/30 22:58
 * @Version 1.0
 */
@Data
public class TableFieldConfig {
    /**
     * 字段名称
     */
    private String fieldName;

    /**
     * 字段类型 0:文本 （默认255） 1:长文本 （max） 2:数字 3:小数 4:日期 5:日期时间 6:外键
     */
    private Integer fieldType;

    /**
     * 字段长度 （默认50）
     */
    private Integer fieldLength = 50;

    /**
     * 字段备注
     */
    private String fieldComment;

}
