package com.xjrsoft.module.generator.entity;

import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotNull;

/**
 * @title: 查询配置
 * <AUTHOR>
 * @Date: 2022/4/12 21:42
 * @Version 1.0
 */
@Data
public class QueryConfig {
    /**
     * 查询字段
     */
    @NotNull(message = "查询字段不能为空！")
    @Length(max = 30, message = "查询字段长度不能超过30！")
    private String fieldName;

    /**
     * 查询框名称
     */
    @NotNull(message = "查询框名称不能为空！")
    @Length(max = 20, message = "查询框名称不能超过20！")
    private String label;

    /**
     * 时间查询类型
     */
    @NotNull(message = "是否为时间查询不能为空！")
    private Boolean isDate;

    /**
     * 查询组件宽度
     */
    private Integer width;

    /**
     * 格式
     */
    private String format;

    /**
     * 字段类型
     */
    private int fieldType;

    /**
     * java字段类型
     */
    private String javaType;

}
