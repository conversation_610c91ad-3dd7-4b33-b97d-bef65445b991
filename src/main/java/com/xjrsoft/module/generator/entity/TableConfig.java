package com.xjrsoft.module.generator.entity;

import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotNull;

/**
 * @title: 数据表配置
 * <AUTHOR>
 * @Date: 2022/4/12 21:19
 * @Version 1.0
 */
public class TableConfig {

    /**
     * 表名
     */
    @NotNull(message = "表名不能为空！")
    @Length(max = 30, message = "表名长度不能超过30！")
    private String tableName;

    /**
     * 是否主表
     */
//    @NotNull(message = "是否主表不能为空！")
    private Boolean isMain = false;

    /**
     * 主键字段
     */
    @NotNull(message = "主键字段不能为空！")
    @Length(max = 30, message = "主键长度不能超过30！")
    private String pkField;

    /**
     * 主键字段类型
     */
    @NotNull(message = "主键字段类型不能为空！")
    @Length(max = 30, message = "主键类型长度不能超过30！")
    private String pkType;

    /**
     * 关联字段
     */
    @Length(max = 30, message = "关联字段长度不能超过30！")
    private String relationField;

    /**
     * 关联表对应字段
     */
    @Length(max = 30, message = "关联表对应字段长度不能超过30！")
    private String relationTableField;

    public String getTableName() {
        return tableName;
    }

    public void setTableName(String tableName) {
        this.tableName = tableName;
    }

    public Boolean getIsMain() {
        isMain = isMain == null ? false : isMain;
        return isMain;
    }

    public void setIsMain(Boolean main) {
        isMain = main;
    }

    public String getPkField() {
        return pkField;
    }

    public void setPkField(String pkField) {
        this.pkField = pkField;
    }

    public String getPkType() {
        return pkType;
    }

    public void setPkType(String pkType) {
        this.pkType = pkType;
    }

    public String getRelationField() {
        return relationField;
    }

    public void setRelationField(String relationField) {
        this.relationField = relationField;
    }

    public String getRelationTableField() {
        return relationTableField;
    }

    public void setRelationTableField(String relationTableField) {
        this.relationTableField = relationTableField;
    }

    public Boolean getMain() {
        isMain = isMain == null ? false : isMain;
        return isMain;
    }

    public void setMain(Boolean main) {
        isMain = main;
    }
}
