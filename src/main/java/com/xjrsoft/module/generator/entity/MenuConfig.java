package com.xjrsoft.module.generator.entity;

import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotNull;

/**
 * @title: 菜单配置
 * <AUTHOR>
 * @Date: 2022/4/16 23:08
 * @Version 1.0
 */
@Data
public class MenuConfig {
    /**
     * 编号
     */
    @NotNull(message = "菜单编号不能为空!")
    @Length( max = 10, message = "编号长度不能超过10个字符!")
    private String code;

    @NotNull(message = "菜单名不能为空!")
    @Length( max = 20, message = "编号长度不能超过20个字符!")
    private String name;

    private String parentId = "0";

    private String systemId = "1";

    private String icon;

    private Integer sortCode;

    private String remark;
}
