package com.xjrsoft.module.generator.entity;

import lombok.Data;

import java.util.Map;

/**
 * @title: ColumnConfig
 * <AUTHOR>
 * @Date: 2022/4/17 22:29
 * @Version 1.0
 */
@Data
public class ColumnConfig {

    /**
     * 显示名称
     */
    private String label;
    /**
     * 列名
     */
    private String columnName;

    /**
     * 对其
     */
    private String alignType;

    /**
     * 宽度
     */
    private String columnWidth;

    /**
     * 宽度
     */
    private Boolean autoWidth;

    /**
     * 组件类型
     */
    private String componentType;

    /**
     * 是否合计
     */
    private Boolean isTotal;


    /**
     * 是否是数字
     */
    private Boolean isNumber;

    /**
     * 日期时间格式
     */
    private String format;

    /**
     * 组件key值
     */
    private String key;

    /**
     * 组件配置信息
     */
    private Map<String, Object> componentProps;

    /**
     * 是否列头删选
     */
    private Boolean isFilter;
}
