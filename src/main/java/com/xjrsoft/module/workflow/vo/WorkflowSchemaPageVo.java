package com.xjrsoft.module.workflow.vo;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @Author: tzx
 * @Date: 2022/9/6 14:48
 */
@Data
public class WorkflowSchemaPageVo implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键")
    private Long id;

    @ApiModelProperty("流程编码")
    private String code;

    @ApiModelProperty("流程模板名称")
    private String name;

    @ApiModelProperty("流程分类")
    private Long category;

    @ApiModelProperty("流程分类")
    private String categoryName;

    @ApiModelProperty("流程定义id")
    private String definitionId;

    @ApiModelProperty("流程定义id")
    private String definitionKey;

    @ApiModelProperty("部署ID")
    private String deploymentId;

    @ApiModelProperty("是否在App上允许发起 1允许 2不允许")
    private Integer appShow;

    @ApiModelProperty("流程状态")
    private Integer enabledMark;

    @ApiModelProperty("备注")
    private String remark;


}
