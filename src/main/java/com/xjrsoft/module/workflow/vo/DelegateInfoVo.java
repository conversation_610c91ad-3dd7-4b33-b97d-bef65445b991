package com.xjrsoft.module.workflow.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
public class DelegateInfoVo {
    private Long id;

    @ApiModelProperty("被委托人id")
    private String delegateUserIds;

    @ApiModelProperty("被委托人姓名")
    private String delegateUserNames;

    @ApiModelProperty("开始时间")
    private LocalDateTime startTime;

    @ApiModelProperty("结束时间")
    private LocalDateTime endTime;

    @ApiModelProperty("说明备注")
    private String remark;

    @ApiModelProperty("模板id")
    private String schemeIds;

}
