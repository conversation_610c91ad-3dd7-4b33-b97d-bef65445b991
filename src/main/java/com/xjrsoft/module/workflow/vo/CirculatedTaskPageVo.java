package com.xjrsoft.module.workflow.vo;

import lombok.Data;

import java.util.Date;

/**
 * @Author: tzx
 * @Date: 2022/10/31 14:21
 */
@Data
public class CirculatedTaskPageVo {

    /**
     * 流水号
     */
    private Long serialNumber;

    /**
     * 模板id
     */
    private Long schemaId;

    /**
     * 任务id
     */
    private String taskId;

    /**
     * 流程id
     */
    private String processId;

    private String processName;

    /**
     * 任务名称
     */
    private String taskName;

    /**
     * 流程模板名称
     */
    private String schemaName;

    /**
     * 当前进度
     */
    private Integer currentProgress;

    /**
     * 发起人
     */
    private String originator;

    /**
     * 当前任务
     */
    private String currentTaskName;

    /**
     * 时间
     */
    private Date createTime;

    /**
     * 是否已阅
     */
    private Integer isRead;

    /**
     * 流程状态 审批中 0  审批完成  1  挂起 2
     */
    private Integer status;
}
