package com.xjrsoft.module.workflow.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;

/**
 * @Author: tzx
 * @Date: 2023/2/10 14:40
 */
@Data
public class RecycleProcessInfoVo {

    /**
     * 被回收的流程 的表单数据
     */
    private Map<String, Map<String,Object>> formDatas;


    /**
     * 模板信息
     */
    private WorkflowSchemaInfoVo schemaInfo;

    /**
     * 所有的关联任务信息
     */
    private List<StartProcessRelationTaskVo> relationTasks;
}
