package com.xjrsoft.module.workflow.vo;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * @Author: tzx
 * @Date: 2022/11/15 11:22
 */
@Data
public class MonitorPageVo {

    /***
     * 流程id
     */
    private String processId;


    /***
     * 任务id
     */
    private String taskId;

    /**
     * 流水号
     */
    private Long serialNumber;

    /**
     * 模板id
     */
    private Long schemaId;

    /**
     * 模板名称
     */
    private String schemaName;

    /**
     * 当前任务id
     */
    private String currentTaskId;

    /**
     * 当前任务名称
     */
    private String currentTaskName;

    /**
     * 当前流程状态
     */
    private String status;

    /**
     * 状态详情
     */
    private String statusMessage;

    /**
     * 当前进度
     */
    private Integer currentProgress;

    /**
     * 发起人
     */
    private String originator;

    /**
     * 开始时间
     */
    private LocalDateTime createDate;
}
