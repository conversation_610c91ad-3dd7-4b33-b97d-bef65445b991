package com.xjrsoft.module.workflow.vo;

import lombok.Data;

/**
 * @Author: tzx
 * @Date: 2022/11/30 10:53
 */
@Data
public class GetAssigneeVo {

    /**
     * 用户id
     */
    private Long id;

    /**
     * 用户名
     */
    private String name;

    /**
     * 编码
     */
    private String code;

    /**
     * 用户名
     */
    private String userName;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 性别
     */
    private Integer gender;

    /**
     * 用于判断是否可以减签
     */
    private Boolean canRemove;
}
