package com.xjrsoft.module.workflow.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: tzx
 * @Date: 2022/11/16 16:22
 */
@Data
public class SchemaHistoryListVo {
    private Long id;

    @ApiModelProperty("工作流模板id")
    private Long schemaId;

    @ApiModelProperty("版本")
    private Integer version;

    @ApiModelProperty("是否为活动版本")
    private Integer activityFlag;

    @ApiModelProperty("xml内容")
    private String xmlContent;

    @ApiModelProperty("json内容")
    private String jsonContent;

    @ApiModelProperty("创建人")
    private String createUserName;

    @ApiModelProperty("创建事件")
    private String createDate;

}
