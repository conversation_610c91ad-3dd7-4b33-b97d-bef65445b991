package com.xjrsoft.module.workflow.vo;

import com.xjrsoft.module.system.vo.MenuVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: tzx
 * @Date: 2023/3/20 11:24
 */
@Data
public class SpecialMenuInfoVo {

    @ApiModelProperty("专项菜单id")
    private String id;

    @ApiModelProperty("专项菜单功能编码")
    private String code;

    @ApiModelProperty("专项菜单功能名")
    private String name;

    @ApiModelProperty("菜单信息")
    private MenuVo menuInfo;

//    @ApiModelProperty("菜单编码")
//    private String menuCode;
//
//    @ApiModelProperty("菜单名称")
//    private String menuName;

    @ApiModelProperty("模板id")
    private String schemaId;

    @ApiModelProperty("表单字段配置")
    private String fieldConfig;

    @ApiModelProperty("查询配置")
    private String queryConfig;

}
