package com.xjrsoft.module.workflow.vo;

import lombok.Data;

import java.util.List;

/**
 * 发起流程 或者 审批流程 返回值
 * @Author: tzx
 * @Date: 2022/10/18 16:58
 */
@Data
public class LaunchAndApproveVo {

    /**
     * 下一用户任务Id （只返回用户任务的）
     */
    private String taskId;

    /**
     * 下一用户任务名称
     */
    private String taskName;

    /**
     * 是否多实例
     */
    private Boolean isMultiInstance = false;

    /**
     * 是否需要指定审批人
     */
    private Boolean isAppoint = false;


    /**
     * 是否可以配置临时宙批人
     */
    private Boolean provisionalApprover;

    /**
     * 如果需要指定审批人 此参数就会返回 候选人id
     * 审批候选人  逗号隔开
     */
    private String approveUserIds;

}
