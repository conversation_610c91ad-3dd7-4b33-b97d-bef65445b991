package com.xjrsoft.module.workflow.vo;

import com.alibaba.fastjson.JSONObject;
import io.swagger.annotations.ApiModelProperty;

/**
 * @Author: tzx
 * @Date: 2022/9/21 15:05
 */

public class WorkflowSchemaInfoVo {
    @ApiModelProperty("主键")
    private Long id;

    @ApiModelProperty("流程编码")
    private String code;

    @ApiModelProperty("流程模板名称")
    private String name;

    @ApiModelProperty("流程分类")
    private Long category;

    @ApiModelProperty("部署ID")
    private String deploymentId;

    @ApiModelProperty("流程定义id")
    private String definitionId;

    @ApiModelProperty("是否在App上允许发起 1允许 2不允许")
    private Integer appShow;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("模板内容")
    private String xmlContent;

    @ApiModelProperty("模板Json")
    private String jsonContent;

    @ApiModelProperty("模板Json对象")
    private JSONObject jsonContentObj;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Long getCategory() {
        return category;
    }

    public void setCategory(Long category) {
        this.category = category;
    }

    public String getDeploymentId() {
        return deploymentId;
    }

    public void setDeploymentId(String deploymentId) {
        this.deploymentId = deploymentId;
    }

    public String getDefinitionId() {
        return definitionId;
    }

    public void setDefinitionId(String definitionId) {
        this.definitionId = definitionId;
    }

    public Integer getAppShow() {
        return appShow;
    }

    public void setAppShow(Integer appShow) {
        this.appShow = appShow;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getXmlContent() {
        return xmlContent;
    }

    public void setXmlContent(String xmlContent) {
        this.xmlContent = xmlContent;
    }

    public String getJsonContent() {
        return jsonContent;
    }

    public void setJsonContent(String jsonContent) {
        this.jsonContent = jsonContent;
        try {
            if (jsonContent != null){
                this.jsonContentObj = JSONObject.parseObject(jsonContent);
                setJsonContentObj(jsonContentObj);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public JSONObject getJsonContentObj() {

        return jsonContentObj;

    }

    public void setJsonContentObj(JSONObject jsonContentObj) {
        this.jsonContentObj = jsonContentObj;
    }
}
