package com.xjrsoft.module.workflow.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: tzx
 * @Date: 2023/3/20 10:07
 */
@Data
public class SpecialMenuPageVo {

    @ApiModelProperty("专项菜单id")
    private String id;

    @ApiModelProperty("专项菜单功能编码")
    private String code;

    @ApiModelProperty("专项菜单功能名")
    private String name;

    @ApiModelProperty("菜单编码")
    private String menuCode;

    @ApiModelProperty("菜单名称")
    private String menuName;

    @ApiModelProperty("模板id")
    private String schemaId;

    @ApiModelProperty("表单字段配置")
    private String fieldConfig;

    @ApiModelProperty("查询配置")
    private String queryConfig;
}
