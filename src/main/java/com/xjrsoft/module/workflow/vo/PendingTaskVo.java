package com.xjrsoft.module.workflow.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Date;

/**
 * 待办任务vo
 * @Author: tzx
 * @Date: 2022/10/11 10:20
 */
@Data
public class PendingTaskVo {

    private Long id;

    private Long serialNumber;

    private String taskId;

    private String processName;

    private String taskName;

    private String taskKey;

    private Integer currentProgress;

    private Long schemaId;

    private String schemaName;

    private String processId;

    private Long startUserId;

    private String startUserName;

    private LocalDateTime startTime;

    private LocalDateTime endTime;

    private LocalDateTime launchTime;

    private String approveUserIds;
}
