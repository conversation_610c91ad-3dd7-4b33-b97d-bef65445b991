package com.xjrsoft.module.workflow.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xjrsoft.module.organization.entity.User;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * @Author: tzx
 * @Date: 2022/11/8 16:10
 */
@Data
public class ProcessRecordListVo {

    /**
     * 节点类型
     */
    private String nodeType;

    /**
     * 节点名称
     */
    private String nodeName;

    /**
     * 审批信息
     */
    private String comment;

    /**
     * 开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss[.SSS]")
    private LocalDateTime startTime;
    /**
     * 传阅信息
     */
    private String circulateMessage;

    /**
     * 审批结果
     */
    private String spjg;

    /**
     * 审批人
     */
    private User user;

    /**
     * 审批意见
     */
    private String  spyj;

    /**
     * node key
     */
    private String nodeKey;
}
