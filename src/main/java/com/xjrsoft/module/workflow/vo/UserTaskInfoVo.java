package com.xjrsoft.module.workflow.vo;

import com.xjrsoft.module.workflow.entity.WorkflowApproveRecord;
import com.xjrsoft.module.workflow.model.ButtonConfig;
import com.xjrsoft.module.workflow.model.FormAssignmentConfig;
import com.xjrsoft.module.workflow.model.OpinionConfig;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * @Author: tzx
 * @Date: 2023/1/30 14:34
 */
@Data
public class UserTaskInfoVo {
    /**
     * 所绑定的表单信息
     */
    private List<UserTaskFormInfoVo> formInfos;

    /**
     * 模板信息
     */
    private WorkflowSchemaInfoVo schemaInfo;

    /**
     * 所有选择的关联任务信息
     */
    private List<UserTaskRelationTaskVo> relationTasks;

    /**
     * 按钮配置
     */
    private List<ButtonConfig> buttonConfigs;

    /**
     * 流程的流转记录
     */
    private List<ProcessRecordListVo> taskRecords;

    /**
     * 是否会签节点
     */
    private Boolean isCountersign;

    /**
     * 是否可以加减签
     */
    private Boolean isAddOrSubSign;

    /**
     * 意见框配置
     */
    private OpinionConfig opinionConfig;

    /**
     * 用户任务审批意见
     */
    private List<WorkflowApproveRecord> taskApproveOpinions;

    /**
     * 用户任务审批意见
     */
    private Map<String,List<WorkflowApproveRecord>> taskApproveOpinionListMap;

    /**
     * 表单赋值 的formData
     */
    private Map<String,Map<String, Object>> formAssignmentData;

    private List<Map<String,Object>> otherProcessApproveRecord;

    /**
     * 流程图
     */
    private String workflowChat;
}
