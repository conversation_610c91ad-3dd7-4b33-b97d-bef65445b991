package com.xjrsoft.module.workflow.vo;

import lombok.Data;

import java.util.Date;

/**
 * @Author: tzx
 * @Date: 2022/10/27 14:45
 */
@Data
public class RelationTaskPageVo {


    /**
     * 任务id
     */
    private String taskId;

    /**
     * 流程id
     */
    private String processId;

    /**
     * 任务名称
     */
    private String taskName;

    /**
     * 流程模板名称
     */
    private String schemaName;

    /**
     * 当前进度
     */
    private String currentProgress;

    /**
     * 发起人
     */
    private String originator;

    /**
     * 时间
     */
    private Date createTime;
}
