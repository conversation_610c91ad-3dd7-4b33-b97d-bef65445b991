package com.xjrsoft.module.workflow.model;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 外部流程 配置
 * @Author: tzx
 * @Date: 2022/11/18 10:54
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class CallActivityConfig extends NodeBasicConfig {

    /**
     * 被调用元素（外部流程schemaId）
     */
    private String schemaId;

    /**
     * 调用类型 0 单实例 1 多实例
     */
    private Integer callActivityType;

    /**
     * 执行类型 0 顺序  1  并行
     */
    private Integer executionType;

    /**
     * 完成条件 0 全部  1 单个 2百分比
     */
    private Integer finishType;

    /**
     * 如果完成条件是百分比  百分比数值
     */
    private Integer percentOf;

    /**
     * 发起人 类型 0 人员  1 API   2表单
     */
    private Integer originatorType;

    /**
     *  如果调用类型多实例 发起人配置方式
     */
    private MemberConfig memberConfig;


    /**
     *  如果调用类型为单实例 子流程发起人
     */
    private String originatorNode;

    /**
     * 多实例发起人配置
     */
    private String originatorConfig;

    /**
     * 入参
     */
    private List<CallActivityParamConfig> inParams;

    /**
     * 出参
     */
    private List<CallActivityParamConfig> outParams;



}
