package com.xjrsoft.module.workflow.model;

import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotNull;

/**
 * @Author: tzx
 * @Date: 2023/3/20 10:31
 */
@Data
public class SpecialMenuQueryConfig {

    /**
     * 是否启用
     */
    private Boolean checked;

    /**
     * 查询类别 0 时间查询 1 流水号  2 表单字段
     */
    private Boolean queryType;

    /**
     * 查询字段
     */
    private String fieldName;

    /**
     * 查询框名称
     */
    private String label;


}
