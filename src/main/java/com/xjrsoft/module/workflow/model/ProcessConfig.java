package com.xjrsoft.module.workflow.model;

import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.List;

/**
 * 流程总体配置
 * @Author: tzx
 * @Date: 2022/9/16 14:57
 */
@Data
public class ProcessConfig {


    /**
     * 流程定义id
     */
    private String processId;

    /**
     * 模板编码
     */
    @NotNull(message = "模板编码不能为空！")
    private String code;

    /**
     * 模板名称
     */
    @NotNull(message = "模板名称不能为空！")
    @Length(min = 2,max = 20,message = "模板名称长度必须大于2 小于20")
    private String name;

    /**
     * 分类id  关联 数据字典detail
     */
    @NotNull(message = "模板分类不能为空！")
    private String category;

    /**
     * 命名规则 显示值
     */
    private String nameRule;

    /**
     * 命名规则配置
     */
    private List<NameRuleConfig> nameRuleConfigs;


    /**
     * 自动同意规则  如果此处配置  无法选择下一节点审批人
     */
    private List<Integer> autoAgreeRule;

    /**
     *   是否由上一届点审批人指定下一节点审批人 如果此处配置  无法选择下一节点审批人
     */
    private Integer isPrevChooseNext;

    /**
     * 无对应处理人 多实例无法使用
     */
    private Integer noHandler;

    private Boolean multiAudit;

    private String defaultApprover;

    /**
     *  是否继承开始节点表单
     */
    private Boolean implStartNodeForm;

    /**
     * 是否app显示
     */
    private Boolean appShow = false;

    /**
     * 默认表单
     */
    private List<FormConfig> defaultFormList = new ArrayList<>();

    /**
     * 权限设置
     */
    private AuthConfig authConfig;

    /**
     * 菜单设置
     */
    private MenuConfig menuConfig;


    /**
     * 表单发起流程配置
     */
    private FormInitConfig formInitConfig;

    /**
     * 超时提醒配置
     */
    private TimeoutRemidConfig timeoutRemidConfig;

    /**
     * 关联任务配置
     */
    private List<RelationProcessConfig> relationProcessConfigs;

    /**
     * 流程参数配置
     */
    private List<ProcessParamConfig> processParamConfigs =new ArrayList<>();


    /**
     * 备注
     */
    private String remark;

    /**
     * xml
     */
    private String xmlContent;

    /**
     * 流程设计图
     */
    private String workflowChat;

    private String valueSet;

    private String zdyspsj;

}
