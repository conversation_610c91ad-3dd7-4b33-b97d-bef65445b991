package com.xjrsoft.module.workflow.model;

import lombok.Data;

/**
 * 成员配置信息  多个地方会使用
 * 推送成员配置
 * 审批成员配置
 * 等等
 *
 * @Author: tzx
 * @Date: 2022/10/8 15:12
 */
@Data
public class MemberConfig {

    /**
     * 人员类型
     */
    private Integer memberType;

    /**
     * 所选类型 的 id (用户 角色  岗位 指定节点审批人 )
     */
    private String id;

    /**
     * 所选类型的 name
     */
    private String name;


    /**
     * 上级领导配置
     */
    private LeaderConfig leaderConfig;

    /**
     * sql 指定人员配置
     */
    private SqlConfig sqlConfig;

    /**
     * 表单字段配置
     */
    private FormFieldConfig formFieldConfig;

    /**
     * api配置
     */
    private ApiConfig apiConfig;
}
