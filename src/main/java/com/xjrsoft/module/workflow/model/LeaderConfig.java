package com.xjrsoft.module.workflow.model;

import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 上级领导配置
 *
 * @Author: tzx
 * @Date: 2022/10/9 17:13
 */
@Data
public class LeaderConfig {
//    /**
//     * 发起人上级领导配置
//     * [true,false,true,false,true] 这种格式
//     */
//    private List<Boolean> originatorLeader;
//
//    /**
//     * 指定节点审批人上级领导
//     * {
//     *     "节点标识1": [true,false,true,false,true] 这种格式
//     *     "节点标识2": [true,false,true,false,true] 这种格式
//     * }
//     *
//     */
//    private Map<String, List<Boolean>> nodeApproveLeader;

    /**
     * 节点审批人
     */
    private String nodeId;


    /**
     * 节点第几级别  一共五个 1 2 3 4 5
     */
    private Integer level;
}
