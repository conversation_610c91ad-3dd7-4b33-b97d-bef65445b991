package com.xjrsoft.module.workflow.model;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 流程线配置
 * @Author: tzx
 * @Date: 2022/9/23 10:55
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SequenceFlowConfig extends NodeBasicConfig {


    /**
     * 流转条件配置 字符串
     */
    private String conditionContent;

    /**
     * 变量来源配置信息
     */
    private List<ConditionConfig> conditionConfigs;
}
