package com.xjrsoft.module.workflow.model;

import lombok.Data;

import java.util.List;

/**
 * 表单配置下的 表单各字段项 配置
 * @Author: tzx
 * @Date: 2022/12/6 10:08
 */
@Data
public class FormItemConfig {

    /**
     * 唯一标识
     */
    private String key;

    /**
     * 当前字段项 是否必填
     */
    private Boolean required;

    /**
     * 当前字段项 是否可以查看
     */
    private Boolean view;

    /**
     * 当前字段项 是否可以编辑
     */
    private Boolean edit;

    /**
     * 当前字段项 禁止操作
     */
    private Boolean disabled;

    /**
     * 表名
     */
    private String tableName;

    /**
     * 字段名
     */
    private String fieldName;

    /**
     * 字段唯一标识
     */
    private String fieldId;

    /**
     * 是否子表
     */
    private Boolean isSubTable;

    /**
     * 显示子级
     */
    private Boolean showChildren;

    /**
     * 子表单 子级配置
     */
    private List<FormItemConfig> children;
}
