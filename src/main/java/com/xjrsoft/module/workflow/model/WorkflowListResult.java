package com.xjrsoft.module.workflow.model;

import com.xjrsoft.module.organization.entity.User;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * @Author: tzx
 * @Date: 2023/4/3 9:31
 */
@Data
public class WorkflowListResult {

    /**
     * 流程状态
     */
    private String status;

    /**
     * 流程id
     */
    private String processId;

    /**
     * 流程模板id
     */
    private Long schemaId;

    /**
     * 当前任务id
     */
    private List<String> taskIds;

    /**
     * 是否启用 关联流程
     */
    private Boolean enabled = Boolean.TRUE;

    /**
     * 草稿id
     */

    private Long draftId;

    private List<User> users = new ArrayList<>();
}
