package com.xjrsoft.module.workflow.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 关联任务配置
 * @Author: tzx
 * @Date: 2022/9/16 16:55
 */
@Data
public class RelationProcessConfig {
    /**
     * 工作流模板id
     */
    @ApiModelProperty("工作流模板id")
    @NotNull(message = "工作流模板id！")
    private Long id;

    /**
     * 工作流模板id
     */
    @ApiModelProperty("关联流程名称")
    @NotBlank(message = "关联流程名称不能为空！")
    private String name;

    /**
     * 工作流模板id
     */
    @ApiModelProperty("关联流程定义key")
    @NotBlank(message = "关联流程定义key不能为空！")
    private String definitionKey;

    /**
     * 流程状态 0 审批中   1审批通过
     */
    @NotNull(message = "流程状态不能为空！")
    private Integer processStatus = 0;


    /**
     * 任务权限
     * 0 限发起人发起的此模板任务 1 所有此模板发起的任务
     */
    @NotNull(message = "任务权限不能为空！")
    private Integer processAuth = 0;
}
