package com.xjrsoft.module.workflow.model;

import lombok.Data;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.Min;
import java.util.List;

/**
 * @Author: tzx
 * @Date: 2022/9/16 16:40
 */
@Data
public class TimeoutRemidConfig {
    /**
     * 是否提醒
     */
    private Boolean enabled = false;

    /**
     * 超时时间
     */
    @Range(min = 1, max = 1024, message = "超时时间不得小于1小时  不得大于1024小时")
    private Integer hour;

    /**
     * 间隔
     */
    @Range(min = 1, max = 1024, message = "提醒间隔不得小于1小时  不得大于1024小时")
    private Integer interval;

    /**
     * 推送次数
     */
    @Min(value = 1,message = "推送次数不小于1次！")
    private Integer pushHits = 1;

    /**
     * 推送成员
     */
    private List<MemberConfig> pushMemberConfigs;
}
