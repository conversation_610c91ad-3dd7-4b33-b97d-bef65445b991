package com.xjrsoft.module.workflow.model;

import cn.hutool.extra.spring.SpringUtil;
import com.xjrsoft.common.utils.ContextListSchemaHolder;
import com.xjrsoft.module.zy.form.pojo.entity.ZyFormEdit;
import com.xjrsoft.module.zy.form.service.ZyFormEditService;
import lombok.EqualsAndHashCode;

import java.util.Iterator;
import java.util.List;

/**
 * @Author: tzx
 * @Date: 2022/9/22 16:10
 */
@EqualsAndHashCode(callSuper = true)
public class UserTaskConfig extends NodeBasicConfig {

    /**
     * 自动同意规则  如果此处配置  无法选择下一节点审批人
     */
    private List<Integer> autoAgreeRule;

    /**
     *   是否由上一届点审批人指定下一节点审批人 如果此处配置  无法选择下一节点审批人
     */
    private Integer isPrevChooseNext;

    /**
     * 无对应处理人 多实例无法使用
     */
    private Integer noHandler;

    private Boolean multiAudit;

    private String defaultApprover;

    /**
     * 意见模版
     */
    private String yjmb;


    /**
     * 当前进度
     */
    private Integer currentProgress;

    /**
     * 是否可以配置临时宙批人
     */
    private Boolean provisionalApprover;

    /**
     * 审批人配置
     */
    private List<MemberConfig> approverConfigs;

    /**
     * 传阅人配置
     */
    private List<MemberConfig> circulateConfigs;


    /**
     * 表单配置
     */
    private List<FormConfig> formConfigs;

    /**
     * 会签配置
     */
    private CountersignConfig countersignConfig;


    /**
     * 按钮配置
     */
    private List<ButtonConfig> buttonConfigs;


    /**
     * 意见框配置
     */
    private OpinionConfig opinionConfig;

    /**
     * 参数操作
     */
    private AssignmentConfig assignmentConfig;

    /**
     * 通知策略
     */
    private List<Integer> noticePolicyConfigs;

    private ZyFormEdit formEdit;

    public List<Integer> getAutoAgreeRule() {
        return autoAgreeRule;
    }

    public void setAutoAgreeRule(List<Integer> autoAgreeRule) {
        this.autoAgreeRule = autoAgreeRule;
    }

    public Integer getIsPrevChooseNext() {
        return isPrevChooseNext;
    }

    public void setIsPrevChooseNext(Integer isPrevChooseNext) {
        this.isPrevChooseNext = isPrevChooseNext;
    }

    public Integer getNoHandler() {
        return noHandler;
    }

    public void setNoHandler(Integer noHandler) {
        this.noHandler = noHandler;
    }

    public Integer getCurrentProgress() {
        return currentProgress;
    }

    public void setCurrentProgress(Integer currentProgress) {
        this.currentProgress = currentProgress;
    }

    public Boolean getProvisionalApprover() {
        return provisionalApprover;
    }

    public void setProvisionalApprover(Boolean provisionalApprover) {
        this.provisionalApprover = provisionalApprover;
    }

    public List<MemberConfig> getApproverConfigs() {
        return approverConfigs;
    }

    public void setApproverConfigs(List<MemberConfig> approverConfigs) {
        this.approverConfigs = approverConfigs;
    }

    public List<MemberConfig> getCirculateConfigs() {
        return circulateConfigs;
    }

    public void setCirculateConfigs(List<MemberConfig> circulateConfigs) {
        this.circulateConfigs = circulateConfigs;
    }

    public List<FormConfig> getFormConfigs() {
        String context = ContextListSchemaHolder.getContext();
        if (context != null) {
            Iterator<FormConfig> iterator = formConfigs.iterator();
            while (iterator.hasNext()) {
                FormConfig formConfig = iterator.next();
                String formId = formConfig.getFormId();
                ZyFormEditService formEditService = SpringUtil.getBean(ZyFormEditService.class);
                ZyFormEdit zyFormEdit = formEditService.getById(formId);
                if (zyFormEdit != null) {
                    if (!context.equals(zyFormEdit.getSchemeinfoId())) {
                        iterator.remove();
                    }
                }
            }
        }
        return formConfigs;
    }

    public void setFormConfigs(List<FormConfig> formConfigs) {
        this.formConfigs = formConfigs;
        if (this.formConfigs != null && this.formConfigs.size() > 0) {
            FormConfig formConfig = this.formConfigs.get(0);
            ZyFormEditService formEditService = SpringUtil.getBean(ZyFormEditService.class);
            this.formEdit = formEditService.getById(formConfig.getFormId());
        }
    }

    public CountersignConfig getCountersignConfig() {
        return countersignConfig;
    }

    public void setCountersignConfig(CountersignConfig countersignConfig) {
        this.countersignConfig = countersignConfig;
    }

    public List<ButtonConfig> getButtonConfigs() {
        return buttonConfigs;
    }

    public void setButtonConfigs(List<ButtonConfig> buttonConfigs) {
        this.buttonConfigs = buttonConfigs;
    }

    public OpinionConfig getOpinionConfig() {
        return opinionConfig;
    }

    public void setOpinionConfig(OpinionConfig opinionConfig) {
        this.opinionConfig = opinionConfig;
    }

    public AssignmentConfig getAssignmentConfig() {
        return assignmentConfig;
    }

    public void setAssignmentConfig(AssignmentConfig assignmentConfig) {
        this.assignmentConfig = assignmentConfig;
    }

    public List<Integer> getNoticePolicyConfigs() {
        return noticePolicyConfigs;
    }

    public void setNoticePolicyConfigs(List<Integer> noticePolicyConfigs) {
        this.noticePolicyConfigs = noticePolicyConfigs;
    }

    public String getYjmb() {
        return yjmb;
    }

    public void setYjmb(String yjmb) {
        this.yjmb = yjmb;
    }

    public ZyFormEdit getFormEdit() {
        return formEdit;
    }

    public void setFormEdit(ZyFormEdit formEdit) {
        this.formEdit = formEdit;
    }

    public Boolean getMultiAudit() {
        return multiAudit;
    }

    public void setMultiAudit(Boolean multiAudit) {
        this.multiAudit = multiAudit;
    }

    public String getDefaultApprover() {
        return defaultApprover;
    }

    public void setDefaultApprover(String defaultApprover) {
        this.defaultApprover = defaultApprover;
    }
}
