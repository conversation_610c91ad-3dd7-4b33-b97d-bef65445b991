package com.xjrsoft.module.workflow.model;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * @Author: tzx
 * @Date: 2022/9/16 16:58
 */
@Data
public class ProcessParamConfig {

    /**
     * 参数id  唯一标识
     */
    @NotBlank(message = "参数id 不能为空！")
    private String id;

    /**
     * 参数名
     */
    @NotBlank(message = "参数名 不能为空！")
    private String name;

    /**
     * 流程类型 0 值 1 变量 2 sql  3api
     */
    @NotNull(message = "参数名 不能为空！")
    private Integer type = 0;

    /**
     * 参数值  type 如果是 值 或者 变量 都使用 此字段
     */
    private String value;


    /**
     * 流程参数 从api 获取 值
     */
    private ApiConfig apiConfig;


}
