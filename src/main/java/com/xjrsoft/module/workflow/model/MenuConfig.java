package com.xjrsoft.module.workflow.model;

import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotNull;

/**
 *
 * @Author: tzx
 * @Date: 2022/9/16 15:54
 */
@Data
public class MenuConfig {
    /**
     * 编号
     */
    private String code;

    /**
     * 菜单名
     */
    private String name;

    /**
     * 上级
     */
    private String parentId;

    /**
     * 图标
     */
    private String icon;

    /**
     * 排序
     */
    private Integer sortCode;

    /**
     * 备注
     */
    private String remark;
}
