package com.xjrsoft.module.workflow.model;

import lombok.Data;

import java.util.List;

/**
 * 表单配置
 * @Author: tzx
 * @Date: 2022/9/22 15:59
 */
@Data
public class FormConfig {

    /**
     * 唯一标识
     */
    private String key;

    /**
     * 表单类型
     */
    private Integer formType;

    /**
     * 表单id
     */
    private String formId;

    /**
     * 表单名
     */
    private String formName;
    /**
     * 表单名
     */
    private String name;

    /**
     * 显示子级
     */
    private Boolean showChildren;

    /**
     * 是否全部必填
     */
    private Boolean requiredAll;


    /**
     * 是否全部可以查看
     */
    private Boolean viewAll;

    /**
     * 是否全部可以编辑
     */
    private Boolean editAll;

    /**
     * 表单字段配置
     */
    private List<FormItemConfig> children;



}
