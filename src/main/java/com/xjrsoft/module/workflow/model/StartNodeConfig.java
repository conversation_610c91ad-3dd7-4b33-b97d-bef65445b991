package com.xjrsoft.module.workflow.model;

import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.json.JSONUtil;
import com.xjrsoft.common.utils.ContextFormDataHolder;
import com.xjrsoft.module.zy.form.pojo.entity.ZyFormEdit;
import com.xjrsoft.module.zy.form.service.ZyFormEditService;
import lombok.EqualsAndHashCode;

import java.util.ArrayList;
import java.util.List;

/**
 * @Author: tzx
 * @Date: 2022/9/22 15:53
 */
@EqualsAndHashCode(callSuper = true)
public class StartNodeConfig extends NodeBasicConfig {

    /**
     * 表单配置
     */
    private List<FormConfig> formConfigs;

    /**
     * 参数操作
     */
    private AssignmentConfig assignmentConfig;

    public List<FormConfig> getFormConfigs() {
        String formId = ContextFormDataHolder.getContext();
        if (StrUtil.isNotEmpty(formId)) {
            ZyFormEditService formEditService = SpringUtil.getBean(ZyFormEditService.class);
            ZyFormEdit zyFormEdit = formEditService.getById(formId);
            if (zyFormEdit != null && StrUtil.isNotBlank(zyFormEdit.getNodeConfig())) {
                String nodeConfig = zyFormEdit.getNodeConfig();
                FormConfig bean = JSONUtil.toBean(nodeConfig, FormConfig.class);
                List<FormConfig> formConfigList =  new ArrayList<>();
                formConfigList.add(bean);
                return formConfigList;
            }
        }
        return formConfigs;
    }

    public void setFormConfigs(List<FormConfig> formConfigs) {
        this.formConfigs = formConfigs;
    }

    public AssignmentConfig getAssignmentConfig() {
        return assignmentConfig;
    }

    public void setAssignmentConfig(AssignmentConfig assignmentConfig) {
        this.assignmentConfig = assignmentConfig;
    }
}
