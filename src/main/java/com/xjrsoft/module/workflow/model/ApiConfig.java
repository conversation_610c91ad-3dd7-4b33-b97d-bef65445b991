package com.xjrsoft.module.workflow.model;

import lombok.Data;

import java.util.List;

/**
 * @Author: tzx
 * @Date: 2022/12/15 10:56
 */
@Data
public class ApiConfig {

    /**
     * 唯一标识
     */
    private String id;

    /**
     * magic-api name
     */
    private String name;

    /**
     * magic-api 请求方法
     */
    private String method;

    /**
     * param 参数配置
     */
    private List<ApiRequestParamsConfig> requestParamsConfigs;

    /**
     * body 参数配置
     */
    private List<ApiRequestParamsConfig> requestBodyConfigs;

    /**
     * Header 参数配置
     */
    private List<ApiRequestParamsConfig> requestHeaderConfigs;
}
