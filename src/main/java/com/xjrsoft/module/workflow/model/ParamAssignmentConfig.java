package com.xjrsoft.module.workflow.model;

import lombok.Data;

/**
 * 参数操作  表单赋值 参数赋值 配置
 * @Author: tzx
 * @Date: 2022/10/17 11:14
 */
@Data
public class ParamAssignmentConfig {
    /**
     * 赋值类型 0  值 1 变量  2 表单  3 api
     */
    private Integer type;

    /**
     * 如果是 type === 0 就存储值
     */
    private String value;

    /**
     * 如果type === 1 存储所选变量
     */
    private String varValue;

    /**
     * 如果type === 2 存储form配置
     */
    private FormAssignmentSourceConfig formConfig;

    /**
     * api
     */
    private ApiConfig apiConfig;

    /**
     * 目标变量 存储 流程变量名 processConfig 的  paramName
     */
    private String target;
}
