package com.xjrsoft.module.workflow.model;

import lombok.Data;

import java.util.List;

/**
 * 各个节点基础配置
 * @Author: tzx
 * @Date: 2022/10/13 15:10
 */
@Data
public class NodeBasicConfig {
    /**
     * 节点id 前端生成  开始节点 会以Start开头
     */
    private String id;

    /**
     * 节点类型
     */
    private String type;

    /**
     * 节点名称
     */
    private String name;

    /**
     * 备注
     */
    private String remark;

    /**
     * 节点开始事件
     */
    private List<NodeEventConfig> startEventConfigs;


    /**
     * 节点结束事件
     */
    private List<NodeEventConfig> endEventConfigs;

    private String status;
}
