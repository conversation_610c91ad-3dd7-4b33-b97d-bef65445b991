package com.xjrsoft.module.workflow.model;

import lombok.Data;

/**
 * 按钮配置
 * @Author: tzx
 * @Date: 2022/9/23 9:45
 */
@Data
public class ButtonConfig {


    /**
     * 是否为默认按钮
     */
    private Boolean isDefault;

    /**
     * 是否选中
     */
    private Boolean checked;

    /**
     * 审批类型 0 同意 1 拒绝 2 驳回 3 结束 4 其他（用户自定义按钮）
     */
    private Integer approveType;

    /**
     * 按钮类型 0  流转按钮  1 功能按钮
     */
    private Integer buttonType;

    /**
     * 按钮名
     */
    private String buttonName;

    /**
     * 按钮编码
     */
    private String buttonCode;


    /**
     * 按钮操作  0 允许驳回到任意流转过的节点  1只允许驳回到上一节点
     */
    private Integer buttonOpera;


    /**
     * 脚本任务类型  0  脚本参数配置 1 sql    0  API
     */
    private Integer scriptType;


    /**
     * 脚本语言 0 javascript  1 groovy
     */
    private Integer scriptLanguage;

    /**
     * 脚本内容
     */
    private String scriptContent;

    /**
     * 脚本内容
     */
    private ApiConfig apiConfig;

}
