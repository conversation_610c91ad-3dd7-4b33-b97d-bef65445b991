package com.xjrsoft.module.workflow.listener;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.xjrsoft.common.enums.WorkflowNoticePolicyType;
import com.xjrsoft.common.enums.YesOrNoEnum;
import com.xjrsoft.common.exception.MyException;
import com.xjrsoft.module.oa.utils.SendMessageUtil;
import com.xjrsoft.module.workflow.constant.WorkflowConstant;
import com.xjrsoft.module.workflow.entity.WorkflowSchema;
import com.xjrsoft.module.workflow.model.*;
import com.xjrsoft.module.workflow.service.IWorkflowExecuteService;
import com.xjrsoft.module.workflow.service.IWorkflowSchemaService;
import com.xjrsoft.module.workflow.utils.WorkFlowUtil;
import org.apache.commons.collections.MapUtils;
import org.camunda.bpm.engine.ProcessEngines;
import org.camunda.bpm.engine.TaskService;
import org.camunda.bpm.engine.task.Task;
import org.springframework.data.redis.connection.Message;
import org.springframework.data.redis.listener.KeyExpirationEventMessageListener;
import org.springframework.data.redis.listener.RedisMessageListenerContainer;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * 超时提醒 监听器
 * 监听redis key 过期
 *
 * @Author: tzx
 * @Date: 2022/10/26 14:46
 */
@Component
public class TaskTimeoutListener extends KeyExpirationEventMessageListener {

    private final IWorkflowExecuteService workflowExecuteService;

    public TaskTimeoutListener(RedisMessageListenerContainer listenerContainer, IWorkflowExecuteService workflowExecuteService) {
        super(listenerContainer);
        this.workflowExecuteService = workflowExecuteService;
    }

    @Override
    public void onMessage(Message message, byte[] pattern) {

    }
}
