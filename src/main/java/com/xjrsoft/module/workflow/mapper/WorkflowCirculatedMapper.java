package com.xjrsoft.module.workflow.mapper;

import com.github.yulichang.base.MPJBaseMapper;
import com.xjrsoft.module.workflow.entity.WorkflowCirculated;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xjrsoft.module.workflow.entity.WorkflowSchema;
import org.apache.ibatis.annotations.Mapper;

/**
 * <p>
 * 流程传阅信息表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-09
 */
@Mapper
public interface WorkflowCirculatedMapper extends MPJBaseMapper<WorkflowCirculated> {

}
