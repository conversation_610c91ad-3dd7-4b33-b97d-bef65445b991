package com.xjrsoft.module.workflow.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.xjrsoft.common.constant.GlobalConstant;
import com.xjrsoft.common.model.result.R;
import com.xjrsoft.common.pojo.CommonResult;
import com.xjrsoft.module.workflow.dto.*;
import com.xjrsoft.module.workflow.service.IWorkflowExecuteService;
import com.xjrsoft.module.workflow.service.IWorkflowSchemaService;
import com.xjrsoft.module.workflow.service.MyTaskService;
import com.xjrsoft.module.workflow.utils.WorkFlowUtil;
import com.xjrsoft.module.workflow.vo.ApproveMultiInfoTaskVO;
import com.xjrsoft.module.workflow.vo.ApproveMultiReqVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.camunda.bpm.engine.RuntimeService;
import org.camunda.bpm.engine.TaskService;
import org.camunda.bpm.engine.task.Task;
import org.camunda.bpm.engine.variable.VariableMap;
import org.camunda.bpm.engine.variable.Variables;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * 工作流操作接口
 *
 * @Author: tzx
 * @Date: 2022/9/8 14:15
 */
@Log4j2
@RestController
@RequestMapping(GlobalConstant.WORKFLOW_MODULE_PREFIX + "/execute")
@Api(value = GlobalConstant.WORKFLOW_MODULE_PREFIX + "/execute", tags = "工作流操作接口")
@AllArgsConstructor
public class WorkflowExecuteController {

    private final IWorkflowExecuteService workflowExecuteService;
    private final MyTaskService myTaskService;
    private final RuntimeService runtimeService;
    private final TaskService taskService;
    private final IWorkflowSchemaService workflowSchemaService;


    @PostMapping("/deploy")
    @ApiOperation(value = "部署流程")
    public R deploy(@Valid @RequestBody DeployDto dto) {
        return R.ok(workflowExecuteService.deploy(dto));
    }

    @GetMapping("/preview")
    @ApiOperation(value = "预览流程")
    public R preview(@RequestParam Long schemaId) {
        return R.ok(workflowExecuteService.preview(schemaId));
    }

    @GetMapping("/start-process-info")
    @ApiOperation(value = "发起流程所需要的信息")
    public R startProcessInfo(@RequestParam String schemaId, @RequestParam String id) {

        return R.ok(workflowExecuteService.getStartProcessInfo(schemaId, id));
    }


    @GetMapping("/approve-process-info")
    @ApiOperation(value = "审批流程所需要的信息")
    public R approveProcessInfo(@RequestParam String taskId) {
        return R.ok(workflowExecuteService.getApproveProcessInfo(taskId));
    }

    @GetMapping("/view-process-info")
    @ApiOperation(value = "查看流程所需要的信息（processId获取）")
    public R approveProcessInfoByProcessId(@RequestParam String processId) {
        return R.ok(workflowExecuteService.getApproveProcessInfoByProcessId(processId));
    }

    @GetMapping("/process/all-record")
    @ApiOperation(value = "查看流程所需要的所有流转信息（processId获取，包括父子流程）")
    public R allRecordInfoByProcessId(@RequestParam String processId, @RequestParam(required = false, defaultValue = "1") Integer onlySelf) {
        return R.ok(workflowExecuteService.getAllRecordInfoByProcessId(processId, onlySelf));
    }

    @GetMapping("/recycle-process-info")
    @ApiOperation(value = "获取回收站的数据流程数据")
    public R recycleProcessInfo(@RequestParam String processId) {
        return R.ok(workflowExecuteService.getRecycleProcessInfo(processId));
    }


    @PostMapping("/new-launch")
    @ApiOperation(value = "重构后发起流程")
    public R newLaunch(@Valid @RequestBody LaunchDto dto) {
        return R.ok(workflowExecuteService.newLaunch(dto));
    }

    @PostMapping("/relaunch")
    @ApiOperation(value = "重新发起")
    public R reLaunch(@Valid @RequestBody ReLaunchDto dto) {
        return R.ok(workflowExecuteService.reLaunch(dto));
    }


    @GetMapping("/pending")
    @ApiOperation(value = "待办任务")
    public R pending(@Valid PendingTaskPageDto dto) {
        return R.ok(workflowExecuteService.pending(dto));
    }

    @GetMapping("/task-info")
    @ApiOperation(value = "查询任务详情")
    public R taskInfo(@RequestParam String taskId) {
        return R.ok(workflowExecuteService.getTaskInfo(taskId));
    }

    @GetMapping("/process-info")
    @ApiOperation(value = "查询流程详情")
    public R processInfo(@RequestParam String processId) {
        return R.ok(workflowExecuteService.getProcessInfo(processId));
    }

    @GetMapping("/circulated-task-info")
    @ApiOperation(value = "查询传阅任务详情")
    public R circulatedTaskInfo(@RequestParam String taskId) {
        return R.ok(workflowExecuteService.getCirculatedTaskInfo(taskId));
    }


    @GetMapping("/process/finished-task")
    @ApiOperation(value = "查询当前流程已完成的任务")
    public R finishedTask(@RequestParam String processInstanceId) {
        return R.ok(workflowExecuteService.getFinishedTask(processInstanceId));
    }

    @GetMapping("/process/record")
    @ApiOperation(value = "查询当前流程所有流转记录")
    public R processRecord(@RequestParam String processInstanceId) {
        return R.ok(workflowExecuteService.getProcessRecord(processInstanceId));
    }


    @PostMapping("/new-approve")
    @ApiOperation(value = "重构后发审批")
    public R newApprove(@Valid @RequestBody ApproveDto dto) {
        if (dto.getTaskId() == null) {
            return R.error("请选择任务");
        }
        //查出所有表单数据
        VariableMap variableMap = Variables.createVariables();
        Task task = taskService.createTaskQuery().taskId(dto.getTaskId()).singleResult();
        if (task == null) {
            return R.error("任务不存在");
        }
        Map<String, Object> formData = WorkFlowUtil.getMainFormData(task.getProcessInstanceId());

        variableMap.putAll(formData);
        runtimeService.setVariables(task.getProcessInstanceId(), variableMap);
        Map<String, Map<String, Object>> dtoFormData = dto.getFormData();
        if (dtoFormData != null) {
            dtoFormData.put("formData", formData);
        }

        return R.ok(workflowExecuteService.newApprove(dto));
    }

    @ApiOperation(value = "重启流程")
    @GetMapping("/reboot")
    public CommonResult<String> reboot(@RequestParam String taskId) {
        myTaskService.reboot(taskId);
        return CommonResult.success("");
    }

    @PostMapping("/xs")
    @ApiOperation(value = "销审")
    public R xs(@Valid @RequestBody XsDto dto) {
        return R.ok(workflowExecuteService.xs(dto));
    }


    @GetMapping("/approve/multi-info")
    @ApiOperation(value = "批量审批获取流程信息")
    public R approveMultiInfo(@Valid ApproveMultiInfoDto dto) {
        return R.ok(workflowExecuteService.approveMultiInfo(dto));
    }

    @PostMapping("/v3/approve/multi")
    @ApiOperation(value = "批量审批")
    public R approveMulti(@Valid @RequestBody List<ApproveMultiReqVO> approveMultiReqVOList) {

        List<CompletableFuture<ApproveDto>> futures = new ArrayList<>();

        for (ApproveMultiReqVO dto : approveMultiReqVOList) {
            if (dto.getTaskIdList() == null || dto.getTaskIdList().size() == 0) {
                return R.error("请选择任务");
            }

            for (String taskId : dto.getTaskIdList()) {
                CompletableFuture<ApproveDto> future = CompletableFuture.supplyAsync(() -> {
                    ApproveDto approveDto = BeanUtil.toBean(dto, ApproveDto.class);
                    Task task = taskService.createTaskQuery().taskId(taskId).singleResult();
                    if (task == null) {
                        throw new RuntimeException("任务不存在");
                    }
                    Map<String, Object> formData = WorkFlowUtil.getMainFormData(task.getProcessInstanceId());
                    Map<String, Map<String, Object>> formDataMap = new HashMap<>();
                    formDataMap.put("formData", formData);
                    approveDto.setFormData(formDataMap);
                    approveDto.setTaskId(taskId);
                    approveDto.setYbsp(dto.getYbsp());
                    approveDto.setCirculateConfigs(new ArrayList<>());
                    if (approveDto.getApprovedContent() == null) {
                        approveDto.setApprovedContent("");
                    }
                    VariableMap variableMap = Variables.createVariables();
                    variableMap.putAll(formData);
                    runtimeService.setVariables(task.getProcessInstanceId(), variableMap);
                    return approveDto;
                });
                futures.add(future);
            }
        }

        // 等待所有异步任务完成并收集结果
        List<ApproveDto> approveDtoList = futures.stream()
                .map(CompletableFuture::join)
                .collect(Collectors.toList());

        for (ApproveDto approveDto : approveDtoList) {
            workflowExecuteService.newApprove(approveDto);
        }

        return R.ok();
    }

    @PostMapping("/approve/multi")
    @ApiOperation(value = "批量审批")
    public R approveMulti(@Valid @RequestBody ApproveMultiReqVO dto) {

        if (dto.getTaskIdList() == null || dto.getTaskIdList().size() == 0) {
            return R.error("请选择任务");
        }

        for (String taskId : dto.getTaskIdList()) {
            ApproveDto approveDto = BeanUtil.toBean(dto, ApproveDto.class);
            Task task = taskService.createTaskQuery().taskId(taskId).singleResult();
            if (task == null) {
                return R.error("任务不存在");
            }
            Map<String, Object> formData = WorkFlowUtil.getFormData(task.getProcessInstanceId());
            Map<String, Map<String, Object>> formDataMap = new HashMap<>();
            formDataMap.put("formData", formData);
            approveDto.setFormData(formDataMap);
            approveDto.setTaskId(taskId);
            approveDto.setCirculateConfigs(new ArrayList<>());
            if (approveDto.getApprovedContent() == null) {
                approveDto.setApprovedContent("");
            }
            VariableMap variableMap = Variables.createVariables();
            variableMap.putAll(formData);
            runtimeService.setVariables(task.getProcessInstanceId(), variableMap);
            workflowExecuteService.newApprove(approveDto);
        }
        return R.ok();
    }


    @PostMapping("/getApproveMultiInfo")
    @ApiOperation(value = "获取批量审批的数据")
    public CommonResult<ApproveMultiInfoTaskDTO> getApproveMultiInfo(@Valid @RequestBody List<ApproveMultiInfoTaskVO> taskList) {
        ApproveMultiInfoTaskDTO approveMultiInfoTaskDTO = myTaskService.getApproveMultiInfoTaskDTO(taskList);
        return CommonResult.success(approveMultiInfoTaskDTO);
    }

    @PostMapping("/v2/getApproveMultiInfo")
    @ApiOperation(value = "获取批量审批的数据V2")
    public CommonResult<ApproveMultiInfoTaskDTOV2> getApproveMultiInfoV2(@Valid @RequestBody List<ApproveMultiInfoTaskVO> taskList) {
        ApproveMultiInfoTaskDTO approveMultiInfoTaskDTO = myTaskService.getApproveMultiInfoTaskDTO(taskList);
        ApproveMultiInfoTaskDTOV2 approveMultiInfoTaskDTOV2 = new ApproveMultiInfoTaskDTOV2();
        approveMultiInfoTaskDTOV2.setList(approveMultiInfoTaskDTO.getList());
        approveMultiInfoTaskDTOV2.setNextTaskInfo(myTaskService.setDeptUsers(approveMultiInfoTaskDTO.getNextTaskInfo()));
        return CommonResult.success(approveMultiInfoTaskDTOV2);
    }

    @PostMapping("/v3/getApproveMultiInfo")
    @ApiOperation(value = "获取批量审批的数据V3")
    public CommonResult<List<ApproveMultiInfoTaskDTOV2>> getApproveMultiInfoV3(@Valid @RequestBody List<ApproveMultiInfoTaskVO> taskList) {
        List<CompletableFuture<ApproveMultiInfoTaskDTOV2>> futures = new ArrayList<>();

        for (ApproveMultiInfoTaskVO approveMultiInfoTaskVO : taskList) {
            CompletableFuture<ApproveMultiInfoTaskDTOV2> future = CompletableFuture.supplyAsync(() -> {
                List<ApproveMultiInfoTaskVO> taskListTemp = new ArrayList<>();
                taskListTemp.add(approveMultiInfoTaskVO);
                ApproveMultiInfoTaskDTO approveMultiInfoTaskDTO = myTaskService.getApproveMultiInfoTaskDTO(taskListTemp);
                ApproveMultiInfoTaskDTOV2 approveMultiInfoTaskDTOV2 = new ApproveMultiInfoTaskDTOV2();
                approveMultiInfoTaskDTOV2.setList(approveMultiInfoTaskDTO.getList());
                approveMultiInfoTaskDTOV2.setNextTaskInfo(myTaskService.setDeptUsers(approveMultiInfoTaskDTO.getNextTaskInfo()));
                return approveMultiInfoTaskDTOV2;
            });
            futures.add(future);
        }

        // 等待所有异步任务完成并收集结果
        List<ApproveMultiInfoTaskDTOV2> list = futures.stream()
                .map(CompletableFuture::join)
                .collect(Collectors.toList());

        return CommonResult.success(list);
    }




    @PostMapping("/set-approve")
    @ApiOperation(value = "指定下一节点审批人(覆盖)")
    public R setApproveUser(@Valid @RequestBody ApproveUserDto dto) {
        return R.ok(workflowExecuteService.setApproveUser(dto));
    }

    @PostMapping("/set-approve-multi")
    @ApiOperation(value = "指定下一节点审批人(批量覆盖)")
    public R setApproveUserMulti(@Valid @RequestBody ApproveUserMultiDto dto) {
        return R.ok(workflowExecuteService.setApproveUserMulti(dto));
    }


    @GetMapping("/relation-task/page")
    @ApiOperation(value = "查询流程模板关联的任务分页")
    public R relationTaskPage(@Valid RelationTaskPageDto dto) {
        return R.ok(workflowExecuteService.getRelationTaskPage(dto));
    }

    @GetMapping("/relation-task/info")
    @ApiOperation(value = "查询流程模板关联的任务 详情")
    public R relationTaskPage(@Valid RelationTaskInfoDto dto) {
        return R.ok(workflowExecuteService.getRelationTaskInfo(dto));
    }


    @GetMapping("/circulated/page")
    @ApiOperation(value = "查询我的传阅")
    public R circulatedTaskPage(@Valid CirculatedTaskPageDto dto) {
        return R.ok(workflowExecuteService.getCirculatedTaskPage(dto));
    }

    @GetMapping("/finished/page")
    @ApiOperation(value = "查询我的已办任务分页")
    public R finishedTaskPage(@Valid FinishedTaskPageDto dto) {
        return R.ok(workflowExecuteService.getFinishedTaskPage(dto));
    }

    @GetMapping("/my-process/page")
    @ApiOperation(value = "查询我的流程")
    public R myProcessPage(@Valid MyProcessPageDto dto) {
        return R.ok(workflowExecuteService.getMyProcessPage(dto));
    }

    @PostMapping("/my-process/move-recycle")
    @ApiOperation(value = "我的流程 移入回收站")
    public R moveRecycle(@Valid @RequestBody MoveRecycleDto dto) {
        return R.ok(workflowExecuteService.moveRecycle(dto));
    }

    @GetMapping("/my-process/recycle/page")
    @ApiOperation(value = "回收站列表")
    public R recycleProcessPage(@Valid RecycleProcessPageDto dto) {
        return R.ok(workflowExecuteService.getRecycleProcessPage(dto));
    }

    @DeleteMapping("/my-process/recycle")
    @ApiOperation(value = "回收站 删除")
    public R recycleDelete(@Valid @RequestBody RecycleDeleteDto dto) {
        return R.ok(workflowExecuteService.recycleDelete(dto));
    }


    @PostMapping("/my-process/recycle/restart")
    @ApiOperation(value = "回收站 重新发起")
    public R restart(@Valid @RequestBody RestartDto dto) {
        return R.ok(workflowExecuteService.restart(dto));
    }


    @GetMapping("/my-task/history-task")
    @ApiOperation(value = "我的流程 已经完成的任务（不包括 外部流程  子流程  会签流程）")
    public R historyTask(@RequestParam String schemaId, @RequestParam String processInstanceId) {
        return R.ok(workflowExecuteService.getHistoryTask(schemaId, processInstanceId));
    }

    @PostMapping("/my-task/withdraw")
    @ApiOperation(value = "我的流程 撤回")
    public R withdraw(@Valid @RequestBody WithdrawDto dto) {
        return R.ok(workflowExecuteService.withdraw(dto));
    }

    @PostMapping("/my-task/multi/withdraw")
    @ApiOperation(value = "我的流程批量撤回")
    public R multiWithdraw(@Valid @RequestBody List<WithdrawDto> dto) {
        for (WithdrawDto withdrawDto : dto) {
            workflowExecuteService.withdraw(withdrawDto);
        }
        return R.ok();
    }


    @PostMapping("/v2/my-task/withdraw")
    @ApiOperation(value = "我的流程 撤回")
    public R withdrawV2(@Valid @RequestBody WithdrawDto dto) {
        workflowExecuteService.canWithdraw(dto);
        ybch(dto);
        return R.ok();
    }

    @PostMapping("/v2/my-task/multi/withdraw")
    @ApiOperation(value = "我的流程批量撤回")
    public R multiWithdrawV2(@Valid @RequestBody List<WithdrawDto> dto) {

        for (WithdrawDto withdrawDto : dto) {
            workflowExecuteService.canWithdraw(withdrawDto);
            ybch(withdrawDto);
        }
        return R.ok();
    }

    private static void ybch(WithdrawDto dto) {
        HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
        // 获取协议
        String scheme = request.getScheme();
        // 获取端口
        int serverPort = request.getServerPort();
        // 构建完整的URL
        String fullUrl = scheme + "://localhost" + ":" + serverPort;
        String authorization = request.getHeader("hzhltoken");

        WorkFlowUtil.updateShowStatus(dto.getProcessId(), "0");
        log.info("异步撤回：" + fullUrl);
        new Thread(() -> {
            String body = HttpUtil.createPost(fullUrl + "/workflow/execute/my-task/withdraw")
                    .header("hzhltoken", authorization)
                    .body(JSONUtil.toJsonStr(dto))
                    .execute()
                    .body();
            log.info("异步撤回结果：" + body);

        }).start();
    }


    @PostMapping("/draft")
    @ApiOperation(value = "保存草稿")
    public R addDraft(@Valid @RequestBody SaveDraftDto dto) throws JsonProcessingException {
        return R.ok(workflowExecuteService.saveDraft(dto));
    }


    @PutMapping("/draft")
    @ApiOperation(value = "修改草稿")
    public R updateDraft(@Valid @RequestBody UpdateDraftDto dto) {
        return R.ok(workflowExecuteService.updateDraft(dto));
    }

    @GetMapping("/draft/page")
    @ApiOperation(value = "查询草稿列表分页")
    public R draftPage(@Valid DraftPageDto dto) {
        return R.ok(workflowExecuteService.draftPage(dto));
    }

    @GetMapping("/draft/info")
    @ApiOperation(value = "查询草稿详情")
    public R draftInfo(@RequestParam Long id) {
        return R.ok(workflowExecuteService.draftData(id));
    }

    @DeleteMapping("/draft")
    @ApiOperation(value = "删除草稿")
    public R deleteDraft(@RequestBody List<Long> ids) {
        return R.ok(workflowExecuteService.deleteDraft(ids));
    }

    @GetMapping("/process-monitor/page")
    @ApiOperation(value = "流程监控分页列表")
    public R processMonitorPage(@Valid MonitorPageDto dto) {
        return R.ok(workflowExecuteService.getProcessMonitorPage(dto));
    }

    @DeleteMapping("/process-monitor/delete")
    @ApiOperation(value = "删除流程（流程监控使用）")
    public R deleteProcessMonitor(@RequestBody DeleteMonitorDto dto) {
        return R.ok(workflowExecuteService.deleteProcessMonitor(dto));
    }

    @PostMapping("/set-assignee")
    @ApiOperation(value = "指派审核人（给任务添加审批人）")
    public R setAssignee(@Valid @RequestBody SetAssigneeDto dto) {
        return R.ok(workflowExecuteService.setAssignee(dto));
    }

    @PostMapping("/set-suspended")
    @ApiOperation(value = "将流程挂起")
    public R setSuspended(@Valid @RequestBody SetSuspendedDto dto) {
        return R.ok(workflowExecuteService.setSuspended(dto));
    }

    @GetMapping("/approve-user")
    @ApiOperation(value = "获取节点审批人（获取下一节点审批人也是这个接口）")
    public R getAssignee(@Valid GetAssigneeDto dto) {
        return R.ok(workflowExecuteService.getAssignee(dto));
    }

    @PostMapping("/set-sign")
    @ApiOperation(value = "加签/减签")
    public R addOrSubSign(@Valid @RequestBody AddOrSubSignDto dto) {
        return R.ok(workflowExecuteService.addOrSubSign(dto));
    }

    @GetMapping("/reject-node")
    @ApiOperation(value = "获取可以驳回的节点")
    public R rejectNode(@Valid RejectNodeDto dto) {
        return R.ok(workflowExecuteService.getRejectNode(dto));
    }

    @PostMapping("/transfer")
    @ApiOperation(value = "转办")
    public R transfer(@Valid @RequestBody TransferDto dto) {
        return R.ok(workflowExecuteService.transfer(dto));
    }


    @GetMapping("/process/form-finished-task")
    @ApiOperation(value = "根据formId  查询流程图 以及  当前流程已完成的任务")
    public R formFinishedTask(FormFinishedTaskDto dto) {
        return R.ok(workflowExecuteService.getFormFinishedTask(dto));
    }


    @GetMapping("/count")
    @ApiOperation(value = "合计")
    public R count() {
        return R.ok(workflowExecuteService.getCount());
    }


}
