package com.xjrsoft.module.workflow.controller;

import com.xjrsoft.common.constant.GlobalConstant;
import io.swagger.annotations.Api;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 流程模板权限 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-04
 */
@RestController
@RequestMapping(GlobalConstant.WORKFLOW_MODULE_PREFIX + "/schema-auth")
@Api(value = GlobalConstant.WORKFLOW_MODULE_PREFIX + "/schema-auth", tags = "流程模板权限")
@AllArgsConstructor
public class WorkflowSchemaAuthController {

}
