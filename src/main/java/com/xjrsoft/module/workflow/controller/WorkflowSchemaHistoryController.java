package com.xjrsoft.module.workflow.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.yulichang.toolkit.MPJWrappers;
import com.xjrsoft.common.constant.GlobalConstant;
import com.xjrsoft.common.enums.EnabledMark;
import com.xjrsoft.common.model.result.R;
import com.xjrsoft.common.page.ConventPage;
import com.xjrsoft.common.page.PageOutput;
import com.xjrsoft.common.utils.VoToColumnUtil;
import com.xjrsoft.module.form.entity.FormHistory;
import com.xjrsoft.module.form.vo.FormHistoryListVo;
import com.xjrsoft.module.organization.entity.User;
import com.xjrsoft.module.system.entity.Datasource;
import com.xjrsoft.module.system.entity.DictionaryDetail;
import com.xjrsoft.module.workflow.dto.HistoryChangeDto;
import com.xjrsoft.module.workflow.dto.WorkflowSchemaPageDto;
import com.xjrsoft.module.workflow.entity.WorkflowSchema;
import com.xjrsoft.module.workflow.entity.WorkflowSchemaHistory;
import com.xjrsoft.module.workflow.service.IWorkflowSchemaHistoryService;
import com.xjrsoft.module.workflow.service.IWorkflowSchemaService;
import com.xjrsoft.module.workflow.vo.SchemaHistoryListVo;
import com.xjrsoft.module.workflow.vo.WorkflowSchemaPageVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;
import org.springframework.stereotype.Controller;

import javax.validation.Valid;
import java.util.List;

/**
 * <p>
 * 流程模板历史记录表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-16
 */
@RestController
@RequestMapping(GlobalConstant.WORKFLOW_MODULE_PREFIX + "/schema-history")
@Api(value = GlobalConstant.WORKFLOW_MODULE_PREFIX + "/schema-history", tags = "流程模板历史记录接口")
@AllArgsConstructor
public class WorkflowSchemaHistoryController {

    private final IWorkflowSchemaHistoryService workflowSchemaHistoryService;


    @GetMapping("/list")
    @ApiOperation(value = "流程列表（分页）")
    public R list(@RequestParam Long schemaId) {
        List<SchemaHistoryListVo> schemaHistoryListVos = workflowSchemaHistoryService.selectJoinList(SchemaHistoryListVo.class,
                MPJWrappers.<WorkflowSchemaHistory>lambdaJoin()
                        .eq(WorkflowSchemaHistory::getSchemaId, schemaId)
                        .select(WorkflowSchemaHistory::getId)
                        .select(WorkflowSchemaHistory.class, x -> VoToColumnUtil.fieldsToColumns(SchemaHistoryListVo.class).contains(x.getProperty()))
                        .selectAs(User::getName, SchemaHistoryListVo::getCreateUserName)
                        .leftJoin(User.class, User::getId, WorkflowSchemaHistory::getCreateUserId)
                        .orderByAsc(WorkflowSchemaHistory::getVersion)
        );

        List<SchemaHistoryListVo> listVos = BeanUtil.copyToList(schemaHistoryListVos, SchemaHistoryListVo.class);
        return R.ok(listVos);
    }

    @PutMapping("/set-current")
    @ApiOperation(value = "更新到此版本")
    public R change(@Valid @RequestBody HistoryChangeDto dto) {

        return R.ok(workflowSchemaHistoryService.change(dto));
    }

    @PutMapping("/processChanges")
    @ApiOperation(value = "新流程变更")
    public R processChanges(@Valid @RequestBody HistoryChangeDto dto) {
        return R.ok(workflowSchemaHistoryService.change(dto));
    }

    @GetMapping("/preview")
    @ApiOperation(value = "历史记录的xml")
    public R preview(@RequestParam String historyId){
        WorkflowSchemaHistory history = workflowSchemaHistoryService.getById(historyId);
        return R.ok(history.getXmlContent());
    }
}
