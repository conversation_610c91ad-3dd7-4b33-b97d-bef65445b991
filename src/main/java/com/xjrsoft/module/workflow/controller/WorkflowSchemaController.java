package com.xjrsoft.module.workflow.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.convert.ConvertException;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.xjrsoft.common.constant.GlobalConstant;
import com.xjrsoft.common.enums.EnabledMark;
import com.xjrsoft.common.model.result.R;
import com.xjrsoft.common.pojo.CommonResult;
import com.xjrsoft.common.utils.ContextUserHolder;
import com.xjrsoft.module.organization.entity.User;
import com.xjrsoft.module.organization.service.IUserService;
import com.xjrsoft.module.workflow.constant.WorkflowConstant;
import com.xjrsoft.module.workflow.dto.*;
import com.xjrsoft.module.workflow.entity.WorkflowSchema;
import com.xjrsoft.module.workflow.model.UserTaskConfig;
import com.xjrsoft.module.workflow.model.WorkflowSchemaConfig;
import com.xjrsoft.module.workflow.service.IWorkflowRecordService;
import com.xjrsoft.module.workflow.service.IWorkflowSchemaService;
import com.xjrsoft.module.workflow.service.MyTaskService;
import com.xjrsoft.module.workflow.utils.WorkFlowUtil;
import com.xjrsoft.module.workflow.vo.GetNodeInfoByNodeIdReqVO;
import com.xjrsoft.module.workflow.vo.GetNodeInfoReqVO;
import com.xjrsoft.module.workflow.vo.GetStartNodeInfoVO;
import com.xjrsoft.module.workflow.vo.WorkflowSchemaInfoVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.camunda.bpm.engine.HistoryService;
import org.camunda.bpm.engine.RuntimeService;
import org.camunda.bpm.engine.TaskService;
import org.camunda.bpm.engine.history.HistoricTaskInstance;
import org.camunda.bpm.engine.history.HistoricVariableInstance;
import org.camunda.bpm.engine.task.Task;
import org.camunda.bpm.model.bpmn.instance.FlowNode;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * <p>
 * 流程模板表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-04
 */
@Log4j2
@RestController
@RequestMapping(GlobalConstant.WORKFLOW_MODULE_PREFIX + "/schema")
@Api(value = GlobalConstant.WORKFLOW_MODULE_PREFIX + "/schema", tags = "单据设计器流程模板")
@AllArgsConstructor
public class WorkflowSchemaController {

    private final IWorkflowSchemaService workflowSchemaService;
    private final MyTaskService myTaskService;
    private final RuntimeService runtimeService;
    private final IUserService userService;
    private final HistoryService historyService;
    private final TaskService taskService;
    private final IWorkflowRecordService iWorkflowRecordService;


    @GetMapping("/page")
    @ApiOperation(value = "流程列表（分页）")
    public R page(@Valid WorkflowSchemaPageDto dto) {
        return R.ok(workflowSchemaService.getPage(dto));
    }


    @PostMapping
    @ApiOperation(value = "新增流程模板")
    public R add(@Valid @RequestBody AddWorkflowSchemaDto dto) {
        return R.ok(workflowSchemaService.add(dto));
    }

    @PostMapping("/draft")
    @ApiOperation(value = "新增流程模板草稿")
    public R draft(@Valid @RequestBody AddWorkflowSchemaDraftDto dto) {
        return R.ok(workflowSchemaService.addDraft(dto));
    }

    @GetMapping("/draft")
    @ApiOperation(value = "流程模板草稿")
    public R draftPage(@Valid WorkflowSchemaDraftPageDto dto) {
        return R.ok(workflowSchemaService.getDraftPage(dto));
    }


    @PutMapping
    @ApiOperation(value = "修改流程模板")
    public R update(@Valid @RequestBody UpdateWorkflowSchemaDto dto) {
        return R.ok(workflowSchemaService.update(dto));
    }


    @GetMapping("/info")
    @ApiOperation(value = "获取流程详细信息")
    public R info(@RequestParam Long id) {
        WorkflowSchema workflowSchema = workflowSchemaService.getById(id);
        if (workflowSchema == null) {
            return R.error("找不到此模板信息！");
        }
        WorkflowSchemaInfoVo bean = BeanUtil.toBean(workflowSchema, WorkflowSchemaInfoVo.class);

        return R.ok(bean);
    }

    @GetMapping("/multi/info")
    @ApiOperation(value = "获取批量流程详细信息")
    public R multiInfo(@RequestParam String ids) {
        List<String> idList = Arrays.stream(ids.split(StringPool.COMMA)).collect(Collectors.toList());

        List<WorkflowSchema> workflowSchemas = workflowSchemaService.listByIds(idList);

        return R.ok(BeanUtil.copyToList(workflowSchemas, WorkflowSchemaInfoVo.class));
    }


    @DeleteMapping
    @ApiOperation(value = "删除模板(可批量)")
    public R delete(@RequestBody List<Long> ids) {

        return R.ok(workflowSchemaService.delete(ids));
    }

    @PutMapping("/enabled")
    @ApiOperation(value = "流程启用")
    public R enabled(@Valid @RequestBody EnabledDto dto) {
        WorkflowSchema schema = new WorkflowSchema();
        schema.setId(dto.getSchemaId());
        schema.setEnabledMark(EnabledMark.ENABLED.getCode());
        return R.ok(workflowSchemaService.updateById(schema));
    }

    @PutMapping("/disabled")
    @ApiOperation(value = "流程禁用")
    public R disabled(@Valid @RequestBody EnabledDto dto) {
        WorkflowSchema schema = new WorkflowSchema();
        schema.setId(dto.getSchemaId());
        schema.setEnabledMark(EnabledMark.DISABLED.getCode());
        return R.ok(workflowSchemaService.updateById(schema));
    }

    @GetMapping("/export")
    @ApiOperation(value = "导出流程")
    @SneakyThrows
    public R export(@RequestParam Long id) {
        WorkflowSchema workflowSchema = workflowSchemaService.getById(id);
        if (workflowSchema == null) {
            return R.error("找不到此模板信息！");
        }
        WorkflowSchemaConfig workflowSchemaConfig = JSONUtil.toBean(workflowSchema.getJsonContent(), WorkflowSchemaConfig.class);
        workflowSchemaConfig.getProcessConfig().setXmlContent(workflowSchema.getXmlContent());

        return R.ok(JSONUtil.toJsonStr(workflowSchemaConfig));
    }

    @PostMapping("/import")
    @ApiOperation(value = "导入流程")
    @SneakyThrows
    public R importSchema(@RequestParam(value = "file") MultipartFile multipartFile) {
        return R.ok(workflowSchemaService.importSchema(multipartFile));
    }

    @GetMapping("/getNextTaskInfo")
    @ApiOperation(value = "获取下一个节点信息")
    public CommonResult<NextTaskInfoDTO> getNextTaskInfo(@RequestParam String processInstanceId) {
        NextTaskInfoDTO nextTaskInfoDTO = myTaskService.getNextTaskInfoDTO(processInstanceId);
        return CommonResult.success(nextTaskInfoDTO);
    }

    @GetMapping("/v2/getNextTaskInfo")
    @ApiOperation(value = "获取下一个节点信息V2")
    public CommonResult<NextTaskInfoDTOV2> getNextTaskInfoV2(@RequestParam String processInstanceId) {
        NextTaskInfoDTO nextTaskInfoDTO = myTaskService.getNextTaskInfoDTO(processInstanceId);
        return CommonResult.success(myTaskService.setDeptUsers(nextTaskInfoDTO));
    }

    @GetMapping("/v3/getNextTaskInfo")
    @ApiOperation(value = "获取下一个节点信息")
    public CommonResult<List<NextTaskInfoDTOV2>> getNextTaskInfoV3(@RequestParam String processInstanceId) {
        List<String> processInstanceIds = Arrays.asList(processInstanceId.split(","));
        List<CompletableFuture<NextTaskInfoDTOV2>> futures = new ArrayList<>();

        for (String instanceId : processInstanceIds) {
            CompletableFuture<NextTaskInfoDTOV2> future = CompletableFuture.supplyAsync(() -> myTaskService.getNextTaskInfoDTO(instanceId))
                    .thenApply(nextTaskInfoDTO -> myTaskService.setDeptUsers(nextTaskInfoDTO));
            futures.add(future);
        }

        CompletableFuture<Void> allFutures = CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]));
        CompletableFuture<List<NextTaskInfoDTOV2>> resultFuture = allFutures.thenApply(v ->
                futures.stream()
                        .map(CompletableFuture::join)
                        .collect(Collectors.toList()));

        return CommonResult.success(resultFuture.join());
    }


    @PostMapping("/getStartNodeInfo")
    @ApiOperation(value = "获取启动节点信息")
    public CommonResult<NextTaskInfoDTO> getStartNodeInfo(@RequestBody GetStartNodeInfoVO getStartNodeInfoVO) {
        WorkflowSchema workflowSchema = workflowSchemaService.getById(getStartNodeInfoVO.getWorkFlowId());
        FlowNode eventStartNode = myTaskService.getNextFlowNode(workflowSchema, workflowSchema.getDefinitionId(), "Event_start_node", getStartNodeInfoVO.getFormData());
        return CommonResult.success(myTaskService.setUsers(eventStartNode, getStartNodeInfoVO));
    }

    @PostMapping("/v2/getStartNodeInfo")
    @ApiOperation(value = "获取启动节点信息V2")
    public CommonResult<NextTaskInfoDTOV2> getStartNodeInfoV2(@RequestBody GetStartNodeInfoVO getStartNodeInfoVO) {
        WorkflowSchema workflowSchema = workflowSchemaService.getById(getStartNodeInfoVO.getWorkFlowId());
        FlowNode eventStartNode = myTaskService.getNextFlowNode(workflowSchema, workflowSchema.getDefinitionId(), "Event_start_node", getStartNodeInfoVO.getFormData());
        NextTaskInfoDTO nextTaskInfoDTO = myTaskService.setUsers(eventStartNode, getStartNodeInfoVO);
        if (nextTaskInfoDTO != null) {
            //获取到整个流程模板的配置
            try {
                WorkflowSchemaConfig workflowSchemaConfig = JSONUtil.toBean(workflowSchema.getJsonContent(), WorkflowSchemaConfig.class);
                Optional<Map<String, Object>> userTaskConfigOp = workflowSchemaConfig.getChildNodeConfig().stream().filter(c -> c.containsValue(nextTaskInfoDTO.getTaskId())).findFirst();
                if (userTaskConfigOp != null && userTaskConfigOp.isPresent()) {
                    UserTaskConfig userTaskConfig = Convert.convert(UserTaskConfig.class, userTaskConfigOp.get());
                    nextTaskInfoDTO.setUserTaskConfig(userTaskConfig);
                }
            } catch (ConvertException e) {
                e.printStackTrace();
                log.error("流程配置转换异常", e);
            }
        }
        return CommonResult.success(myTaskService.setDeptUsers(nextTaskInfoDTO));
    }

    @PostMapping("/getAllFlowNode")
    @ApiOperation(value = "getAllFlowNode")
    public CommonResult<List<NextTaskInfoDTO>> getAllFlowNode(@RequestBody GetAllFlowNodeDTO getAllFlowNodeDTO) {

        if (getAllFlowNodeDTO.getFormData() == null || getAllFlowNodeDTO.getFormData().isEmpty()) {
            getAllFlowNodeDTO.setFormData(WorkFlowUtil.getMainFormDataByID(getAllFlowNodeDTO.getId()));
        }
        List<FlowNode> allFlowNode = myTaskService.getAllFlowNode(getAllFlowNodeDTO);
        List<NextTaskInfoDTO> nextTaskInfoDTOS = new ArrayList<>();
        WorkflowSchema workflowSchema = workflowSchemaService.getById(getAllFlowNodeDTO.getWorkFlowId());
        WorkflowSchemaConfig workflowSchemaConfig = JSONUtil.toBean(workflowSchema.getJsonContent(), WorkflowSchemaConfig.class);
        for (FlowNode flowNode : allFlowNode) {
            NextTaskInfoDTO nextTaskInfoDTO = new NextTaskInfoDTO();
            nextTaskInfoDTO.setTaskId(flowNode.getId());
            nextTaskInfoDTO.setTaskName(flowNode.getName());
            if (StringUtils.isNotEmpty(getAllFlowNodeDTO.getTaskId())) {
                Task task = taskService.createTaskQuery().taskId(getAllFlowNodeDTO.getTaskId()).singleResult();
                if (task != null) {
                    if (flowNode.getId().equals(task.getTaskDefinitionKey())) {
                        String assignee = task.getAssignee();
                        if (StringUtils.isNotEmpty(assignee)) {
                            for (String s : assignee.split(",")) {
                                User user = userService.getById(s);
                                if (user != null) {
                                    nextTaskInfoDTO.getUsers().add(user);
                                }
                            }
                        }
                    }
                }
            }
            if (nextTaskInfoDTO.getUsers() != null
                    && nextTaskInfoDTO.getUsers().isEmpty()) {
                Optional<Map<String, Object>> userTaskConfigOp = workflowSchemaConfig.getChildNodeConfig().stream().filter(c -> c.containsValue(flowNode.getId())).findFirst();
                if (userTaskConfigOp.isPresent()) {
                    UserTaskConfig userTaskConfig = Convert.convert(UserTaskConfig.class, userTaskConfigOp.get());
                    List<String> approveUserIds = WorkFlowUtil.getUserIdsByMemberConfig(userTaskConfig.getApproverConfigs(), workflowSchemaConfig.getChildNodeConfig(), ContextUserHolder.getContext(), getAllFlowNodeDTO.getFormData());
                    if (approveUserIds.size() > 0) {
                        approveUserIds.forEach(userId -> {
                            User user = userService.getById(userId);
                            if (user != null) {
                                nextTaskInfoDTO.getUsers().add(user);
                            }
                        });
                    }
                }
            }


            nextTaskInfoDTOS.add(nextTaskInfoDTO);
        }
        return CommonResult.success(nextTaskInfoDTOS);
    }

    @PostMapping("/getNodeInfo")
    @ApiOperation(value = "获取节点信息")
    public CommonResult<UserTaskConfig> getNodeInfo(@RequestBody GetNodeInfoReqVO getNodeInfoReqVO) {
        HistoricVariableInstance historicVariableInstance = historyService.createHistoricVariableInstanceQuery().processInstanceId(getNodeInfoReqVO.getProcessId()).variableName(WorkflowConstant.PROCESS_SCHEMA_ID_KEY).singleResult();

        //排除xml 查出数据
        WorkflowSchema workflowSchema = workflowSchemaService.getOne(Wrappers.lambdaQuery(WorkflowSchema.class).eq(WorkflowSchema::getId, historicVariableInstance.getValue()));

        //获取到整个流程模板的配置
        WorkflowSchemaConfig workflowSchemaConfig = JSONUtil.toBean(workflowSchema.getJsonContent(), WorkflowSchemaConfig.class);

        String taskDefinitionKey;
        Optional<Map<String, Object>> userTaskConfigOp = null;
        if (ObjectUtil.isNotNull(getNodeInfoReqVO.getTaskId())) {
            HistoricTaskInstance historicTaskInstance = historyService.createHistoricTaskInstanceQuery().taskId(getNodeInfoReqVO.getTaskId()).singleResult();
            taskDefinitionKey = historicTaskInstance.getTaskDefinitionKey();
            userTaskConfigOp = workflowSchemaConfig.getChildNodeConfig().stream().filter(c -> c.containsValue(taskDefinitionKey)).findFirst();
        }
        if (userTaskConfigOp != null && userTaskConfigOp.isPresent()) {
            UserTaskConfig userTaskConfig = Convert.convert(UserTaskConfig.class, userTaskConfigOp.get());
            return CommonResult.success(userTaskConfig);
        }

        return CommonResult.success(new UserTaskConfig());
    }

    @PostMapping("/getNodeInfoByNodeId")
    @ApiOperation(value = "获取节点信息，开始节点默认传 Event_start_node")
    public CommonResult<UserTaskConfig> getNodeInfoByNodeId(@RequestBody GetNodeInfoByNodeIdReqVO getNodeInfoByNodeIdReqVO) {
        //排除xml 查出数据
        WorkflowSchema workflowSchema = workflowSchemaService.getOne(Wrappers.lambdaQuery(WorkflowSchema.class).eq(WorkflowSchema::getId, getNodeInfoByNodeIdReqVO.getModuleId()));
        //获取到整个流程模板的配置
        WorkflowSchemaConfig workflowSchemaConfig = JSONUtil.toBean(workflowSchema.getJsonContent(), WorkflowSchemaConfig.class);
        Optional<Map<String, Object>> userTaskConfigOp = workflowSchemaConfig.getChildNodeConfig().stream().filter(c -> c.containsValue(getNodeInfoByNodeIdReqVO.getNodeId())).findFirst();
        if (userTaskConfigOp != null && userTaskConfigOp.isPresent()) {
            UserTaskConfig userTaskConfig = Convert.convert(UserTaskConfig.class, userTaskConfigOp.get());
            return CommonResult.success(userTaskConfig);
        }

        return CommonResult.success(new UserTaskConfig());
    }

}
