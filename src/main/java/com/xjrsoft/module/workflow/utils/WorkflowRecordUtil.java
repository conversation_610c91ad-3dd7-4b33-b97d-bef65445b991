package com.xjrsoft.module.workflow.utils;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.xjrsoft.common.enums.WorkflowMultiInstanceType;
import com.xjrsoft.common.utils.WorkFlowUtils;
import com.xjrsoft.module.workflow.constant.WorkflowConstant;
import com.xjrsoft.module.workflow.entity.WorkflowRecord;
import com.xjrsoft.module.workflow.entity.WorkflowSchema;
import com.xjrsoft.module.workflow.mapper.WorkflowRecordMapper;
import com.xjrsoft.module.workflow.model.StartNodeConfig;
import com.xjrsoft.module.workflow.service.IWorkflowRecordService;
import org.camunda.bpm.engine.TaskService;
import org.camunda.bpm.engine.runtime.ProcessInstance;
import org.camunda.bpm.engine.task.Task;

import java.time.LocalDateTime;
import java.util.Date;

/**
 * WorkflowRecord 记录工具类
 * 统一处理工作流记录的创建和保存逻辑
 *
 * <AUTHOR>
 * @since 2025-01-08
 */
public class WorkflowRecordUtil {

    /**
     * 创建并保存基础的工作流记录
     *
     * @param task 任务对象
     * @param schemaId 模板ID
     * @param message 消息内容
     * @return 创建的记录对象
     */
    public static WorkflowRecord createAndSaveRecord(Task task, Long schemaId, String message) {
        WorkflowRecord record = createBaseRecord(task, schemaId);
        record.setMessage(message);
        record.setRecordTime(LocalDateTime.now());
        saveRecord(record);
        return record;
    }

    /**
     * 创建并保存基础的工作流记录（通过taskId）
     *
     * @param taskId 任务ID
     * @param message 消息内容
     * @return 创建的记录对象
     */
    public static WorkflowRecord createAndSaveRecord(String taskId, String message) {
        TaskService taskService = SpringUtil.getBean(TaskService.class);
        Task task = taskService.createTaskQuery().taskId(taskId).singleResult();
        Object schemaId = taskService.getVariable(taskId, WorkflowConstant.PROCESS_SCHEMA_ID_KEY);

        WorkflowRecord record = createBaseRecord(task, Convert.toLong(schemaId));
        record.setMessage(message);
        record.setRecordTime(LocalDateTime.now());
        saveRecord(record);
        return record;
    }

    /**
     * 创建并保存审批记录
     *
     * @param task 任务对象
     * @param schemaId 模板ID
     * @param message 消息内容
     * @param approvedType 审批类型
     * @param approvedContent 审批内容
     * @param recordTime 记录时间
     * @return 创建的记录对象
     */
    public static WorkflowRecord createAndSaveApprovalRecord(Task task, Long schemaId, String message,
                                                           String approvedType, String approvedContent,
                                                           LocalDateTime recordTime) {
        WorkflowRecord record = createBaseRecord(task, schemaId);
        record.setMessage(message);
        record.setSpjg(approvedType);
        record.setSpyj(approvedContent);
        record.setRecordTime(recordTime != null ? recordTime : LocalDateTime.now());
        saveRecord(record);
        return record;
    }

    /**
     * 创建并保存审批记录（支持Date类型时间）
     *
     * @param task 任务对象
     * @param schemaId 模板ID
     * @param message 消息内容
     * @param approvedType 审批类型
     * @param approvedContent 审批内容
     * @param recordTime 记录时间（Date类型）
     * @return 创建的记录对象
     */
    public static WorkflowRecord createAndSaveApprovalRecord(Task task, Long schemaId, String message,
                                                           String approvedType, String approvedContent,
                                                           Date recordTime) {
        LocalDateTime localDateTime = recordTime != null ? DateUtil.toLocalDateTime(recordTime) : LocalDateTime.now();
        return createAndSaveApprovalRecord(task, schemaId, message, approvedType, approvedContent, localDateTime);
    }

    /**
     * 创建并保存开始节点记录
     *
     * @param workflowSchema 工作流模板
     * @param startNodeConfig 开始节点配置
     * @param processInstance 流程实例
     * @param message 消息内容
     * @return 创建的记录对象
     */
    public static WorkflowRecord createAndSaveStartRecord(WorkflowSchema workflowSchema,
                                                        StartNodeConfig startNodeConfig,
                                                        ProcessInstance processInstance,
                                                        String message) {
        WorkflowRecord record = new WorkflowRecord();
        record.setNodeId(startNodeConfig.getId());
        record.setNodeName(startNodeConfig.getName());
        record.setNodeType(WorkflowConstant.START_EVENT_TYPE_NAME);
        record.setProcessId(processInstance.getId());
        record.setSchemaId(workflowSchema.getId());
        record.setNodeMultiType(WorkflowMultiInstanceType.NONE.getCode());
        record.setRecordTime(LocalDateTime.now());
        record.setMessage(message);
        record.setBizId(WorkFlowUtils.getBizIdByProcessId(processInstance.getId()));

        saveRecord(record);
        return record;
    }

    /**
     * 创建并保存开始节点记录（支持时间偏移）
     *
     * @param workflowSchema 工作流模板
     * @param startNodeConfig 开始节点配置
     * @param processInstanceId 流程实例ID
     * @param message 消息内容
     * @param timeOffset 时间偏移（秒）
     * @return 创建的记录对象
     */
    public static WorkflowRecord createAndSaveStartRecord(WorkflowSchema workflowSchema,
                                                        StartNodeConfig startNodeConfig,
                                                        String processInstanceId,
                                                        String message,
                                                        long timeOffset) {
        WorkflowRecord record = new WorkflowRecord();
        record.setNodeId(startNodeConfig.getId());
        record.setNodeName(startNodeConfig.getName());
        record.setNodeType(WorkflowConstant.START_EVENT_TYPE_NAME);
        record.setProcessId(processInstanceId);
        record.setSchemaId(workflowSchema.getId());
        record.setNodeMultiType(WorkflowMultiInstanceType.NONE.getCode());
        record.setRecordTime(LocalDateTime.now().minusSeconds(timeOffset));
        record.setMessage(message);
        record.setBizId(WorkFlowUtils.getBizIdByProcessId(processInstanceId));

        saveRecord(record);
        return record;
    }

    /**
     * 创建基础的工作流记录对象（不保存）
     *
     * @param task 任务对象
     * @param schemaId 模板ID
     * @return 创建的记录对象
     */
    public static WorkflowRecord createBaseRecord(Task task, Long schemaId) {
        if (task == null) {
            throw new IllegalArgumentException("Task cannot be null");
        }

        WorkflowRecord record = new WorkflowRecord();
        record.setNodeId(task.getId());
        record.setTaskDefKey(task.getTaskDefinitionKey());
        record.setNodeName(task.getName());
        record.setNodeType(WorkflowConstant.USER_TASK_TYPE_NAME);
        record.setProcessId(task.getProcessInstanceId());
        record.setSchemaId(schemaId);
        record.setNodeMultiType(WorkflowMultiInstanceType.NONE.getCode());
        record.setBizId(WorkFlowUtils.getBizIdByProcessId(task.getProcessInstanceId()));

        return record;
    }

    /**
     * 创建基础的工作流记录对象（通过节点信息）
     *
     * @param nodeId 节点ID
     * @param nodeName 节点名称
     * @param nodeType 节点类型
     * @param processId 流程ID
     * @param schemaId 模板ID
     * @return 创建的记录对象
     */
    public static WorkflowRecord createBaseRecord(String nodeId, String nodeName, String nodeType,
                                                String processId, Long schemaId) {
        WorkflowRecord record = new WorkflowRecord();
        record.setNodeId(nodeId);
        record.setNodeName(nodeName);
        record.setNodeType(nodeType);
        record.setProcessId(processId);
        record.setSchemaId(schemaId);
        record.setNodeMultiType(WorkflowMultiInstanceType.NONE.getCode());
        record.setBizId(WorkFlowUtils.getBizIdByProcessId(processId));

        return record;
    }

    /**
     * 保存工作流记录
     *
     * @param record 记录对象
     */
    public static void saveRecord(WorkflowRecord record) {
        if (record == null) {
            throw new IllegalArgumentException("WorkflowRecord cannot be null");
        }

        IWorkflowRecordService workflowRecordService = SpringUtil.getBean(IWorkflowRecordService.class);
        workflowRecordService.saveOrUpdate(record);
    }

    /**
     * 直接插入工作流记录（使用 Mapper）
     *
     * @param record 记录对象
     */
    public static void insertRecord(WorkflowRecord record) {
        if (record == null) {
            throw new IllegalArgumentException("WorkflowRecord cannot be null");
        }

        WorkflowRecordMapper workflowRecordMapper = SpringUtil.getBean(WorkflowRecordMapper.class);

        if (record.getId() != null) {
            workflowRecordMapper.updateById(record);
        }else {
            workflowRecordMapper.insert(record);
        }
    }

    /**
     * 批量保存工作流记录
     *
     * @param records 记录对象列表
     */
    public static void saveRecords(java.util.List<WorkflowRecord> records) {
        if (ObjectUtil.isEmpty(records)) {
            return;
        }

        IWorkflowRecordService workflowRecordService = SpringUtil.getBean(IWorkflowRecordService.class);
        workflowRecordService.saveBatch(records);
    }

    /**
     * 设置审批相关字段
     *
     * @param record 记录对象
     * @param approvedType 审批类型
     * @param approvedContent 审批内容
     */
    public static void setApprovalFields(WorkflowRecord record, String approvedType, String approvedContent) {
        if (record != null) {
            record.setSpjg(approvedType);
            record.setSpyj(approvedContent);
        }
    }

    /**
     * 设置驳回相关字段
     *
     * @param record 记录对象
     * @param sourceNode 源节点
     * @param targetNode 目标节点
     * @param bhType 驳回类型
     */
    public static void setRejectFields(WorkflowRecord record, String sourceNode, String targetNode, String bhType) {
        if (record != null) {
            record.setSourceNode(sourceNode);
            record.setTargetNode(targetNode);
            record.setBhType(bhType);
        }
    }

    /**
     * 设置传阅信息
     *
     * @param record 记录对象
     * @param circulateMessage 传阅信息
     */
    public static void setCirculateMessage(WorkflowRecord record, String circulateMessage) {
        if (record != null) {
            record.setCirculateMessage(circulateMessage);
        }
    }
}
