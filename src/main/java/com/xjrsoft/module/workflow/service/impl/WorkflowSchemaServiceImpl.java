package com.xjrsoft.module.workflow.service.impl;

import cn.dev33.satoken.session.SaSession;
import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.module.SimpleModule;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.github.yulichang.base.MPJBaseServiceImpl;
import com.github.yulichang.toolkit.MPJWrappers;
import com.xjrsoft.common.constant.GlobalConstant;
import com.xjrsoft.common.enums.EnabledMark;
import com.xjrsoft.common.enums.WorkflowMemberType;
import com.xjrsoft.common.enums.YesOrNoEnum;
import com.xjrsoft.common.exception.MyException;
import com.xjrsoft.common.page.ConventPage;
import com.xjrsoft.common.page.PageOutput;
import com.xjrsoft.common.utils.ActivityImplHolder;
import com.xjrsoft.common.utils.VoToColumnUtil;
import com.xjrsoft.module.organization.entity.Post;
import com.xjrsoft.module.organization.entity.Role;
import com.xjrsoft.module.organization.entity.User;
import com.xjrsoft.module.organization.service.IRoleService;
import com.xjrsoft.module.organization.service.IUserService;
import com.xjrsoft.module.system.entity.DictionaryDetail;
import com.xjrsoft.module.workflow.constant.WorkflowConstant;
import com.xjrsoft.module.workflow.dto.*;
import com.xjrsoft.module.workflow.entity.*;
import com.xjrsoft.module.workflow.mapper.WorkflowSchemaDraftMapper;
import com.xjrsoft.module.workflow.mapper.WorkflowSchemaHistoryMapper;
import com.xjrsoft.module.workflow.mapper.WorkflowSchemaMapper;
import com.xjrsoft.module.workflow.model.*;
import com.xjrsoft.module.workflow.service.IWorkflowSchemaAuthService;
import com.xjrsoft.module.workflow.service.IWorkflowSchemaService;
import com.xjrsoft.module.workflow.service.MyTaskService;
import com.xjrsoft.module.workflow.utils.WorkFlowUtil;
import com.xjrsoft.module.workflow.vo.WorkflowSchemaDraftPageVo;
import com.xjrsoft.module.workflow.vo.WorkflowSchemaPageVo;
import lombok.AllArgsConstructor;
import lombok.SneakyThrows;
import org.apache.commons.lang3.tuple.Triple;
import org.camunda.bpm.engine.RepositoryService;
import org.camunda.bpm.engine.RuntimeService;
import org.camunda.bpm.engine.TaskService;
import org.camunda.bpm.engine.impl.task.TaskDefinition;
import org.camunda.bpm.engine.migration.MigrationPlan;
import org.camunda.bpm.engine.repository.Deployment;
import org.camunda.bpm.engine.repository.ProcessDefinition;
import org.camunda.bpm.engine.runtime.ProcessInstance;
import org.camunda.commons.utils.IoUtil;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

/**
 * <p>
 * 流程模板表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-04
 */
@Service
@AllArgsConstructor
public class WorkflowSchemaServiceImpl extends MPJBaseServiceImpl<WorkflowSchemaMapper, WorkflowSchema> implements IWorkflowSchemaService {

    private final RepositoryService repositoryService;


    private final WorkflowSchemaDraftMapper workflowSchemaDraftMapper;

    private final WorkflowSchemaHistoryMapper workflowSchemaHistoryMapper;

    private final TaskService taskService;

    private final IWorkflowSchemaAuthService workflowSchemaAuthService;
    private final RuntimeService runtimeService;


    @Override
    public PageOutput<WorkflowSchemaPageVo> getPage(WorkflowSchemaPageDto dto) {

        dto.setIsAuth(false);
        SaSession tokenSession = StpUtil.getTokenSession();
        List<Long> roleIds = tokenSession.get(GlobalConstant.LOGIN_USER_ROLE_ID_KEY, new ArrayList<>());
        Post post = tokenSession.get(GlobalConstant.LOGIN_USER_POST_INFO_KEY, new Post());

        List<Long> allSchemaId = new ArrayList<>();

        //是否需要管控权限
        if (dto.getIsAuth()) {
            LambdaQueryWrapper<WorkflowSchemaAuth> query = Wrappers.lambdaQuery(WorkflowSchemaAuth.class)
                    .eq(WorkflowSchemaAuth::getObjType, -1)
                    .or(x -> x.eq(WorkflowSchemaAuth::getObjType, 2).in(WorkflowSchemaAuth::getObjId, post.getId()))
                    .or(x -> x.eq(WorkflowSchemaAuth::getObjType, 1).in(WorkflowSchemaAuth::getObjId, roleIds))
                    .or(x -> x.eq(WorkflowSchemaAuth::getObjType, 0).in(WorkflowSchemaAuth::getObjId, StpUtil.getLoginIdAsString())
                    );
            List<WorkflowSchemaAuth> authList = workflowSchemaAuthService.list(query);
            allSchemaId = authList.stream().map(WorkflowSchemaAuth::getSchemaId).collect(Collectors.toList());

            if (CollectionUtil.isEmpty(allSchemaId)) {
                //如果权限为空  返回空数组
                PageOutput<WorkflowSchemaPageVo> pageOutput = new PageOutput<>();
                pageOutput.setList(new ArrayList<>());
                pageOutput.setCurrentPage(0);
                pageOutput.setTotalPage(0);
                pageOutput.setPageSize(0);
                return pageOutput;
            }

        }
        //因为多表关联 会有多个表都使用了id字段，  所以必须专门指定主表的Id
        IPage<WorkflowSchemaPageVo> page = selectJoinListPage(ConventPage.getPage(dto), WorkflowSchemaPageVo.class,
                MPJWrappers.<WorkflowSchema>lambdaJoin()
                        .disableSubLogicDel()
                        .orderByDesc(WorkflowSchema::getId)
                        .eq(true, WorkflowSchema::getParentId, 0)
                        .eq(ObjectUtil.isNotNull(dto.getEnabledMark()), WorkflowSchema::getEnabledMark, dto.getEnabledMark())
                        .like(StrUtil.isNotBlank(dto.getKeyword()), WorkflowSchema::getName, dto.getKeyword())
                        .like(StrUtil.isNotBlank(dto.getName()), WorkflowSchema::getName, dto.getName())
                        .like(StrUtil.isNotBlank(dto.getCode()), WorkflowSchema::getCode, dto.getCode())
                        .eq(ObjectUtil.isNotNull(dto.getCategory()), WorkflowSchema::getCategory, dto.getCategory())
                        .in(dto.getIsAuth() && CollectionUtil.isNotEmpty(allSchemaId), WorkflowSchema::getId, allSchemaId)
                        .select(WorkflowSchema::getId)
                        .select(WorkflowSchema.class, x -> VoToColumnUtil.fieldsToColumns(WorkflowSchemaPageVo.class).contains(x.getProperty()))
                        .selectAs(DictionaryDetail::getName, WorkflowSchemaPageVo::getCategoryName)
                        .leftJoin(DictionaryDetail.class, DictionaryDetail::getId, WorkflowSchema::getCategory));

        PageOutput<WorkflowSchemaPageVo> pageOutput = ConventPage.getPageOutput(page);
        return ConventPage.getPageOutput(page);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @SneakyThrows
    public boolean add(AddWorkflowSchemaDto dto) {
        LambdaQueryWrapper<WorkflowSchema> countQuery = Wrappers.lambdaQuery(WorkflowSchema.class)
                .eq(WorkflowSchema::getParentId, "0")
                .eq(WorkflowSchema::getName, dto.getProcessConfig().getName()).or().eq(WorkflowSchema::getCode, dto.getProcessConfig().getCode());
        if (count(countQuery) > 0) {
            throw new MyException("当前模板名称和模板编号重复！");
        }

        WorkflowSchemaConfig workflowSchemaConfig = BeanUtil.toBean(dto, WorkflowSchemaConfig.class);
        WorkflowSchema workflowSchema = BeanUtil.toBean(dto.getProcessConfig(), WorkflowSchema.class);

        //表单发起流程
        FormInitConfig formInitConfig = workflowSchemaConfig.getProcessConfig().getFormInitConfig();
        if (formInitConfig.getEnabled()) {
            LambdaQueryWrapper<WorkflowSchema> select = Wrappers.lambdaQuery(WorkflowSchema.class).eq(WorkflowSchema::getFormId, formInitConfig.getFormId()).select(WorkflowSchema::getId);

            if (count(select) > 0) {
                throw new MyException("当前表单已经绑定过流程，请重新选择！");
            }
            workflowSchema.setFormId(formInitConfig.getFormId());
        }

        Deployment deploy = repositoryService.createDeployment()
                .addInputStream(workflowSchemaConfig.getProcessConfig().getName() + StringPool.DOT + WorkflowConstant.WORKFLOW_SUFFIX, IoUtil.stringAsInputStream(workflowSchemaConfig.getProcessConfig().getXmlContent())).name(workflowSchemaConfig.getProcessConfig().getName()).deploy();

        //存储流程部署id
        workflowSchema.setDeploymentId(deploy.getId());

        //获取流程定义id
        ProcessDefinition processDefinition = repositoryService.createProcessDefinitionQuery().deploymentId(deploy.getId()).singleResult();
        //存储流程定义key
        workflowSchema.setDefinitionKey(processDefinition.getKey());

        //存储流程定义id
        workflowSchema.setDefinitionId(processDefinition.getId());

        //把配置json存储  (清除掉xml)
        workflowSchemaConfig.getProcessConfig().setXmlContent("");

        ObjectMapper mapper = new ObjectMapper();
        SimpleModule simpleModule = new SimpleModule();
        simpleModule.addSerializer(Long.class, ToStringSerializer.instance);
        simpleModule.addSerializer(Long.TYPE, ToStringSerializer.instance);
        mapper.registerModule(simpleModule);

        String jsonContent = mapper.writeValueAsString(workflowSchemaConfig);

        workflowSchema.setJsonContent(jsonContent);

        save(workflowSchema);

        AuthConfig authConfig = workflowSchemaConfig.getProcessConfig().getAuthConfig();

        //如果是指定人员
        if (authConfig.getAuthType() == 1) {
            List<WorkflowSchemaAuth> authList = new ArrayList<>();
            List<MemberConfig> authMemberConfigs = authConfig.getAuthMemberConfigs();
            for (MemberConfig authMemberConfig : authMemberConfigs) {
                WorkflowSchemaAuth auth = new WorkflowSchemaAuth();
                auth.setSchemaId(workflowSchema.getId());
                auth.setObjId(Long.parseLong(authMemberConfig.getId()));
                auth.setObjName(authMemberConfig.getName());
                auth.setObjType(authMemberConfig.getMemberType());
                authList.add(auth);
            }
            workflowSchemaAuthService.saveBatch(authList);
        }
        //如果所有人
        else {
            WorkflowSchemaAuth auth = new WorkflowSchemaAuth();
            auth.setSchemaId(workflowSchema.getId());
            auth.setObjType(-1);
            workflowSchemaAuthService.save(auth);
        }


        //将新增的数据 记录模板历史记录
        WorkflowSchemaHistory history = new WorkflowSchemaHistory();
        history.setSchemaId(workflowSchema.getId());
        history.setVersion(1);
        history.setActivityFlag(EnabledMark.ENABLED.getCode());
        history.setXmlContent(workflowSchema.getXmlContent());
        history.setJsonContent(workflowSchema.getJsonContent());
        history.setDefinitionKey(workflowSchema.getDefinitionKey());
        history.setDeploymentId(workflowSchema.getDeploymentId());
        history.setDefinitionId(workflowSchema.getDefinitionId());


        //缓存节点监听器数据
        CompletableFuture.runAsync(() -> {
            WorkFlowUtil.cacheNodeListener(deploy.getId(), workflowSchemaConfig.getChildNodeConfig());
        });


        return workflowSchemaHistoryMapper.insert(history) > 0;
    }

    @Override
    public boolean addDraft(AddWorkflowSchemaDraftDto dto) {
        WorkflowSchemaConfig workflowSchemaConfig = BeanUtil.toBean(dto, WorkflowSchemaConfig.class);
        WorkflowSchemaDraft workflowSchemaDraft = BeanUtil.toBean(dto.getProcessConfig(), WorkflowSchemaDraft.class);

        //把配置json存储
        workflowSchemaDraft.setJsonContent(JSONUtil.toJsonStr(workflowSchemaConfig));

        return workflowSchemaDraftMapper.insert(workflowSchemaDraft) > 0;
    }

    @Override
    public PageOutput<WorkflowSchemaDraftPageVo> getDraftPage(WorkflowSchemaDraftPageDto dto) {

        LambdaQueryWrapper<WorkflowSchemaDraft> queryWrapper = Wrappers.lambdaQuery(WorkflowSchemaDraft.class)
                .like(StrUtil.isNotBlank(dto.getKeyword()), WorkflowSchemaDraft::getName, dto.getKeyword())
                .between(ObjectUtil.isNotNull(dto.getStartTime()) && ObjectUtil.isNotNull(dto.getEndTime()), WorkflowSchemaDraft::getCreateDate, dto.getStartTime(), dto.getEndTime())
                .select(WorkflowSchemaDraft.class, x -> VoToColumnUtil.fieldsToColumns(WorkflowSchemaPageVo.class).contains(x.getProperty()));

        IPage<WorkflowSchemaDraft> workflowSchemaDraftPage = workflowSchemaDraftMapper.selectPage(ConventPage.getPage(dto), queryWrapper);

        return ConventPage.getPageOutput(workflowSchemaDraftPage, WorkflowSchemaDraftPageVo.class);
    }

    @Override
    public synchronized boolean update(UpdateWorkflowSchemaDto dto) {
        LambdaQueryWrapper<WorkflowSchema> countQuery = Wrappers.lambdaQuery(WorkflowSchema.class)
                .ne(WorkflowSchema::getId, dto.getId())
                .eq(WorkflowSchema::getParentId, "0")
                .and(x -> x.eq(WorkflowSchema::getName, dto.getProcessConfig().getName()).or().eq(WorkflowSchema::getCode, dto.getProcessConfig().getCode()));
        if (count(countQuery) > 0) {
            throw new MyException("当前模板名称和模板编号重复！");
        }


        WorkflowSchema workflowSchema = getById(dto.getId());


        if (workflowSchema == null) {
            throw new MyException("找不到此模板！");
        }
        workflowSchema.setName(dto.getProcessConfig().getName());
        workflowSchema.setCode(dto.getProcessConfig().getCode());
        workflowSchema.setRemark(dto.getProcessConfig().getRemark());
        workflowSchema.setXmlContent(dto.getProcessConfig().getXmlContent());
        workflowSchema.setWorkflowChat(dto.getProcessConfig().getWorkflowChat());
        workflowSchema.setCategory(Long.valueOf(dto.getProcessConfig().getCategory()));

        WorkflowSchemaConfig workflowSchemaConfig = BeanUtil.toBean(dto, WorkflowSchemaConfig.class);


        //表单发起流程
        FormInitConfig formInitConfig = workflowSchemaConfig.getProcessConfig().getFormInitConfig();
        if (formInitConfig.getEnabled()) {
            LambdaQueryWrapper<WorkflowSchema> select = Wrappers.lambdaQuery(WorkflowSchema.class).ne(WorkflowSchema::getId, dto.getId()).eq(WorkflowSchema::getFormId, formInitConfig.getFormId()).select(WorkflowSchema::getId);

            if (count(select) > 0) {
                throw new MyException("当前表单已经绑定过流程，请重新选择！");
            }
            workflowSchema.setFormId(formInitConfig.getFormId());
        } else {
            workflowSchema.setFormId("0");
        }


//        ProcessDefinition processDefinition = repositoryService.createProcessDefinitionQuery().deploymentId(workflowSchema.getDeploymentId()).singleResult();
//
//
//        //如果需要级联删除  把第二个参数设置为true
//        //级联删除 会删除所有关联数据 包括 历史记录
//        repositoryService.deleteProcessDefinition(processDefinition.getId());
//
//        //如果需要级联删除  把第二个参数设置为true
//        //级联删除 会删除所有关联数据 包括 历史记录
//        repositoryService.deleteDeployment(workflowSchema.getDeploymentId());


        //因为流程图会被缓存  如果修改了流程图 缓存会没变。 需要清理本地缓存
//        processEngineConfiguration.getDeploymentCache().removeDeployment(workflowSchema.getDeploymentId());


        //更新的流程重新部署
        Deployment deploy = repositoryService.createDeployment()
                .addInputStream(dto.getProcessConfig().getName() + StringPool.DOT + WorkflowConstant.WORKFLOW_SUFFIX, IoUtil.stringAsInputStream(dto.getProcessConfig().getXmlContent()))
                .name(dto.getProcessConfig().getName())
                .deploy();

        //获取流程定义id
        ProcessDefinition newProcessDefinition = repositoryService.createProcessDefinitionQuery().deploymentId(deploy.getId()).singleResult();
        List<ProcessInstance> processInstances = runtimeService.createProcessInstanceQuery()
                .processDefinitionId(workflowSchema.getDefinitionId())
                .list();


        ExecutorService executorService = Executors.newFixedThreadPool(5);
        if (processInstances != null && !processInstances.isEmpty()) {
            MigrationPlan migrationPlan = runtimeService
                    .createMigrationPlan(workflowSchema.getDefinitionId(), newProcessDefinition.getId())
                    .mapEqualActivities()
                    .updateEventTriggers()
                    .build();
            List<String> collect = processInstances.stream().map(ProcessInstance::getId).collect(Collectors.toList());
            try {
                new Thread(new Runnable() {
                    @Override
                    public void run() {
                        collect.forEach(id -> {
                            executorService.submit(() -> {
                                try {
                                    runtimeService.newMigration(migrationPlan)
                                            .processInstanceIds(id)
                                            .skipIoMappings()
                                            .skipCustomListeners()
                                            .execute();
                                } catch (Exception e) {
                                    log.error("流程迁移失败, 流程实例ID: " + id, e);
                                }
                            });
                        });
                    }
                }).start();


            } catch (Exception e) {
                log.error("流程迁移失败", e);
            }
        }

        //存储流程定义key
        workflowSchema.setDefinitionKey(newProcessDefinition.getKey());
        //存储流程定义id
        workflowSchema.setDefinitionId(newProcessDefinition.getId());
        workflowSchema.setDeploymentId(deploy.getId());


        AuthConfig authConfig = workflowSchemaConfig.getProcessConfig().getAuthConfig();
        //如果是指定人员
        if (authConfig.getAuthType() == 1) {
            workflowSchemaAuthService.remove(Wrappers.lambdaQuery(WorkflowSchemaAuth.class).eq(WorkflowSchemaAuth::getSchemaId, workflowSchema.getId()));
            List<WorkflowSchemaAuth> authList = new ArrayList<>();
            List<MemberConfig> authMemberConfigs = authConfig.getAuthMemberConfigs();
            for (MemberConfig authMemberConfig : authMemberConfigs) {
                WorkflowSchemaAuth auth = new WorkflowSchemaAuth();
                auth.setSchemaId(workflowSchema.getId());
                auth.setObjId(Long.parseLong(authMemberConfig.getId()));
                auth.setObjName(authMemberConfig.getName());
                auth.setObjType(authMemberConfig.getMemberType());
                authList.add(auth);
            }
            workflowSchemaAuthService.saveBatch(authList);
        }
        //如果所有人
        else {
            workflowSchemaAuthService.remove(Wrappers.lambdaQuery(WorkflowSchemaAuth.class).eq(WorkflowSchemaAuth::getSchemaId, workflowSchema.getId()));
            WorkflowSchemaAuth auth = new WorkflowSchemaAuth();
            auth.setSchemaId(workflowSchema.getId());
            auth.setObjType(-1);
            workflowSchemaAuthService.save(auth);
        }


        //找到上一个版本号 只需要版本号 以及 id
        LambdaQueryWrapper<WorkflowSchemaHistory> queryWrapper = Wrappers.lambdaQuery(WorkflowSchemaHistory.class)
                .eq(WorkflowSchemaHistory::getSchemaId, workflowSchema.getId())
                .eq(WorkflowSchemaHistory::getActivityFlag, YesOrNoEnum.YES.getCode())
                .select(WorkflowSchemaHistory::getVersion, WorkflowSchemaHistory::getId)
                .orderByDesc(WorkflowSchemaHistory::getVersion);
        List<WorkflowSchemaHistory> workflowSchemaHistories = workflowSchemaHistoryMapper.selectList(queryWrapper);
        Integer version = workflowSchemaHistories.isEmpty() ? 0 : workflowSchemaHistories.get(0).getVersion();
        for (WorkflowSchemaHistory workflowSchemaHistory : workflowSchemaHistories) {
            //将上一个版本的 历史数据 flag  改为 非活动版本
            workflowSchemaHistory.setActivityFlag(EnabledMark.DISABLED.getCode());
            workflowSchemaHistoryMapper.updateById(workflowSchemaHistory);
        }


        //把配置json存储  (清除掉xml)
        workflowSchemaConfig.getProcessConfig().setXmlContent("");

        ObjectMapper mapper = new ObjectMapper();
        SimpleModule simpleModule = new SimpleModule();
        simpleModule.addSerializer(Long.class, ToStringSerializer.instance);
        simpleModule.addSerializer(Long.TYPE, ToStringSerializer.instance);
        mapper.registerModule(simpleModule);

        String jsonContent = null;
        try {
            jsonContent = mapper.writeValueAsString(workflowSchemaConfig);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }

        workflowSchema.setJsonContent(jsonContent);

        //将修改后的数据 记录模板历史记录
        WorkflowSchemaHistory history = new WorkflowSchemaHistory();
        history.setSchemaId(workflowSchema.getId());
        history.setVersion(version + 1);
        history.setActivityFlag(EnabledMark.ENABLED.getCode());
        history.setXmlContent(workflowSchema.getXmlContent());
        history.setJsonContent(workflowSchema.getJsonContent());
        history.setWorkflowChat(workflowSchema.getWorkflowChat());
        history.setDefinitionId(workflowSchema.getDefinitionId());
        history.setDefinitionKey(workflowSchema.getDefinitionKey());
        workflowSchemaHistoryMapper.insert(history);

        //缓存节点监听器数据
        CompletableFuture.runAsync(() -> {
            WorkFlowUtil.cacheNodeListener(deploy.getId(), workflowSchemaConfig.getChildNodeConfig());
        });
        List<WorkflowSchema> list = list(new LambdaQueryWrapper<>(WorkflowSchema.class)
                .eq(WorkflowSchema::getParentId, workflowSchema.getId()));
        list.forEach(item -> {
            CopyOptions copyOptions = CopyOptions.create();
            copyOptions.setIgnoreProperties("id", "schemeId", "formId", "parentId", "jsonContent");
            BeanUtil.copyProperties(workflowSchema, item, copyOptions);
            WorkflowSchemaConfig workflowSchemaConfigNew = JSONUtil.toBean(workflowSchema.getJsonContent(), WorkflowSchemaConfig.class);
            WorkflowSchemaConfig workflowSchemaConfigOld = JSONUtil.toBean(item.getJsonContent(), WorkflowSchemaConfig.class);
            workflowSchemaConfigOld.setProcessConfig(workflowSchemaConfigNew.getProcessConfig());
            //父流程
            for (Map<String, Object> newObjectMap : workflowSchemaConfigNew.getChildNodeConfig()) {
                String id = newObjectMap.get("id").toString();
                boolean flag = false;
                //子流程
                for (Map<String, Object> oldObjectMap : workflowSchemaConfigOld.getChildNodeConfig()) {
                    if (id.equals(oldObjectMap.get("id").toString())) {
                        flag = true;
                        CopyOptions copyOptions2 = CopyOptions.create();
                        copyOptions2.setIgnoreProperties("formConfigs");
                        BeanUtil.copyProperties(newObjectMap, oldObjectMap, copyOptions2);
                    }
                }
                if (!flag) {
                    workflowSchemaConfigOld.getChildNodeConfig().add(newObjectMap);
                }
            }
            item.setJsonContent(JSONUtil.toJsonStr(workflowSchemaConfigOld));
            updateById(item);
        });


        return updateById(workflowSchema);
    }

    @Override
    public boolean delete(List<Long> ids) {

        for (Long id : ids) {
            long count = taskService.createTaskQuery().processVariableValueEquals(WorkflowConstant.PROCESS_SCHEMA_ID_KEY, id).count();
            if (count > 0) {
                throw new MyException("有流程正在执行 无法删除！");
            }
        }
        QueryWrapper<WorkflowSchema> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .in(WorkflowSchema::getId, ids)
                .select(WorkflowSchema::getDeploymentId, WorkflowSchema::getDefinitionId);

        List<WorkflowSchema> workflowSchemas = list(queryWrapper);

        //获取到所有流程定义id
        List<String> definitionIds = workflowSchemas.stream().map(WorkflowSchema::getDefinitionId).collect(Collectors.toList());
        //获取到所有deployIds
        List<String> deploymentIds = workflowSchemas.stream().map(WorkflowSchema::getDeploymentId).collect(Collectors.toList());

        try {
            repositoryService.deleteProcessDefinitions().byIds(StrUtil.join(",", definitionIds)).delete();
        } catch (Exception e) {
            if (e.getMessage().contains("since there exists")) {
                throw new MyException("有流程正在执行 无法删除！");//避免有外部任务正在执行的流程删除时，提示引擎删除错误。
            }
        }
        for (String deploymentId : deploymentIds) {
            repositoryService.deleteDeployment(deploymentId);
        }

        //缓存节点监听器数据
        CompletableFuture.runAsync(() -> {
            for (String deploymentId : deploymentIds) {
                WorkFlowUtil.removeNodeListener(deploymentId);
            }
        });

        return removeBatchByIds(ids);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @SneakyThrows
    public boolean importSchema(MultipartFile multipartFile) {

        //用流读取文件
        BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(multipartFile.getInputStream()));
        String line;
        StringBuilder content = new StringBuilder();
        // 读取想定文件
        while ((line = bufferedReader.readLine()) != null) {
            content.append(line);
        }

        WorkflowSchemaConfig workflowSchemaConfig = JSONUtil.toBean(content.toString(), WorkflowSchemaConfig.class);

        WorkflowSchema workflowSchema = BeanUtil.toBean(workflowSchemaConfig.getProcessConfig(), WorkflowSchema.class);

        Deployment deploy = repositoryService.createDeployment()
                .addInputStream(workflowSchemaConfig.getProcessConfig().getName() + StringPool.DOT + WorkflowConstant.WORKFLOW_SUFFIX, IoUtil.stringAsInputStream(workflowSchemaConfig.getProcessConfig().getXmlContent())).name(workflowSchemaConfig.getProcessConfig().getName()).deploy();

        //存储流程部署id
        workflowSchema.setDeploymentId(deploy.getId());

        //获取流程定义id
        ProcessDefinition processDefinition = repositoryService.createProcessDefinitionQuery().deploymentId(deploy.getId()).singleResult();
        //存储流程定义key
        workflowSchema.setDefinitionKey(processDefinition.getKey());

        //存储流程定义id
        workflowSchema.setDefinitionId(processDefinition.getId());

        //把配置json存储  (清除掉xml)
        workflowSchemaConfig.getProcessConfig().setXmlContent("");
        workflowSchema.setJsonContent(JSONUtil.toJsonStr(workflowSchemaConfig));

        save(workflowSchema);

        //将新增的数据 记录模板历史记录
        WorkflowSchemaHistory history = new WorkflowSchemaHistory();
        history.setSchemaId(workflowSchema.getId());
        history.setVersion(1);
        history.setActivityFlag(EnabledMark.ENABLED.getCode());
        history.setXmlContent(workflowSchema.getXmlContent());
        history.setJsonContent(workflowSchema.getJsonContent());

        //缓存节点监听器数据
        CompletableFuture.runAsync(() -> {
            WorkFlowUtil.cacheNodeListener(deploy.getId(), workflowSchemaConfig.getChildNodeConfig());
        });


        return workflowSchemaHistoryMapper.insert(history) > 0;
    }

    @Override
    public NextTaskInfoDTO getNextTaskInfo(String processInstanceId) {
        ActivityImplHolder.clearContext();
        MyTaskService myTaskService = SpringUtil.getBean(MyTaskService.class);
        TaskDefinition nextTaskInfo = myTaskService.getNextTaskInfo(processInstanceId);
        if (nextTaskInfo != null) {
            NextTaskInfoDTO nextTaskInfoDTO = new NextTaskInfoDTO();
            nextTaskInfoDTO.setTaskId(nextTaskInfo.getKey());
            try {
                nextTaskInfoDTO.setTaskName(nextTaskInfo.getNameExpression().getExpressionText());
            } catch (Exception e) {
                log.error("获取下一个节点名称失败", e);
            }
            myTaskService.getNextTaskUser(processInstanceId, nextTaskInfoDTO);
            Triple<Long, WorkflowSchema, WorkflowSchemaConfig> workflowConfig = WorkFlowUtil.getWorkflowConfigById(WorkFlowUtil.getWorkflowSchema(processInstanceId));
            if (workflowConfig != null
                    && workflowConfig.getRight() != null
                    && "1".equals(workflowConfig.getRight().getProcessConfig().getZdyspsj())) {
                WorkflowApproveRecord lastApproveRecord = WorkFlowUtil.getLastApproveRecord(WorkFlowUtil.getFormDataId(processInstanceId));
                if (lastApproveRecord != null && lastApproveRecord.getApproveTime() != null) {
                    Date valueFormData = DateUtil.parse(DateUtil.format(lastApproveRecord.getApproveTime(), "yyyy-MM-dd HH:mm:ss"));
                    nextTaskInfoDTO.setSpsj(DateUtil.offsetMinute(valueFormData, 30));
                }
            }

            return nextTaskInfoDTO;
        }
        return null;
    }

    @Override
    public void updateAllMemberConfig() {
        List<WorkflowSchema> list = list();
        IUserService userService = SpringUtil.getBean(IUserService.class);
        IRoleService iRoleService = SpringUtil.getBean(IRoleService.class);
        list.forEach(workflowSchema -> {
            new Thread(() -> {
                try {
                    WorkflowSchemaConfig workflowSchemaConfig = JSONUtil.toBean(workflowSchema.getJsonContent(), WorkflowSchemaConfig.class);
                    List<Map<String, Object>> childNodeConfig = workflowSchemaConfig.getChildNodeConfig();
                    for (Map<String, Object> stringObjectMap : childNodeConfig) {
                        UserTaskConfig userTaskConfig = Convert.convert(UserTaskConfig.class, stringObjectMap);
                        List<MemberConfig> approverConfigs = userTaskConfig.getApproverConfigs();
                        if (approverConfigs != null) {
                            Iterator<MemberConfig> iterator = approverConfigs.iterator();
                            while (iterator.hasNext()) {
                                MemberConfig memberConfig = iterator.next();
                                if (memberConfig.getMemberType() == WorkflowMemberType.USER.getCode()) {
                                    String id = memberConfig.getId();
                                    User user = userService.getById(id);
                                    if (user != null) {
                                        memberConfig.setName(user.getName());
                                    } else {
                                        iterator.remove();
                                    }
                                }
                                if (memberConfig.getMemberType() == WorkflowMemberType.ROLE.getCode()) {
                                    String id = memberConfig.getId();
                                    Role role = iRoleService.getById(id);
                                    if (role != null) {
                                        memberConfig.setName(role.getName());
                                    } else {
                                        iterator.remove();
                                    }
                                }
                            }
                        }


                    }
                    String jsonString = JSON.toJSONString(workflowSchemaConfig,
                            SerializerFeature.WriteMapNullValue,
                            SerializerFeature.WriteNullListAsEmpty,
                            SerializerFeature.WriteNullStringAsEmpty);
                    workflowSchema.setJsonContent(jsonString);
                    updateById(workflowSchema);
                } catch (Exception e) {
                    log.error("更新流程配置失败", e);
                }
            }).start();
        });
    }

}
