package com.xjrsoft.module.workflow.service;

import com.xjrsoft.common.model.result.R;
import com.xjrsoft.common.page.PageOutput;
import com.xjrsoft.module.workflow.dto.*;
import com.xjrsoft.module.workflow.entity.WorkflowRecord;
import com.xjrsoft.module.workflow.model.WorkflowSchemaConfig;
import com.xjrsoft.module.workflow.vo.*;
import org.camunda.bpm.engine.task.Task;

import java.util.List;

/**
 * @Author: tzx
 * @Date: 2022/9/8 14:22
 */
public interface IWorkflowExecuteService {

    /**
     * 部署
     *
     * @param dto
     * @return
     */
    boolean deploy(DeployDto dto);

    /**
     * 部署
     *
     * @param schemaId
     * @return
     */
    String preview(Long schemaId);

    /**
     * 发起流程前  所需要获取的流程信息数据
     * @param schemaId
     * @return
     */
    StartProcessInfoVo getStartProcessInfo(String schemaId,String bizId);


    /**
     * 审批流程前 需要获取的流程信息数据
     * @param taskId
     * @return
     */
    UserTaskInfoVo getApproveProcessInfo(String taskId);

    /**
     * 审批流程前 需要获取的流程信息数据
     * @param processId
     * @return
     */
    UserTaskInfoVo getApproveProcessInfoByProcessId(String processId);

    /**
     * 获取当前流程所有的流转记录
     * @param processId
     * @return
     */
    AllRecordListVo getAllRecordInfoByProcessId(String processId,Integer onlySelf);

    /**
     * 审批流程所需要的信息 需要获取的流程信息数据
     * @param processId
     * @return
     */
    RecycleProcessInfoVo getRecycleProcessInfo(String processId);


    /**
     * 发起流程
     *
     * @param dto
     * @return
     */
    List<LaunchAndApproveVo> newLaunch(LaunchDto dto);


    /**
     * 发起流程
     *
     * @param dto
     * @return
     */
    List<LaunchAndApproveVo> reLaunch(ReLaunchDto dto);


    /**
     * 当前人员待处理
     *
     * @param dto
     * @return
     */
    PageOutput<PendingTaskVo> pending(PendingTaskPageDto dto);


    /**
     * 审批
     *
     * @param dto
     * @return
     */
    List<LaunchAndApproveVo> newApprove(ApproveDto dto);
    R xs(XsDto dto);


    List<ApproveMultiVo> approveMulti(ApproveMultiDto dto);

    ApproveMultiInfoVo approveMultiInfo(ApproveMultiInfoDto dto);

    /**
     * 获取当前用户任务的下一节点审批人
     *
     * @param dto
     * @return
     */
    boolean setApproveUser(ApproveUserDto dto);

    /**
     * 获取当前用户任务的下一节点审批人
     *
     * @param dto
     * @return
     */
    boolean setApproveUserMulti(ApproveUserMultiDto dto);


    /**
     * 获取关联任务
     *
     * @param dto
     * @return
     */
    PageOutput<RelationTaskPageVo> getRelationTaskPage(RelationTaskPageDto dto);

    /**
     * 查询关联任务详情
     *
     * @param dto
     * @return
     */
    RelationTaskInfoVo getRelationTaskInfo(RelationTaskInfoDto dto);

    /**
     * 查询任务详情
     *
     * @param taskId
     * @return
     */
    TaskInfoVo getTaskInfo(String taskId);

    /**
     * 获取流程详情信息
     *
     * @param processId
     * @return
     */
    ProcessInfoVo getProcessInfo(String processId);


    /**
     * 查询任务详情
     *
     * @param taskId
     * @return
     */
    TaskInfoVo getCirculatedTaskInfo(String taskId);


    /**
     * 查询流程已完成任务
     * @param processInstanceId 流程id
     * @return
     */
    FinishedTaskVo getFinishedTask(String processInstanceId);


    /**
     * 根据formId  查询流程图 以及  当前流程已完成的任务
     * @param dto
     * @return
     */
    FormFinishedTaskVo getFormFinishedTask(FormFinishedTaskDto dto);


    /**
     * 查询流程流转记录
     * @param processInstanceId 流程id
     * @return
     */
    List<ProcessRecordListVo> getProcessRecord(String processInstanceId);

    /**
     * 获取传阅分页列表
     *
     * @param dto
     * @return
     */
    PageOutput<CirculatedTaskPageVo> getCirculatedTaskPage(CirculatedTaskPageDto dto);


    /**
     * 获取已办分页列表
     *
     * @param dto
     * @return
     */
    PageOutput<FinishedTaskPageVo> getFinishedTaskPage(FinishedTaskPageDto dto);


    /**
     * 获取我所有发起的流程
     *
     * @param dto
     * @return
     */
    PageOutput<MyProcessPageVo> getMyProcessPage(MyProcessPageDto dto);

    /**
     * 流程移入回收站
     *
     * @param dto
     * @return
     */
    boolean moveRecycle(MoveRecycleDto dto);

    /**
     * 流程移入回收站
     *
     * @param dto
     * @return
     */
    PageOutput<RecycleProcessPageVo> getRecycleProcessPage(RecycleProcessPageDto dto);


    /**
     * 回收站流程 重新发起
     *
     * @param dto
     * @return
     */
    RestartVo restart(RestartDto dto);

    /**
     * 回收站删除
     *
     * @param dto
     * @return
     */
    Boolean recycleDelete(RecycleDeleteDto dto);

    /**
     * 获取当前流程已经审批过的节点
     * @param schemaId 流程实例id
     * @param processInstanceId 流程实例id
     * @return
     */
    List<HistoryTaskVo> getHistoryTask(String schemaId, String processInstanceId);


    /**
     * 撤回
     *
     * @param dto
     * @return
     */
    boolean withdraw(WithdrawDto dto);

    CanWithdrawDto canWithdraw(WithdrawDto dto);



    /**
     * 保存流程草稿
     *
     * @param dto
     * @return
     */
    boolean saveDraft(SaveDraftDto dto);

    /**
     * 修改流程草稿
     *
     * @param dto
     * @return
     */
    boolean updateDraft(UpdateDraftDto dto);

    /**
     * 保存流程草稿
     *
     * @param dto
     * @return
     */
    PageOutput<DraftPageVo> draftPage(DraftPageDto dto);


    /**
     * 删除草稿
     *
     * @param ids
     * @return
     */
    boolean deleteDraft(List<Long> ids);

    /**
     * 查询草稿详情
     *
     * @param id
     * @return
     */
    DraftInfoVo draftData(Long id);

    /**
     * 查询流程监控
     *
     * @param dto
     * @return
     */
    PageOutput<MonitorPageVo> getProcessMonitorPage(MonitorPageDto dto);

    /**
     * 删除流程监控
     *
     * @param dto
     * @return
     */
    boolean deleteProcessMonitor(DeleteMonitorDto dto);


    /**
     * 设置审批人
     *
     * @param dto
     * @return
     */
    boolean setAssignee(SetAssigneeDto dto);


    /**
     * 流程挂起
     *
     * @param dto
     * @return
     */
    boolean setSuspended(SetSuspendedDto dto);

    /**
     * 获取节点审批人
     *
     * @param dto
     * @return
     */
    List<GetAssigneeVo> getAssignee(GetAssigneeDto dto);


    /**
     * 加签/减签
     *
     * @param dto
     * @return
     */
    boolean addOrSubSign(AddOrSubSignDto dto);


    /**
     * 获取到可以驳回的节点
     * @param dto
     * @return
     */
    List<RejectNodeVo> getRejectNode(RejectNodeDto dto);

    Boolean transfer(TransferDto dto);

    /**
     * 获取总和
     *
     * @param
     * @return
     */
    GetCountVo getCount();

    Boolean dealTimeoutTask(Integer handleType,String taskId);

    public void addProcessRecord(Task task, Long schemaId, String message, WorkflowRecord record);

    public void addProcessRecord(String taskId,String message);

    public  boolean noHandle(WorkflowSchemaConfig workflowSchemaConfig, List<Task> taskList);
    public  boolean noHandle(String schmaId, String nodeId);
}
