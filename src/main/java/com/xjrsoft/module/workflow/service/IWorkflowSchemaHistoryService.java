package com.xjrsoft.module.workflow.service;

import com.github.yulichang.base.MPJBaseService;
import com.xjrsoft.module.workflow.dto.HistoryChangeDto;
import com.xjrsoft.module.workflow.entity.WorkflowSchemaHistory;
import com.baomidou.mybatisplus.extension.service.IService;
import com.xjrsoft.module.workflow.vo.ProcessChangeVo;

import java.util.List;

/**
 * <p>
 * 流程模板历史记录表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-16
 */
public interface IWorkflowSchemaHistoryService extends MPJBaseService<WorkflowSchemaHistory> {

    /**
     * 设置为当前版本
     * @param dto
     * @return
     */
    List<ProcessChangeVo> change(HistoryChangeDto dto);

    /**
     * 流程变更
     * @param dto
     * @return
     */
    List<ProcessChangeVo> processChanges(HistoryChangeDto dto);

}
