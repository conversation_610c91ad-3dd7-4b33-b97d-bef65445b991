package com.xjrsoft.module.workflow.service.impl;

import com.xjrsoft.module.workflow.entity.WorkflowRecord;
import com.xjrsoft.module.workflow.mapper.WorkflowRecordMapper;
import com.xjrsoft.module.workflow.service.IWorkflowRecordService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 工作流 流转记录信息 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-10
 */
@Service
public class WorkflowRecordServiceImpl extends ServiceImpl<WorkflowRecordMapper, WorkflowRecord> implements IWorkflowRecordService {

}
