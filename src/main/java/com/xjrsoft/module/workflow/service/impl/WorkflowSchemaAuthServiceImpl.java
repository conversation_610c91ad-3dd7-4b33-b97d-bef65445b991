package com.xjrsoft.module.workflow.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xjrsoft.module.workflow.entity.WorkflowSchemaAuth;
import com.xjrsoft.module.workflow.mapper.WorkflowSchemaAuthMapper;
import com.xjrsoft.module.workflow.service.IWorkflowSchemaAuthService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 流程模板权限 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-04
 */
@Service
@AllArgsConstructor
public class WorkflowSchemaAuthServiceImpl extends ServiceImpl<WorkflowSchemaAuthMapper, WorkflowSchemaAuth> implements IWorkflowSchemaAuthService {

}
