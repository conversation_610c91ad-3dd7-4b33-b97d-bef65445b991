package com.xjrsoft.module.workflow.service;

import com.xjrsoft.module.workflow.dto.ApproveMultiInfoTaskDTO;
import com.xjrsoft.module.workflow.dto.GetAllFlowNodeDTO;
import com.xjrsoft.module.workflow.dto.NextTaskInfoDTO;
import com.xjrsoft.module.workflow.dto.NextTaskInfoDTOV2;
import com.xjrsoft.module.workflow.entity.WorkflowSchema;
import com.xjrsoft.module.workflow.model.WorkflowSchemaConfig;
import com.xjrsoft.module.workflow.vo.ApproveMultiInfoTaskVO;
import com.xjrsoft.module.workflow.vo.GetStartNodeInfoVO;
import org.camunda.bpm.engine.impl.task.TaskDefinition;
import org.camunda.bpm.model.bpmn.instance.FlowNode;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * @ClassName TaskService
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/6/17 22:49
 * @Version 1.0
 */
public interface MyTaskService {
    public TaskDefinition getNextTaskInfo(String processInstanceId);
    public TaskDefinition getNextTaskInfoByTaskId(String taskId);
    public void getNextTaskUser(String processInstanceId, NextTaskInfoDTO nextTaskInfoDTO);
    public FlowNode getNextFlowNode(WorkflowSchema workflowSchema, String definitionId, String nodeId, Map<String, Object> formData);
    public List<FlowNode> getAllFlowNode(GetAllFlowNodeDTO getAllFlowNodeDTO);
    public List<FlowNode> getXmlFlowNode(String workFlowId);
    public void toNode(String taskId, String activityId,String userId);
    public void reboot(@RequestParam String taskId);
    public  NextTaskInfoDTO getNextTaskInfoDTO(String processInstanceId);
    public  NextTaskInfoDTO getNextTaskInfoDTO(GetStartNodeInfoVO getStartNodeInfoVO,FlowNode eventStartNode, WorkflowSchema workflowSchema);
    public NextTaskInfoDTO setUsers(FlowNode node, GetStartNodeInfoVO getStartNodeInfoVO);
    public NextTaskInfoDTOV2 setDeptUsers(NextTaskInfoDTO nextTaskInfoDTO);
    public Date getZdyspsj(Map<String, Object> formData, WorkflowSchemaConfig workflowSchemaConfig);

    public  ApproveMultiInfoTaskDTO getApproveMultiInfoTaskDTO(List<ApproveMultiInfoTaskVO> taskList);
}
