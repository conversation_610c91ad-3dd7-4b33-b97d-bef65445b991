package com.xjrsoft.module.workflow.service;

import com.github.yulichang.base.MPJBaseService;
import com.xjrsoft.common.page.PageOutput;
import com.xjrsoft.module.workflow.dto.AddDelegateDto;
import com.xjrsoft.module.workflow.dto.DelegatePageDto;
import com.xjrsoft.module.workflow.dto.UpdateDelegateDto;
import com.xjrsoft.module.workflow.entity.WorkflowDelegate;
import com.baomidou.mybatisplus.extension.service.IService;
import com.xjrsoft.module.workflow.vo.DelegatePageVo;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 流程委托 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-05
 */
@Service
public interface IWorkflowDelegateService extends IService<WorkflowDelegate> {

    /**
     * 分页查询
     * @param dto
     * @return
     */
    PageOutput<DelegatePageVo> page(DelegatePageDto dto);

    /**
     * 新增
     * @param dto
     * @return
     */
    boolean add(AddDelegateDto dto);


    /**
     * 修改
     * @param dto
     * @return
     */
    boolean update(UpdateDelegateDto dto);


    /**
     * 删除
     * @param ids
     * @return
     */
    boolean delete(List<Long> ids);
}
