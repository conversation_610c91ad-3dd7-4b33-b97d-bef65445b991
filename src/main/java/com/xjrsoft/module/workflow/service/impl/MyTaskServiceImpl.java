package com.xjrsoft.module.workflow.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.convert.ConvertException;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.xjrsoft.common.enums.WorkflowApproveType;
import com.xjrsoft.common.enums.YesOrNoEnum;
import com.xjrsoft.common.exception.MyException;
import com.xjrsoft.common.utils.ActivityImplHolder;
import com.xjrsoft.common.utils.ContextUserHolder;
import com.xjrsoft.common.utils.NextTaskInfoUserHolder;
import com.xjrsoft.module.organization.entity.Department;
import com.xjrsoft.module.organization.entity.User;
import com.xjrsoft.module.organization.service.IDepartmentService;
import com.xjrsoft.module.organization.service.IUserService;
import com.xjrsoft.module.workflow.constant.WorkflowConstant;
import com.xjrsoft.module.workflow.dto.*;
import com.xjrsoft.module.workflow.entity.WorkflowApproveRecord;
import com.xjrsoft.module.workflow.entity.WorkflowSchema;
import com.xjrsoft.module.workflow.model.UserTaskConfig;
import com.xjrsoft.module.workflow.model.WorkflowSchemaConfig;
import com.xjrsoft.module.workflow.service.IWorkflowApproveRecordService;
import com.xjrsoft.module.workflow.service.IWorkflowExecuteService;
import com.xjrsoft.module.workflow.service.IWorkflowSchemaService;
import com.xjrsoft.module.workflow.service.MyTaskService;
import com.xjrsoft.module.workflow.utils.WorkFlowUtil;
import com.xjrsoft.module.workflow.utils.WorkflowRecordUtil;
import com.xjrsoft.module.workflow.vo.ApproveMultiInfoTaskVO;
import com.xjrsoft.module.workflow.vo.GetStartNodeInfoVO;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.camunda.bpm.engine.RepositoryService;
import org.camunda.bpm.engine.RuntimeService;
import org.camunda.bpm.engine.TaskService;
import org.camunda.bpm.engine.impl.RepositoryServiceImpl;
import org.camunda.bpm.engine.impl.bpmn.behavior.UserTaskActivityBehavior;
import org.camunda.bpm.engine.impl.el.FixedValue;
import org.camunda.bpm.engine.impl.javax.el.ExpressionFactory;
import org.camunda.bpm.engine.impl.javax.el.PropertyNotFoundException;
import org.camunda.bpm.engine.impl.javax.el.ValueExpression;
import org.camunda.bpm.engine.impl.juel.ExpressionFactoryImpl;
import org.camunda.bpm.engine.impl.juel.SimpleContext;
import org.camunda.bpm.engine.impl.persistence.entity.ExecutionEntity;
import org.camunda.bpm.engine.impl.persistence.entity.ProcessDefinitionEntity;
import org.camunda.bpm.engine.impl.pvm.PvmActivity;
import org.camunda.bpm.engine.impl.pvm.PvmTransition;
import org.camunda.bpm.engine.impl.pvm.process.ActivityImpl;
import org.camunda.bpm.engine.impl.task.TaskDefinition;
import org.camunda.bpm.engine.runtime.ActivityInstance;
import org.camunda.bpm.engine.runtime.Execution;
import org.camunda.bpm.engine.task.Task;
import org.camunda.bpm.model.bpmn.BpmnModelInstance;
import org.camunda.bpm.model.bpmn.instance.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestParam;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.stream.Collectors;

/**
 * @ClassName TaskServiceImpl
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/6/17 22:50
 * @Version 1.0
 */
@Log4j2
@Service
public class MyTaskServiceImpl implements MyTaskService {

    @Autowired
    TaskService taskService;
    @Autowired
    RuntimeService runtimeService;
    @Autowired
    RepositoryService repositoryService;
    @Autowired
    IUserService userService;
    @Autowired
    IWorkflowSchemaService workflowSchemaService;
    @Autowired
    IWorkflowApproveRecordService approveRecordService;
    @Autowired
    IWorkflowApproveRecordService iWorkflowApproveRecordService;
    @Autowired
    IDepartmentService departmentService;

    /**
     * 获取下一个用户任务信息
     *
     * @param processInstanceId 任务Id信息
     * @return 下一个用户任务用户组信息
     * @throws Exception
     */
    @Override
    public TaskDefinition getNextTaskInfo(String processInstanceId) {

        ProcessDefinitionEntity processDefinitionEntity = null;

        String id = null;

        TaskDefinition task = null;

        //获取流程发布Id信息
        String definitionId = runtimeService.createProcessInstanceQuery().processInstanceId(processInstanceId).singleResult().getProcessDefinitionId();

        processDefinitionEntity = (ProcessDefinitionEntity) ((RepositoryServiceImpl) repositoryService)
                .getDeployedProcessDefinition(definitionId);

        ExecutionEntity execution = (ExecutionEntity) runtimeService.createProcessInstanceQuery().processInstanceId(processInstanceId).singleResult();

        //当前流程节点Id信息
        String activitiId = execution.getActivityId();

        //获取到当前活动的实例
        List<WorkflowApproveRecord> workflowApproveRecordList = approveRecordService.list(new LambdaQueryWrapper<WorkflowApproveRecord>()
                .eq(WorkflowApproveRecord::getTargetNode, activitiId)
                .eq(WorkflowApproveRecord::getProcessId, processInstanceId)
                .eq(WorkflowApproveRecord::getApproveType, 2)
                .orderByDesc(WorkflowApproveRecord::getApproveTime));

        if (workflowApproveRecordList.size() > 0) {
            WorkflowApproveRecord workflowApproveRecord = workflowApproveRecordList.get(0);
            if ("2".equals(workflowApproveRecord.getBhType())) {
                TaskDefinition taskDefinition = new TaskDefinition(null);
                taskDefinition.setKey(workflowApproveRecord.getSourceNode());
                taskDefinition.setNameExpression(new FixedValue(workflowApproveRecord.getTaskName()));
                return taskDefinition;
            }
        }


        //获取流程所有节点信息
        List<ActivityImpl> activitiList = processDefinitionEntity.getActivities();

        Object varObj = runtimeService.getVariables(execution.getProcessInstanceId());
        Map<String, Object> varMap = Convert.toMap(String.class, Object.class, varObj);
        Map<String, Object> formData = WorkFlowUtil.getMainFormData(execution.getProcessInstanceId());
        varMap.putAll(formData);

        //遍历所有节点信息
        for (ActivityImpl activityImpl : activitiList) {
            id = activityImpl.getId();
            if (activitiId.equals(id)) {
                //获取下一个节点信息
                task = nextTaskDefinition(activityImpl, activityImpl.getId(), null, processInstanceId, varMap);

                break;
            }
        }
        return task;
    }

    @Override
    public TaskDefinition getNextTaskInfoByTaskId(String taskId) {
        Task taskObj = taskService.createTaskQuery().taskId(taskId).singleResult();
        if (taskObj == null) {
            return null;
        }
        return getNextTaskInfo(taskObj.getProcessInstanceId());
    }

    @Override
    public void getNextTaskUser(String processInstanceId, NextTaskInfoDTO nextTaskInfoDTO) {
        try {
            Object variable = runtimeService.getVariable(processInstanceId, WorkflowConstant.PROCESS_SCHEMA_ID_KEY);
            if (variable != null) {
                WorkflowSchema workflowSchema = workflowSchemaService.getOne(Wrappers.lambdaQuery(WorkflowSchema.class).eq(WorkflowSchema::getId, variable.toString()));
                WorkflowSchemaConfig workflowSchemaConfig = JSONUtil.toBean(workflowSchema.getJsonContent(), WorkflowSchemaConfig.class);
                Optional<Map<String, Object>> userTaskConfigOp = workflowSchemaConfig.getChildNodeConfig().stream().filter(c -> c.containsValue(nextTaskInfoDTO.getTaskId())).findFirst();
                if (userTaskConfigOp.isPresent()) {
                    UserTaskConfig userTaskConfig = Convert.convert(UserTaskConfig.class, userTaskConfigOp.get());
                    try {
                        List<String> approveUserIds = WorkFlowUtil.getUserIdsByMemberConfig(userTaskConfig.getApproverConfigs(), workflowSchemaConfig.getChildNodeConfig(), processInstanceId);
                        if (approveUserIds.size() > 0) {
                            approveUserIds.forEach(userId -> {
                                User user = userService.getById(userId);
                                if (user != null) {
                                    nextTaskInfoDTO.getUsers().add(user);
                                }
                            });
                        }
                    } catch (Exception e) {
                        log.error("获取用户信息失败", e);
                    }
                }
            }
        } catch (ConvertException e) {
            log.error("获取下一个节点信息失败", e);
        }
    }


    @Override
    public FlowNode getNextFlowNode(WorkflowSchema workflowSchema, String definitionId, String nodeId, Map<String, Object> formData) {
        // 获取BPMN模型实例
        try {
            BpmnModelInstance bpmnModelInstance = repositoryService.getBpmnModelInstance(definitionId);
            try {

                WorkflowApproveRecord bhStartNode = WorkFlowUtil.getBhWorkflowApproveRecord(null, nodeId, formData.get("ID").toString());
                if (bhStartNode != null) {
                    FlowNode startNode = bpmnModelInstance.getModelElementById(bhStartNode.getSourceNode());
                    startNode.setId(bhStartNode.getSourceNode());
                    startNode.setName(bhStartNode.getTaskName());
                    return startNode;
                }
            } catch (Exception e) {
                log.error("获取节点信息失败", e);
            }
            FlowNode startNode = bpmnModelInstance.getModelElementById(nodeId);
            if (startNode == null) {
                throw new MyException("节点不存在");
            }
            Collection<SequenceFlow> outgoing = startNode.getOutgoing();
            for (SequenceFlow sequenceFlow : outgoing) {
                FlowNode flowNode = sequenceFlow.getTarget();
                String name = flowNode.getName();
                if (name != null && WorkflowConstant.ZD_NODE_NAME.contains(name)) {
                    return getNextFlowNode(workflowSchema, definitionId, flowNode.getId(), formData);
                }
                if (flowNode instanceof UserTask) {
                    FlowNode flowNodeTemp = getFlowNode(workflowSchema, formData, flowNode);
                    if (flowNodeTemp != null) {
                        return flowNodeTemp;
                    }
                    return getNextFlowNode(workflowSchema, definitionId, flowNode.getId(), formData);
                }
                if (flowNode instanceof EndEvent) {
                    return flowNode;
                }
                if (flowNode instanceof ExclusiveGateway) {
                    ExclusiveGateway exclusiveGateway = (ExclusiveGateway) flowNode;
                    Collection<SequenceFlow> exclusiveGatewayOutgoing = exclusiveGateway.getOutgoing();
                    for (SequenceFlow exclusiveSequenceFlow : exclusiveGatewayOutgoing) {
                        ConditionExpression conditionExpression = exclusiveSequenceFlow.getConditionExpression();
                        if (conditionExpression != null) {
                            String condition = conditionExpression.getTextContent();
                            if (isCondition(condition, formData)) {
                                if (exclusiveSequenceFlow.getTarget() instanceof UserTask || exclusiveSequenceFlow.getTarget() instanceof EndEvent) {
                                    FlowNode flowNodeTemp = getFlowNode(workflowSchema, formData, exclusiveSequenceFlow.getTarget());
                                    if (flowNodeTemp != null) {
                                        return flowNodeTemp;
                                    }
                                    return getNextFlowNode(workflowSchema, definitionId, exclusiveSequenceFlow.getTarget().getId(), formData);
                                } else {
                                    return getNextFlowNode(workflowSchema, definitionId, exclusiveSequenceFlow.getTarget().getId(), formData);
                                }

                            }
                        }
                    }
                    throw new MyException(flowNode.getName() + "流程配置错误，没有合适的分支条件", 500);
                }
            }
        } catch (PropertyNotFoundException e) {
            throw new MyException("表单缺少字段" + e.getMessage(), 500);
        }

        return null;
    }

    private static FlowNode getFlowNode(WorkflowSchema workflowSchema, Map<String, Object> formData, FlowNode flowNode) {
        try {
            IWorkflowExecuteService iWorkflowExecuteService = SpringUtil.getBean(IWorkflowExecuteService.class);
            WorkflowSchemaConfig workflowSchemaConfig = JSONUtil.toBean(workflowSchema.getJsonContent(), WorkflowSchemaConfig.class);
            boolean noHandle = iWorkflowExecuteService.noHandle(workflowSchema.getId() + "", flowNode.getId());
            if (noHandle) {
                Optional<Map<String, Object>> userTaskConfigOp = workflowSchemaConfig.getChildNodeConfig().stream().filter(c -> c.containsValue(flowNode.getId())).findFirst();
                if (userTaskConfigOp.isPresent()) {
                    UserTaskConfig userTaskConfig = Convert.convert(UserTaskConfig.class, userTaskConfigOp.get());
                    List<String> approveUserIds = WorkFlowUtil.getUserIdsByMemberConfig(userTaskConfig.getApproverConfigs(), workflowSchemaConfig.getChildNodeConfig(), ContextUserHolder.getContext(), formData);
                    if (approveUserIds.size() > 0) {
                        return flowNode;
                    }
                }
            } else {
                return flowNode;
            }
        } catch (Exception e) {
            log.error("获取自动跳过审批信息失败", e);
        }
        return null;
    }

    @Override
    public List<FlowNode> getAllFlowNode(GetAllFlowNodeDTO getAllFlowNodeDTO) {
        WorkflowSchema workflowSchema = workflowSchemaService.getById(getAllFlowNodeDTO.getWorkFlowId());
        List<FlowNode> flowNodeList = new ArrayList<>();
        // 获取BPMN模型实例
        BpmnModelInstance bpmnModelInstance = repositoryService.getBpmnModelInstance(workflowSchema.getDefinitionId());
        //获取启动节点
        FlowNode startNode = bpmnModelInstance.getModelElementById("Event_start_node");
        getNextNode(startNode, getAllFlowNodeDTO.getFormData(), flowNodeList);
        return flowNodeList;
    }

    @Override
    public List<FlowNode> getXmlFlowNode(String workFlowId) {
        WorkflowSchema workflowSchema = workflowSchemaService.getById(workFlowId);
        List<FlowNode> flowNodeList = new ArrayList<>();
        // 获取BPMN模型实例
        BpmnModelInstance bpmnModelInstance = repositoryService.getBpmnModelInstance(workflowSchema.getDefinitionId());
        bpmnModelInstance.toString();
        //获取启动节点
        FlowNode startNode = bpmnModelInstance.getModelElementById("Event_start_node");
        flowNodeList.add(startNode);
        getXmlNode(startNode, flowNodeList);
        return flowNodeList;
    }

    @Override
    public void toNode(String taskId, String activityId, String userId) {
        Task currentTask = taskService.createTaskQuery().taskId(taskId).singleResult();
        if (currentTask == null) {
            throw new MyException("任务不存在");
        }

        ActivityInstance currentActivityInstance = runtimeService.getActivityInstance(currentTask.getProcessInstanceId());
        if (currentActivityInstance == null) {
            return;
        }

        // 执行流程修改
        runtimeService.createProcessInstanceModification(currentTask.getProcessInstanceId())
                .cancelActivityInstance(currentActivityInstance.getId())
                .startBeforeActivity(activityId)
                .setVariableLocal(WorkflowConstant.TASK_IS_APPOINT_APPROVE, YesOrNoEnum.NO.getCode())
                .execute();

        // 设置审批人
        List<Task> list = taskService.createTaskQuery().processInstanceId(currentTask.getProcessInstanceId()).list();
        IWorkflowExecuteService iWorkflowExecuteService = SpringUtil.getBean(IWorkflowExecuteService.class);
        list.forEach(x -> {
            ApproveUserDto approveUserDto = new ApproveUserDto();
            approveUserDto.setTaskId(x.getId());
            approveUserDto.setApprovedUsers(Arrays.asList(userId));
            iWorkflowExecuteService.setApproveUser(approveUserDto);
        });
    }

    public void reboot(@RequestParam String taskId) {
        Task currentTask = taskService.createTaskQuery().taskId(taskId).singleResult();
        if (currentTask == null) {
            throw new MyException("任务不存在");
        }

        // 获取当前用户信息
        User currentUser = userService.getById(StpUtil.getLoginIdAsString());
        if (currentUser == null) {
            throw new MyException("当前用户信息不存在");
        }

        // 获取模板ID
        Object schemaIdObj = taskService.getVariable(taskId, WorkflowConstant.PROCESS_SCHEMA_ID_KEY);
        Long schemaId = Convert.toLong(schemaIdObj);

        List<WorkflowApproveRecord> workflowApproveRecordList = iWorkflowApproveRecordService.list(new LambdaQueryWrapper<WorkflowApproveRecord>()
                .eq(WorkflowApproveRecord::getProcessId, currentTask.getProcessInstanceId())
                .eq(WorkflowApproveRecord::getTaskName, "制单")
                .orderByDesc(WorkflowApproveRecord::getApproveTime));

        if (!workflowApproveRecordList.isEmpty()) {
            WorkflowApproveRecord workflowApproveRecord = workflowApproveRecordList.get(0);
            String taskDefinitionKey = workflowApproveRecord.getTaskDefinitionKey();

            // 记录重启流程操作，审批结果为"重启"
            String message = "【" + currentUser.getName() + "】重启流程，从【" + currentTask.getName() + "】回到【制单】节点";
            String approvedContent = "重启流程到制单节点";
            WorkflowRecordUtil.createAndSaveApprovalRecord(
                currentTask,
                schemaId,
                message,
                WorkflowApproveType.CQ.getCode() + "", // 审批类型设置为"重启"
                approvedContent,
                LocalDateTime.now()
            );

            // 执行节点跳转
            toNode(taskId, taskDefinitionKey, workflowApproveRecord.getApproveUserId());
        } else {
            throw new MyException("未找到制单节点的审批记录，无法重启流程");
        }
    }

    /**
     * 根据用户ID获取用户名称
     */
    private String getUserNameById(String userId) {
        if (StringUtils.isEmpty(userId)) {
            return "未知用户";
        }
        User user = userService.getById(userId);
        return user != null ? user.getName() : "未知用户";
    }

    public void getNextNode(FlowNode flowNode, Map<String, Object> formData, List<FlowNode> flowNodeList) {
        Collection<SequenceFlow> outgoing = flowNode.getOutgoing();
        if (outgoing.isEmpty()) {
            return;
        }
        for (SequenceFlow sequenceFlow : outgoing) {
            FlowNode targetFlowNode = sequenceFlow.getTarget();
            if (targetFlowNode instanceof UserTask) {
                flowNodeList.add(targetFlowNode);
                getNextNode(targetFlowNode, formData, flowNodeList);
            }
            if (targetFlowNode instanceof ExclusiveGateway) {
                ExclusiveGateway exclusiveGateway = (ExclusiveGateway) targetFlowNode;
                Collection<SequenceFlow> exclusiveGatewayOutgoing = exclusiveGateway.getOutgoing();
                for (SequenceFlow exclusiveSequenceFlow : exclusiveGatewayOutgoing) {
                    ConditionExpression conditionExpression = exclusiveSequenceFlow.getConditionExpression();
                    if (conditionExpression != null) {
                        String condition = conditionExpression.getTextContent();
                        if (isCondition(condition, formData)) {
                            if (exclusiveSequenceFlow.getTarget() instanceof UserTask) {
                                flowNodeList.add(exclusiveSequenceFlow.getTarget());
                            }
                            getNextNode(exclusiveSequenceFlow.getTarget(), formData, flowNodeList);
                        }
                    }
                }
            }
            if (targetFlowNode instanceof EndEvent) {
                return;
            }

        }
    }

    public void getXmlNode(FlowNode flowNode, List<FlowNode> flowNodeList) {
        Collection<SequenceFlow> outgoing = flowNode.getOutgoing();
        if (outgoing.isEmpty()) {
            return;
        }
        for (SequenceFlow sequenceFlow : outgoing) {
            FlowNode targetFlowNode = sequenceFlow.getTarget();
            if (targetFlowNode instanceof UserTask && !flowNodeList.contains(targetFlowNode)) {
                flowNodeList.add(targetFlowNode);
                getXmlNode(targetFlowNode, flowNodeList);
            }
            if (targetFlowNode instanceof ExclusiveGateway) {
                ExclusiveGateway exclusiveGateway = (ExclusiveGateway) targetFlowNode;
                Collection<SequenceFlow> exclusiveGatewayOutgoing = exclusiveGateway.getOutgoing();
                for (SequenceFlow exclusiveSequenceFlow : exclusiveGatewayOutgoing) {
                    if (exclusiveSequenceFlow.getTarget() instanceof UserTask && !flowNodeList.contains(exclusiveSequenceFlow.getTarget())) {
                        flowNodeList.add(exclusiveSequenceFlow.getTarget());
                    }
                    getXmlNode(exclusiveSequenceFlow.getTarget(), flowNodeList);
                }
            }

        }
    }


    /**
     * 下一个任务节点信息,
     * <p>
     * 如果下一个节点为用户任务则直接返回,
     * <p>
     * 如果下一个节点为排他网关, 获取排他网关Id信息, 根据排他网关Id信息和execution获取流程实例排他网关Id为key的变量值,
     * 根据变量值分别执行排他网关后线路中的el表达式, 并找到el表达式通过的线路后的用户任务
     *
     * @param activityImpl      流程节点信息
     * @param activityId        当前流程节点Id信息
     * @param elString          排他网关顺序流线段判断条件
     * @param processInstanceId 流程实例Id信息
     * @return
     */
    private TaskDefinition nextTaskDefinition(ActivityImpl activityImpl, String activityId, String elString, String processInstanceId, Map<String, Object> variables) {

        PvmActivity ac = null;

        Object s = null;
        ActivityImplHolder.setContext(activityImpl);
        // 如果遍历节点为用户任务并且节点不是当前节点信息
        try {
            if ("userTask".equals(activityImpl.getProperty("type")) && !activityId.equals(activityImpl.getId())) {
                // 获取该节点下一个节点信息
                TaskDefinition taskDefinition = ((UserTaskActivityBehavior) activityImpl.getActivityBehavior())
                        .getTaskDefinition();
                return taskDefinition;
            } else if ("exclusiveGateway".equals(activityImpl.getProperty("type"))) {// 当前节点为exclusiveGateway
                List<PvmTransition> outTransitions = activityImpl.getOutgoingTransitions();
                //outTransitionsTemp = ac.getOutgoingTransitions();

                // 如果网关路线判断条件为空信息
                //          if (StringUtils.isEmpty(elString)) {
                // 获取流程启动时设置的网关判断条件信息
                elString = getGatewayCondition(activityImpl.getId(), processInstanceId);
                //          }
                // 如果排他网关只有一条线路信息
                if (outTransitions.size() == 1) {
                    return nextTaskDefinition((ActivityImpl) outTransitions.get(0).getDestination(), activityId,
                            elString, processInstanceId, variables);
                } else if (outTransitions.size() > 1) { // 如果排他网关有多条线路信息
                    for (PvmTransition tr1 : outTransitions) {
                        s = tr1.getProperty("conditionText"); // 获取排他网关线路判断条件信息
                        // 判断el表达式是否成立
                        if (isCondition(StringUtils.trim(s.toString()), variables)) {
                            return nextTaskDefinition((ActivityImpl) tr1.getDestination(), activityId, elString,
                                    processInstanceId, variables);
                        }

                    }
                }
            } else {
                // 获取节点所有流向线路信息
                List<PvmTransition> outTransitions = activityImpl.getOutgoingTransitions();
                List<PvmTransition> outTransitionsTemp = null;
                if (outTransitions.size() == 0) {
                    if ("noneEndEvent".equals(activityImpl.getProperty("type"))) {
                        //获取 TaskDefinition
                        TaskDefinition taskDefinition = new TaskDefinition(null);
                        taskDefinition.setKey(activityImpl.getId());
                        taskDefinition.setNameExpression(new FixedValue(activityImpl.getProperty("name")));
                        return taskDefinition;
                    }
                } else {
                    for (PvmTransition tr : outTransitions) {
                        ac = tr.getDestination(); // 获取线路的终点节点
                        // 如果流向线路为排他网关
                        if ("exclusiveGateway".equals(ac.getProperty("type"))) {
                            outTransitionsTemp = ac.getOutgoingTransitions();

                            // 如果网关路线判断条件为空信息
                            if (StringUtils.isEmpty(elString)) {
                                // 获取流程启动时设置的网关判断条件信息
                                elString = getGatewayCondition(ac.getId(), processInstanceId);
                            }

                            // 如果排他网关只有一条线路信息
                            if (outTransitionsTemp.size() == 1) {
                                return nextTaskDefinition((ActivityImpl) outTransitionsTemp.get(0).getDestination(), activityId,
                                        elString, processInstanceId, variables);
                            } else if (outTransitionsTemp.size() > 1) { // 如果排他网关有多条线路信息
                                for (PvmTransition tr1 : outTransitionsTemp) {
                                    s = tr1.getProperty("conditionText"); // 获取排他网关线路判断条件信息
                                    // 判断el表达式是否成立
                                    if (isCondition(StringUtils.trim(s.toString()), variables)) {
                                        return nextTaskDefinition((ActivityImpl) tr1.getDestination(), activityId, elString,
                                                processInstanceId, variables);
                                    }
                                }
                            }
                        } else if ("userTask".equals(ac.getProperty("type"))) {
                            return ((UserTaskActivityBehavior) ((ActivityImpl) ac).getActivityBehavior()).getTaskDefinition();
                        } else if ("noneEndEvent".equals(ac.getProperty("type"))) {
                            //获取 TaskDefinition
                            TaskDefinition taskDefinition = new TaskDefinition(null);
                            taskDefinition.setKey(ac.getId());
                            taskDefinition.setNameExpression(new FixedValue(ac.getProperty("name")));
                            return taskDefinition;
                        } else {
                        }
                    }

                }
                return null;
            }
        } catch (PropertyNotFoundException e) {
            throw new MyException("表单缺少字段" + e.getMessage(), 500);

        }
        return null;
    }

    /**
     * 查询流程启动时设置排他网关判断条件信息
     *
     * @param gatewayId         排他网关Id信息, 流程启动时设置网关路线判断条件key为网关Id信息
     * @param processInstanceId 流程实例Id信息
     * @return
     */
    public String getGatewayCondition(String gatewayId, String processInstanceId) {
        Execution execution = runtimeService.createExecutionQuery().processInstanceId(processInstanceId).singleResult();
        Object object = runtimeService.getVariable(execution.getId(), gatewayId);
        return object == null ? "" : object.toString();
    }

    /**
     * 根据key和value判断el表达式是否通过信息
     *
     * @param el el表达式信息
     * @return
     */
    public boolean isCondition(String el, Map<String, Object> variables) {

        try {
            ExpressionFactory factory = new ExpressionFactoryImpl();
            SimpleContext context = new SimpleContext();
            Set<String> strings = variables.keySet();
            for (String string : strings) {
                try {
                    context.setVariable(string, factory.createValueExpression(variables.get(string), variables.get(string).getClass()));
                } catch (Exception e) {

                }
            }
            ValueExpression e = factory.createValueExpression(context, el, boolean.class);
            return (Boolean) e.getValue(context);
        } catch (PropertyNotFoundException ex) {
            throw new MyException("流程分支配置错误" + ex.getMessage(), 500);
        }
    }

    @Override
    public NextTaskInfoDTO getNextTaskInfoDTO(String processInstanceId) {
        GetStartNodeInfoVO getStartNodeInfoVO = new GetStartNodeInfoVO();
        Task task = taskService.createTaskQuery().processInstanceId(processInstanceId).singleResult();
        if (task == null) {
            throw new MyException("当前实例不存在任务");
        }
        ExecutionEntity execution = (ExecutionEntity) runtimeService.createProcessInstanceQuery().processInstanceId(processInstanceId).singleResult();

        //当前流程节点Id信息
        String activitiId = execution.getActivityId();
        Map<String, Object> formData = WorkFlowUtil.getMainFormData(task.getProcessInstanceId());
        Map<String, Map<String, Object>> formDataMap = new HashMap<>();
        Long schemaId = Convert.toLong(taskService.getVariable(task.getId(), WorkflowConstant.PROCESS_SCHEMA_ID_KEY));
        getStartNodeInfoVO.setWorkFlowId(schemaId + "");
        getStartNodeInfoVO.setFormData(formData);

        WorkflowSchema workflowSchema = workflowSchemaService.getById(getStartNodeInfoVO.getWorkFlowId());
        FlowNode eventStartNode = getNextFlowNode(workflowSchema, workflowSchema.getDefinitionId(), activitiId, getStartNodeInfoVO.getFormData());
        NextTaskInfoDTO nextTaskInfoDTO = setUsers(eventStartNode, getStartNodeInfoVO);
        if (nextTaskInfoDTO != null) {
            //获取到整个流程模板的配置
            try {
                WorkflowSchemaConfig workflowSchemaConfig = JSONUtil.toBean(workflowSchema.getJsonContent(), WorkflowSchemaConfig.class);
                Optional<Map<String, Object>> userTaskConfigOp = workflowSchemaConfig.getChildNodeConfig().stream().filter(c -> c.containsValue(nextTaskInfoDTO.getTaskId())).findFirst();
                if (userTaskConfigOp != null && userTaskConfigOp.isPresent()) {
                    UserTaskConfig userTaskConfig = Convert.convert(UserTaskConfig.class, userTaskConfigOp.get());
                    nextTaskInfoDTO.setUserTaskConfig(userTaskConfig);
                }
            } catch (ConvertException e) {
                e.printStackTrace();
                log.error("流程配置转换异常", e);
            }
        }
        nextTaskInfoDTO.setProcessInstanceId(processInstanceId);
        return nextTaskInfoDTO;
    }

    @Override
    public NextTaskInfoDTO getNextTaskInfoDTO(GetStartNodeInfoVO getStartNodeInfoVO, FlowNode eventStartNode, WorkflowSchema workflowSchema) {
        NextTaskInfoDTO nextTaskInfoDTO = setUsers(eventStartNode, getStartNodeInfoVO);
        if (nextTaskInfoDTO != null) {
            //获取到整个流程模板的配置
            try {
                WorkflowSchemaConfig workflowSchemaConfig = JSONUtil.toBean(workflowSchema.getJsonContent(), WorkflowSchemaConfig.class);
                Optional<Map<String, Object>> userTaskConfigOp = workflowSchemaConfig.getChildNodeConfig().stream().filter(c -> c.containsValue(nextTaskInfoDTO.getTaskId())).findFirst();
                if (userTaskConfigOp != null && userTaskConfigOp.isPresent()) {
                    UserTaskConfig userTaskConfig = Convert.convert(UserTaskConfig.class, userTaskConfigOp.get());
                    nextTaskInfoDTO.setUserTaskConfig(userTaskConfig);
                }
            } catch (ConvertException e) {
                e.printStackTrace();
                log.error("流程配置转换异常", e);
            }
        }
        return nextTaskInfoDTO;
    }

    @Override
    public NextTaskInfoDTO setUsers(FlowNode node, GetStartNodeInfoVO getStartNodeInfoVO) {
        NextTaskInfoDTO nextTaskInfoDTO = new NextTaskInfoDTO();
        WorkflowSchema workflowSchema = workflowSchemaService.getById(getStartNodeInfoVO.getWorkFlowId());
        if (node != null) {
            nextTaskInfoDTO.setTaskId(node.getId());
            nextTaskInfoDTO.setTaskName(node.getName());
            WorkflowSchemaConfig workflowSchemaConfig = JSONUtil.toBean(workflowSchema.getJsonContent(), WorkflowSchemaConfig.class);
            Optional<Map<String, Object>> userTaskConfigOp = workflowSchemaConfig.getChildNodeConfig().stream().filter(c -> c.containsValue(node.getId())).findFirst();
            if (userTaskConfigOp.isPresent()) {
                UserTaskConfig userTaskConfig = Convert.convert(UserTaskConfig.class, userTaskConfigOp.get());
                List<String> approveUserIds = WorkFlowUtil.getUserIdsByMemberConfig(userTaskConfig.getApproverConfigs(), workflowSchemaConfig.getChildNodeConfig(), ContextUserHolder.getContext(), getStartNodeInfoVO.getFormData());
                if (approveUserIds.size() > 0) {
                    approveUserIds.forEach(userId -> {
                        User user = userService.getById(userId);
                        if (user != null) {
                            nextTaskInfoDTO.getUsers().add(user);

                        }
                    });
                }
            }

            nextTaskInfoDTO.setSpsj(getZdyspsj(getStartNodeInfoVO.getFormData(), workflowSchemaConfig));
        }
        return nextTaskInfoDTO;
    }

    @Override
    public Date getZdyspsj(Map<String, Object> formData, WorkflowSchemaConfig workflowSchemaConfig) {
        if ("1".equals(workflowSchemaConfig.getProcessConfig().getZdyspsj())) {
            WorkflowApproveRecord lastApproveRecord = WorkFlowUtil.getLastApproveRecord(formData.get("ID").toString());
            Object wfStatus = formData.get("WF_STATUS");
            if (wfStatus == null) {
                wfStatus = "";
            }
            String wfStatusStr = wfStatus.toString();
            if (lastApproveRecord != null && lastApproveRecord.getApproveTime() != null && !"1".equals(wfStatusStr)) {
                Date valueFormData = DateUtil.parse(DateUtil.format(lastApproveRecord.getApproveTime(), "yyyy-MM-dd HH:mm:ss"));
                return DateUtil.offsetMinute(valueFormData, 30);
            } else {
                if (formData.get(WorkflowConstant.TD_TIME) != null) {
                    Date startTime = new Date();
                    int year = 0;
                    int month = 0;
                    int day = 0;
                    DateTime dateTime = null;

                    if (formData.get(WorkflowConstant.TD_TIME) instanceof DateTime) {
                        dateTime = (DateTime) formData.get(WorkflowConstant.TD_TIME);
                    } else {
                        dateTime = DateUtil.parse(formData.get(WorkflowConstant.TD_TIME).toString());
                    }
                    year = DateUtil.year(dateTime);
                    month = DateUtil.month(dateTime) + 1;
                    day = DateUtil.dayOfMonth(dateTime);
                    LocalDateTime now = LocalDateTime.now();
                    startTime = DateUtil.parse(year + "-" + month + "-" + day + " " + now.getHour() + ":" + now.getMinute() + ":" + now.getSecond());
                    return startTime;
                } else {
                    throw new MyException("业务开始时间不能为空");
                }
            }
        }
        return null;
    }

    @Override
    public  ApproveMultiInfoTaskDTO getApproveMultiInfoTaskDTO(List<ApproveMultiInfoTaskVO> taskList) {
        ApproveMultiInfoTaskDTO approveMultiInfoTaskDTO = new ApproveMultiInfoTaskDTO();
        // 使用 ConcurrentHashMap 来代替普通的 HashMap
        ConcurrentHashMap<String, List<ApproveMultiInfoTaskVO>> map = new ConcurrentHashMap<>();
        ConcurrentHashMap<String, NextTaskInfoDTO> nextTaskMap = new ConcurrentHashMap<>();
        //线程安全的 list
        List<GetApproveMultiInfoTaskDTO> getApproveMultiInfoTaskDTOList = new CopyOnWriteArrayList<>();
        for (ApproveMultiInfoTaskVO approveMultiInfoTaskVO : taskList) {
            GetStartNodeInfoVO getStartNodeInfoVO = new GetStartNodeInfoVO();
            Task task = taskService.createTaskQuery().processInstanceId(approveMultiInfoTaskVO.getProcessInstanceId()).singleResult();
            if (task == null) {
                throw new MyException("当前实例不存在任务");
            }
            ExecutionEntity execution = (ExecutionEntity) runtimeService.createProcessInstanceQuery().processInstanceId(approveMultiInfoTaskVO.getProcessInstanceId()).singleResult();

            //当前流程节点Id信息
            String activitiId = execution.getActivityId();
            Map<String, Object> formData = WorkFlowUtil.getMainFormData(task.getProcessInstanceId());
            Long schemaId = Convert.toLong(taskService.getVariable(task.getId(), WorkflowConstant.PROCESS_SCHEMA_ID_KEY));
            getStartNodeInfoVO.setWorkFlowId(schemaId + "");
            getStartNodeInfoVO.setFormData(formData);

            WorkflowSchema workflowSchema = workflowSchemaService.getById(getStartNodeInfoVO.getWorkFlowId());
            FlowNode eventStartNode = getNextFlowNode(workflowSchema, workflowSchema.getDefinitionId(), activitiId, getStartNodeInfoVO.getFormData());

            GetApproveMultiInfoTaskDTO getApproveMultiInfoTaskDTO = new GetApproveMultiInfoTaskDTO();
            getApproveMultiInfoTaskDTO.setWorkflowSchema(workflowSchema);
            getApproveMultiInfoTaskDTO.setFlowNode(eventStartNode);
            getApproveMultiInfoTaskDTO.setGetStartNodeInfoVO(getStartNodeInfoVO);
            getApproveMultiInfoTaskDTO.setApproveMultiInfoTaskVO(approveMultiInfoTaskVO);
            getApproveMultiInfoTaskDTOList.add(getApproveMultiInfoTaskDTO);
        }
        for (GetApproveMultiInfoTaskDTO getApproveMultiInfoTaskDTO : getApproveMultiInfoTaskDTOList) {

            NextTaskInfoDTO nextTaskInfo = getNextTaskInfoDTO(getApproveMultiInfoTaskDTO.getGetStartNodeInfoVO(),
                    getApproveMultiInfoTaskDTO.getFlowNode(),
                    getApproveMultiInfoTaskDTO.getWorkflowSchema());
            if (nextTaskInfo != null) {
                String key = nextTaskInfo.getTaskId();
                map.computeIfAbsent(key, k -> new ArrayList<>()).add(getApproveMultiInfoTaskDTO.getApproveMultiInfoTaskVO());

                NextTaskInfoDTO nextTaskInfoDTO = new NextTaskInfoDTO();
                try {
                    nextTaskInfoDTO.setTaskName(nextTaskInfo.getTaskName());
                } catch (Exception e) {
                    log.error("获取下一个节点名称失败", e);
                }
                nextTaskInfoDTO.setTaskId(nextTaskInfo.getTaskId());
                nextTaskInfoDTO.setUserTaskConfig(nextTaskInfo.getUserTaskConfig());
                WorkflowSchemaConfig workflowSchemaConfig = JSONUtil.toBean(getApproveMultiInfoTaskDTO.getWorkflowSchema().getJsonContent(), WorkflowSchemaConfig.class);
                //设置审批时间
                nextTaskInfoDTO.setSpsj(getZdyspsj(getApproveMultiInfoTaskDTO.getGetStartNodeInfoVO().getFormData(), workflowSchemaConfig));
                nextTaskMap.putIfAbsent(key, nextTaskInfoDTO);
            }
        }


        List<ApproveMultiInfoTaskVO> approveMultiInfoTaskList = new ArrayList<>();
        String key = null;
        for (String s : map.keySet()) {
            List<ApproveMultiInfoTaskVO> approveMultiInfoTaskVOS = map.get(s);
            if (approveMultiInfoTaskVOS.size() > approveMultiInfoTaskList.size()) {
                approveMultiInfoTaskList = approveMultiInfoTaskVOS;
                key = s;
            }
        }
        if (approveMultiInfoTaskList.size() > 0) {
            NextTaskInfoDTO nextTaskInfoDTO = nextTaskMap.get(key);
            getNextTaskUser(approveMultiInfoTaskList.get(0).getProcessInstanceId(), nextTaskInfoDTO);
            approveMultiInfoTaskDTO.setNextTaskInfo(nextTaskInfoDTO);
            approveMultiInfoTaskDTO.setList(approveMultiInfoTaskList);
            if (!nextTaskInfoDTO.getUsers().isEmpty()) {
                List<String> objectList = nextTaskInfoDTO.getUsers().stream().map(User::getId).collect(Collectors.toList());
                String spr = String.join(",", objectList);
                Iterator<ApproveMultiInfoTaskVO> iterator = approveMultiInfoTaskList.iterator();
                while (iterator.hasNext()) {
                    ApproveMultiInfoTaskVO next = iterator.next();
                    NextTaskInfoDTO nextTaskInfoDTOTemp = new NextTaskInfoDTO();
                    nextTaskInfoDTOTemp.setTaskId(key);
                    getNextTaskUser(next.getProcessInstanceId(), nextTaskInfoDTOTemp);
                    if (!nextTaskInfoDTOTemp.getUsers().isEmpty()) {
                        List<String> objectListTemp = nextTaskInfoDTOTemp.getUsers().stream().map(User::getId).collect(Collectors.toList());
                        String sprTemp = String.join(",", objectListTemp);
                        if (!spr.equals(sprTemp)) {
                            iterator.remove();
                        }
                    } else {
                        iterator.remove();
                    }

                }
            }
        }

        return approveMultiInfoTaskDTO;
    }

    @Override
    public NextTaskInfoDTOV2 setDeptUsers(NextTaskInfoDTO nextTaskInfoDTO) {
        NextTaskInfoDTOV2 nextTaskInfoDTOV2 = new NextTaskInfoDTOV2();
        nextTaskInfoDTOV2.setTaskId(nextTaskInfoDTO.getTaskId());
        nextTaskInfoDTOV2.setTaskName(nextTaskInfoDTO.getTaskName());
        nextTaskInfoDTOV2.setSpsj(nextTaskInfoDTO.getSpsj());
        nextTaskInfoDTOV2.setUserTaskConfig(nextTaskInfoDTO.getUserTaskConfig());
        Map<String, List<User>> map = NextTaskInfoUserHolder.getContext();
        if (map != null && map.size() > 0) {
            nextTaskInfoDTOV2.setUsers(map);
        } else {
            List<User> users = nextTaskInfoDTO.getUsers();
            map = new HashMap<>();
            for (User user : users) {
                String bm = user.getBm();
                if (StringUtils.isNotEmpty(bm)) {
                    Department department = departmentService.getById(bm);
                    if (department != null) {
                        List<User> userList = map.get(department.getName());
                        if (userList == null) {
                            userList = new ArrayList<User>();
                        }
                        userList.add(user);
                        map.put(department.getName(), userList);
                    }
                }
            }
        }
        nextTaskInfoDTOV2.setUsers(map);
        NextTaskInfoUserHolder.clearContext();
        return nextTaskInfoDTOV2;
    }
}
