package com.xjrsoft.module.workflow.service.impl;

import com.github.yulichang.base.MPJBaseServiceImpl;
import com.xjrsoft.module.workflow.entity.WorkflowCirculated;
import com.xjrsoft.module.workflow.mapper.WorkflowCirculatedMapper;
import com.xjrsoft.module.workflow.service.IWorkflowCirculatedService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 流程传阅信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-09
 */
@Service
public class WorkflowCirculatedServiceImpl extends MPJBaseServiceImpl<WorkflowCirculatedMapper, WorkflowCirculated> implements IWorkflowCirculatedService {

}
