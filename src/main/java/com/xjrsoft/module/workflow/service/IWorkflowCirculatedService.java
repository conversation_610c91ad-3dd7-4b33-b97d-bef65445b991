package com.xjrsoft.module.workflow.service;

import com.github.yulichang.base.MPJBaseService;
import com.xjrsoft.module.workflow.entity.WorkflowCirculated;
import com.baomidou.mybatisplus.extension.service.IService;
import com.xjrsoft.module.workflow.entity.WorkflowSchema;

/**
 * <p>
 * 流程传阅信息表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-09
 */
public interface IWorkflowCirculatedService extends MPJBaseService<WorkflowCirculated> {

}
