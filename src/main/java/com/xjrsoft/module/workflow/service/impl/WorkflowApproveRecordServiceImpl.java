package com.xjrsoft.module.workflow.service.impl;

import com.github.yulichang.base.MPJBaseServiceImpl;
import com.xjrsoft.module.workflow.entity.WorkflowApproveRecord;
import com.xjrsoft.module.workflow.mapper.WorkflowApproveRecordMapper;
import com.xjrsoft.module.workflow.service.IWorkflowApproveRecordService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-10
 */
@Service
public class WorkflowApproveRecordServiceImpl extends MPJBaseServiceImpl<WorkflowApproveRecordMapper, WorkflowApproveRecord> implements IWorkflowApproveRecordService {

}
