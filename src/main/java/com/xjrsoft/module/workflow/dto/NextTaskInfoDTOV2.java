package com.xjrsoft.module.workflow.dto;

import com.xjrsoft.module.organization.entity.User;
import com.xjrsoft.module.workflow.model.UserTaskConfig;
import lombok.Data;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * @ClassName NextTaskInfoDTO
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/6/18 0:26
 * @Version 1.0
 */
@Data
public class NextTaskInfoDTOV2 {
    private String taskId;
    private String taskName;
    private UserTaskConfig userTaskConfig;
    private Date spsj;
    private Map<String, List<User>> users;
}
