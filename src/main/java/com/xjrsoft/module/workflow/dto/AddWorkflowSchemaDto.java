package com.xjrsoft.module.workflow.dto;

import com.xjrsoft.module.workflow.model.ProcessConfig;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.Range;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * 新增流程设计dto
 *
 * @Author: tzx
 * @Date: 2022/9/5 15:26
 */
@Data
public class AddWorkflowSchemaDto implements Serializable {

    private static final long serialVersionUID = 1L;

    @NotNull(message = "流程配置不能为空")
    @Valid
    private ProcessConfig processConfig;

    @NotNull(message = "子节点配置不能为空")
    private List<Map<String, Object>> childNodeConfig;

}
