package com.xjrsoft.module.workflow.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xjrsoft.module.workflow.model.MemberConfig;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * @Author: tzx
 * @Date: 2022/9/9 10:19
 */
@Data
public class XsDto {

    @ApiModelProperty("流程实例id")
    @NotBlank(message = "流程实例id")
    private String processInstanceId;

    @ApiModelProperty("任务id")
    @NotBlank(message = "任务id")
    private String taskId;

}
