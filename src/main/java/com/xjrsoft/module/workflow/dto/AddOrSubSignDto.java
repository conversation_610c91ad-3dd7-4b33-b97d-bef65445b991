package com.xjrsoft.module.workflow.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @Author: tzx
 * @Date: 2022/11/29 15:45
 */
@Data
public class AddOrSubSignDto {

    /**
     * 任务id
     */
    @NotBlank(message = "任务id 不能为空！")
    private String taskId;

    /**
     * 模板id
     */
    @NotNull(message = "模板id 不能为空！")
    private Long schemaId;

    /**
     * 所选用户id
     */
    @NotNull(message = "所选用户不能为空！")
    private List<Long> userIds;

}
