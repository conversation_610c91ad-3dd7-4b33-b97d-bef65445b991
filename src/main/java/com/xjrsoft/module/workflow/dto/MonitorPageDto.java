package com.xjrsoft.module.workflow.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xjrsoft.common.page.PageInput;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NonNull;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * @Author: tzx
 * @Date: 2022/11/15 14:25
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class MonitorPageDto extends PageInput {

    @ApiModelProperty("开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    @ApiModelProperty("结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    /**
     * 0未完成 1完成
     */
    @ApiModelProperty("类型")
    @NotNull(message = "参数比如传入类型！0 代表未完成 1 代表完成")
    private Integer type;

}
