package com.xjrsoft.module.workflow.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @Author: tzx
 * @Date: 2023/2/10 14:13
 */
@Data
public class RecycleDeleteDto {
    @ApiModelProperty("流程id")
    @NotNull(message = "流程id 不能为空！")
    private List<String> processIds;
}
