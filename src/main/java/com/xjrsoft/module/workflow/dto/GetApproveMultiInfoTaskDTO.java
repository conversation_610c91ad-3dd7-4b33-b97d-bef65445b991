package com.xjrsoft.module.workflow.dto;

import com.xjrsoft.module.workflow.entity.WorkflowSchema;
import com.xjrsoft.module.workflow.model.UserTaskConfig;
import com.xjrsoft.module.workflow.vo.ApproveMultiInfoTaskVO;
import com.xjrsoft.module.workflow.vo.GetStartNodeInfoVO;
import lombok.Data;
import org.camunda.bpm.model.bpmn.instance.FlowNode;

/**
 * @ClassName GetApproveMultiInfoTaskDTO
 * @Description TODO
 * <AUTHOR>
 * @Date 2025/2/28 16:19
 * @Version 1.0
 */
@Data
public class GetApproveMultiInfoTaskDTO {
    private WorkflowSchema workflowSchema;
    private FlowNode flowNode;
    private GetStartNodeInfoVO getStartNodeInfoVO;
    private ApproveMultiInfoTaskVO approveMultiInfoTaskVO;
    private UserTaskConfig userTaskConfig;

}
