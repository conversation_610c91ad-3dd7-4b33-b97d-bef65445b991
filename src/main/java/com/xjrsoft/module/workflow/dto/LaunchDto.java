package com.xjrsoft.module.workflow.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * @Author: tzx
 * @Date: 2022/9/8 14:28
 */
@Data
public class LaunchDto {
    @ApiModelProperty("工作流模板id")
    @NotNull(message = "工作流模板id")
    private Long schemaId;

    @ApiModelProperty("表单数据，与 id 二选一")
    private Map<String,Map<String,Object>> formData;

    @ApiModelProperty("关联任务信息")
    private List<LaunchRelationTaskDto> relationTasks;

    @ApiModelProperty("所有上传文件的文件夹id")
    private List<Long> fileFolderIds;

    @ApiModelProperty("是否是老版本表单")
    private Map<String,Boolean> isOldSystem;

    @ApiModelProperty("下一级审批人")
    private List<String> userIds;

    @ApiModelProperty("驳回送审方式1逐级送审，2直接送审至本节点")
    private String bfssFs;
    @ApiModelProperty("数据主键，formData 二选一")
    private String id;

    @ApiModelProperty("审批时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date spsj;
}
