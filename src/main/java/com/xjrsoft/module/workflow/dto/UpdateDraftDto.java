package com.xjrsoft.module.workflow.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Map;

/**
 * @Author: tzx
 * @Date: 2022/11/4 10:48
 */
@Data
public class UpdateDraftDto {

    @ApiModelProperty("草稿id")
    @NotNull(message = "草稿id")
    private Long id;

    @ApiModelProperty("工作流模板id")
    @NotNull(message = "工作流模板id")
    private Long schemaId;

    @ApiModelProperty("表单数据")
    @NotNull(message = "表单数据不能为空")
    private Map<String, Map<String, Object>> formData;
}
