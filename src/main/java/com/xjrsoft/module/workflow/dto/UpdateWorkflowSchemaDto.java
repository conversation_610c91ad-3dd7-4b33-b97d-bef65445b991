package com.xjrsoft.module.workflow.dto;

import com.xjrsoft.module.workflow.model.ProcessConfig;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * @Author: tzx
 * @Date: 2022/9/21 14:35
 */
@Data
public class UpdateWorkflowSchemaDto implements Serializable {

    private static final long serialVersionUID = 1L;

    @NotNull(message = "流程配置id不能为空")
    private Long id;

    @NotNull(message = "流程配置不能为空")
    private ProcessConfig processConfig;

    @NotNull(message = "子节点配置不能为空")
    private List<Map<String, Object>> childNodeConfig;

}
