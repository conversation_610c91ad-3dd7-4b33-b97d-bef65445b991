package com.xjrsoft.module.workflow.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * @Author: tzx
 * @Date: 2022/11/30 10:49
 */
@Data
public class GetAssigneeDto {


    /**
     * 模板id
     */
    @NotNull(message = "模板id 不能为空！")
    private Long schemaId;

    /**
     * 任务id
     */
    @NotBlank(message = "任务id 不能为空！")
    private String taskId;
}
