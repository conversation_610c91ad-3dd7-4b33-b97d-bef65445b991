package com.xjrsoft.module.workflow.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;

/**
 * @Author: tzx
 * @Date: 2023/5/26 10:04
 */
@Data
public class ReLaunchDto {
    @ApiModelProperty("流程id")
    @NotBlank(message = "流程id不能为空！")
    private String processId;

    @ApiModelProperty("工作流模板id")
    @NotNull(message = "工作流模板id")
    private Long schemaId;

    @ApiModelProperty("表单数据")
    @NotNull(message = "表单数据不能为空")
    private Map<String, Map<String,Object>> formData;

    @ApiModelProperty("关联任务信息")
    private List<LaunchRelationTaskDto> relationTasks;

    @ApiModelProperty("所有上传文件的文件夹id")
    private List<Long> fileFolderIds;

    @ApiModelProperty("是否是老版本表单")
    private Map<String,Boolean> isOldSystem;

    @ApiModelProperty("下一级审批人")
    private List<String> userIds;

    @ApiModelProperty("驳回送审方式1逐级送审，2直接送审至本节点")
    private String bfssFs;
}
