package com.xjrsoft.module.workflow.dto;

import lombok.Data;
import org.camunda.bpm.engine.history.HistoricActivityInstance;
import org.camunda.bpm.engine.history.HistoricProcessInstance;

import java.util.List;

/**
 * @Author: tzx
 * @Date: 2023/2/15 9:02
 */
@Data
public class CanWithdrawDto {

    List<HistoricActivityInstance> historicActivityInstances;
    HistoricActivityInstance lastHistoricActivityInstance;
    HistoricProcessInstance historicProcessInstance;
}
