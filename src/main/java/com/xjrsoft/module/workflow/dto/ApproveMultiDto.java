package com.xjrsoft.module.workflow.dto;

import com.xjrsoft.module.workflow.model.MemberConfig;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;

/**
 * @Author: tzx
 * @Date: 2023/2/15 9:11
 */
@Data
public class ApproveMultiDto {

    @ApiModelProperty("流程任务id")
    @NotNull(message = "流程任务id不能为空")
    private List<String> taskIds;

    @ApiModelProperty("审批内容")
    private String approvedContent;

    @ApiModelProperty("审批类型 0 同意 1 拒绝 2 驳回 3 结束 4 其他（用户自定义按钮） ")
    @NotNull(message = "审批类型不能为空")
    private Integer approvedType;

    @ApiModelProperty("签章Id")
    private Long stampId;

    @ApiModelProperty("签章如果有密码 填写的密码")
    private String stampPassword;
}
