package com.xjrsoft.module.workflow.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xjrsoft.module.workflow.model.MemberConfig;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * @Author: tzx
 * @Date: 2022/9/9 10:19
 */
@Data
public class ApproveDto {

    @ApiModelProperty("流程任务id")
    @NotBlank(message = "流程任务id不能为空")
    private String taskId;

    @ApiModelProperty("审批内容")
    private String approvedContent;

    @ApiModelProperty("审批类型 0 同意 1 拒绝 2 驳回 3 结束 4 其他（用户自定义按钮） ")
    @NotNull(message = "审批类型不能为空")
    private Integer approvedType;

    @ApiModelProperty("审批结果 buttonConfig 的 buttonCode ")
    @NotNull(message = "审批结果按钮编码不能为空")
    private String approvedResult;

    @ApiModelProperty("表单数据")
    @NotNull(message = "表单数据不能为空")
    private Map<String, Map<String, Object>> formData;

    @ApiModelProperty("驳回节点activityId (传入节点activityId)")
    private String rejectNodeActivityId;

    @ApiModelProperty("下一级节点activityId (传入节点activityId)")
    private String nextNodeActivityId;

    @ApiModelProperty("传阅人信息")
    private List<MemberConfig> circulateConfigs = new ArrayList<>();

    @ApiModelProperty("签章Id")
    private Long stampId;

    @ApiModelProperty("签章如果有密码 填写的密码")
    private String stampPassword;

    @ApiModelProperty("所有上传文件的文件夹id")
    private List<Long> fileFolderIds = new ArrayList<>();

    @ApiModelProperty("是否是老版本表单")
    private Map<String, Boolean> isOldSystem;

    @ApiModelProperty("下一级审批人")
    private List<String> userIds;

    @ApiModelProperty("驳回送审方式1逐级送审，2直接送审至本节点")
    private String bfssFs;

    @ApiModelProperty("审批时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date spsj;

    @ApiModelProperty("异步审批0否1是")
    private String ybsp;
}
