package com.xjrsoft.module.workflow.dto;

import com.xjrsoft.module.organization.entity.User;
import com.xjrsoft.module.workflow.model.UserTaskConfig;
import lombok.Data;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * @ClassName NextTaskInfoDTO
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/6/18 0:26
 * @Version 1.0
 */
@Data
public class NextTaskInfoDTO {
    private String taskId;
    private String processInstanceId;
    private String taskName;
    private UserTaskConfig userTaskConfig;
    private Date spsj;
    List<User> users = new ArrayList<>();
}
