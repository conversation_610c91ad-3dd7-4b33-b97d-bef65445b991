package com.xjrsoft.module.workflow.dto;

import com.xjrsoft.common.page.PageInput;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * @Author: tzx
 * @Date: 2022/10/31 14:22
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class CirculatedTaskPageDto extends PageInput {

    /**
     * 开始时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    /**
     * 结束时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    /**
     * 模板名称
     */
    private String name;

    /**
     * 任务名称
     */
    private String taskName;

    /**
     * 发起人
     */
    private String originator;

    /**
     * 流水号
     */
    private String serialNumber;
}
