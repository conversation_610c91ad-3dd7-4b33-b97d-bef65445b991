package com.xjrsoft.module.workflow.dto;

import com.xjrsoft.common.page.PageInput;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @Author: tzx
 * @Date: 2022/9/6 14:45
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class WorkflowSchemaPageDto extends PageInput {
    /**
     * 分类
     */
    private Long category;

    /**
     * 名称
     */
    private String name;

    /**
     * 编码
     */
    private String code;

    /**
     * 启用
     */
    private Integer enabledMark;

    /**
     * 是否需要管控流程权限
     */
    private Boolean isAuth = Boolean.TRUE;

    /**
     * schemeinfoId
     */
    private String schemeinfoId;
}
