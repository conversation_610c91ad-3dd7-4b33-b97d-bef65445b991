package com.xjrsoft.module.workflow.dto;

import com.xjrsoft.common.page.PageInput;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * @Author: tzx
 * @Date: 2022/11/4 10:12
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class DraftPageDto extends PageInput {
    /**
     * 任务名称
     */
    private String name;


    /**
     * 发起人
     */
    private String originator;


    /**
     * 开始时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    /**
     * 结束时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;
}
