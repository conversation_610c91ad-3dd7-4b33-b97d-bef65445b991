package com.xjrsoft.module.workflow.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.xjrsoft.common.model.base.AuditEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <p>
 * 流程模板权限
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-04
 */
@TableName("xjr_workflow_schema_auth")
@ApiModel(value = "WorkflowSchemaAuth对象", description = "流程模板权限")
@Data
public class WorkflowSchemaAuth  implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键")
    private Long id;

    @ApiModelProperty("模板信息id")
    private Long schemaId;

    @ApiModelProperty("对应主键Id")
    private Long objId;

    @ApiModelProperty("对象名称")
    private String objName;

    @ApiModelProperty("对应对象类型 0用户 1 角色 2 岗位 -1所有人")
    private Integer objType;

}
