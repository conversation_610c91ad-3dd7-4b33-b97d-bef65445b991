package com.xjrsoft.module.workflow.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * 工作流 流程 与 表单关联表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-27
 */
@TableName("xjr_workflow_form_relation")
@ApiModel(value = "WorkflowFormRelation对象", description = "工作流 流程 与 表单关联表")
@Data
public class WorkflowFormRelation implements Serializable {

    private static final long serialVersionUID = 1L;

    private String id;

    @ApiModelProperty("流程id")
    private String processId;


//    @ApiModelProperty("流程id")
//    private String taskId;

    @ApiModelProperty("表单id（formTemplateId）")
    private String formId;

    @ApiModelProperty("表单配置key formKey 属性  用于区分同一template  多个表单数据")
    private String formKey;

    @ApiModelProperty("表单数据id")
    private String formKeyValue;

}
