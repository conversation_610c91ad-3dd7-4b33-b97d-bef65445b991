package com.xjrsoft.module.workflow.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 工作流 流转记录信息
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-10
 */
@TableName("xjr_workflow_record")
@ApiModel(value = "流转记录对象", description = "工作流 流转记录信息")
@Data
public class WorkflowRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    @ApiModelProperty("节点id")
    private String nodeId;

    @ApiModelProperty("节点类型")
    private String nodeType;

    @ApiModelProperty("节点名称")
    private String nodeName;

    @ApiModelProperty("节点多实例类型（节点审批类型）")
    private Integer nodeMultiType;

    @ApiModelProperty("模板id")
    private Long schemaId;

    @ApiModelProperty("流程id")
    private String processId;

    @ApiModelProperty("审批信息")
    private String message;

    @ApiModelProperty("记录时间")
    private LocalDateTime recordTime;

    @ApiModelProperty("传阅信息")
    private String circulateMessage;

    @TableField(fill = FieldFill.INSERT)
    private String createUserId;

    @ApiModelProperty("业务主键")
    private String bizId;

    @ApiModelProperty("驳回的节点源节点")
    private String sourceNode;
    @ApiModelProperty("驳回的节点目标节点")
    private String targetNode;
    @ApiModelProperty("驳回送审类型")
    private String bhType;
    @ApiModelProperty("审批结果")
    private String spjg;

    @ApiModelProperty("审批意见")
    private String spyj;

    @ApiModelProperty("流程定义key")
    private String taskDefKey;



}
