package com.xjrsoft.module.workflow.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-10
 */
@TableName("xjr_workflow_approve_record")
@ApiModel(value = "WorkflowApproveRecord对象", description = "工作流用户任务审批记录表")
@Data
public class WorkflowApproveRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键")
    private Long id;

    @ApiModelProperty("模板Id")
    private Long schemaId;

    @ApiModelProperty("流程id")
    private String processId;

    @ApiModelProperty("任务id")
    private String taskId;

    @ApiModelProperty("任务节点的定义key")
    private String taskDefinitionKey;

    @ApiModelProperty("任务名")
    private String taskName;

    @ApiModelProperty("审批类型")
    private Integer approveType;

    @ApiModelProperty("审批结果按钮值")
    private String approveResult;

    @ApiModelProperty("审批内容")
    private String approveComment;

    @ApiModelProperty("审批人id")
    private String approveUserId;

    @ApiModelProperty("审批人审批时所使用岗位")
    private String approveUserPostId;

    @ApiModelProperty("审批人姓名")
    @TableField(exist = false)
    private String approveUserName;

    @ApiModelProperty("审批时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime approveTime;

    @ApiModelProperty("签章id")
    private Long approveStamp;

    @ApiModelProperty("签章图片url")
    @TableField(exist = false)
    private String approveStampUrl;

    @ApiModelProperty("流水号")
    private Long serialNumber;

    @ApiModelProperty("当前进度")
    private Integer currentProgress;

    @ApiModelProperty("发起人id")
    private String startUserId;

    @ApiModelProperty("驳回的节点源节点")
    private String sourceNode;
    @ApiModelProperty("驳回的节点目标节点")
    private String targetNode;
    @ApiModelProperty("驳回送审类型")
    private String bhType;
    @ApiModelProperty("业务id")
    private String bizId;


}
