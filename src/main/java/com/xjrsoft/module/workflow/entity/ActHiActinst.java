package com.xjrsoft.module.workflow.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-08
 */
@Getter
@Setter
@TableName("ACT_HI_ACTINST")
@ApiModel(value = "ActHiActinst对象", description = "")
public class ActHiActinst implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId("ID_")
    private String id;

    @TableField("PARENT_ACT_INST_ID_")
    private String parentActInstId;

    @TableField("PROC_DEF_KEY_")
    private String procDefKey;

    @TableField("PROC_DEF_ID_")
    private String procDefId;

    @TableField("ROOT_PROC_INST_ID_")
    private String rootProcInstId;

    @TableField("PROC_INST_ID_")
    private String procInstId;

    @TableField("EXECUTION_ID_")
    private String executionId;

    @TableField("ACT_ID_")
    private String actId;

    @TableField("TASK_ID_")
    private String taskId;

    @TableField("CALL_PROC_INST_ID_")
    private String callProcInstId;

    @TableField("CALL_CASE_INST_ID_")
    private String callCaseInstId;

    @TableField("ACT_NAME_")
    private String actName;

    @TableField("ACT_TYPE_")
    private String actType;

    @TableField("ASSIGNEE_")
    private String assignee;

    @TableField("START_TIME_")
    private LocalDateTime startTime;

    @TableField("END_TIME_")
    private LocalDateTime endTime;

    @TableField("DURATION_")
    private BigDecimal duration;

    @TableField("ACT_INST_STATE_")
    private BigDecimal actInstState;

    @TableField("SEQUENCE_COUNTER_")
    private BigDecimal sequenceCounter;

    @TableField("TENANT_ID_")
    private String tenantId;

    @TableField("REMOVAL_TIME_")
    private LocalDateTime removalTime;
}
