package com.xjrsoft.module.workflow.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * 专项菜单表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-20
 */
@TableName("xjr_workflow_special_menu")
@ApiModel(value = "WorkflowSpecialMenu对象", description = "专项菜单表")
@Data
public class WorkflowSpecialMenu implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键")
    private Long id;

    @ApiModelProperty("功能编码")
    private String code;

    @ApiModelProperty("功能名称")
    private String name;

    @ApiModelProperty("菜单id")
    private String menuId;

    @ApiModelProperty("模板id")
    private String schemaId;

    @ApiModelProperty("模板权限类型 0 所有 1 指定")
    private Integer schemaAuthType;

    @ApiModelProperty("专项菜单使用人 如果 schema_auth_type == 1 才会有")
    private String schemaAuthUserId;

    @ApiModelProperty("字段权限 配置 0 所有 1指定")
    private Integer fieldAuthType;

    @ApiModelProperty("专项菜单字段权限 指定成员 如果 field_auth_type == 1 才会有")
    private String fieldAuthUserId;

    @ApiModelProperty("表单字段配置")
    private String fieldConfig;

    @ApiModelProperty("查询配置")
    private String queryConfig;

}
