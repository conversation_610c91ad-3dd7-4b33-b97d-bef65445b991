package com.xjrsoft.module.workflow.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 流程传阅信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-09
 */
@TableName("xjr_workflow_circulated")
@ApiModel(value = "WorkflowCirculated对象", description = "流程传阅信息表")
@Data
public class WorkflowCirculated implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键")
    private Long id;

    @ApiModelProperty("流程id")
    private String processId;

    @ApiModelProperty("流程id")
    private String processName;

    @ApiModelProperty("任务id")
    private String taskId;

    @ApiModelProperty("任务名")
    private String taskName;

    @ApiModelProperty("模板id")
    private Long schemaId;

    @ApiModelProperty("流水号")
    private Long serialNumber;

    @ApiModelProperty("当前进度")
    private Integer currentProgress;

    @ApiModelProperty("发起人id")
    private String startUserId;

    @ApiModelProperty("传阅人的用户id")
    private String circulatedUserId;

    @ApiModelProperty("发起时间")
    private LocalDateTime createTime;

    @ApiModelProperty("是否已读")
    private Integer isRead;


}
