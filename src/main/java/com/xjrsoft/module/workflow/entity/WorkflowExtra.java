package com.xjrsoft.module.workflow.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 *  工作流 拓展表
 *  id 必须自增 用于计算流水号！！！！！！！！！！！！！！！！
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-11
 */
@TableName("xjr_workflow_extra")
@ApiModel(value = "WorkflowExtra对象", description = "")
@Data
public class WorkflowExtra implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("流水号")
    private Long serialNumber;

    @ApiModelProperty("流程名")
    private String processName;

    @ApiModelProperty("任务id")
    private String taskId;

    @ApiModelProperty("任务name")
    private String taskName;

    @ApiModelProperty("任务key（XML中的key，数据库中的 TASK_DEF_KEY_）")
    private String taskKey;

    @ApiModelProperty("进度")
    private Integer currentProgress;

    @ApiModelProperty("流程模板id")
    private Long schemaId;

    @ApiModelProperty("流程模板名称")
    private String schemaName;

    @ApiModelProperty("流程id")
    private String processId;

    @ApiModelProperty("发起人id")
    private String startUserId;

    @ApiModelProperty("发起人名")
    private String startUserName;

    @ApiModelProperty("开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss[.SSS]")
    private LocalDateTime startTime;

    @ApiModelProperty("结束时间（审批时间）")
    private LocalDateTime endTime;

    @ApiModelProperty("任务发起时间")
    private LocalDateTime launchTime;

    @ApiModelProperty("审批人（如果是多实例 默认是一个）")
    private String approveUserIds;
}
