package com.xjrsoft.module.workflow.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.xjrsoft.common.model.base.AuditEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 流程模板草稿
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-04
 */
@TableName("xjr_workflow_schema_draft")
@ApiModel(value = "WorkflowSchemaDraft对象", description = "流程模板草稿")
@Data
@EqualsAndHashCode(callSuper = false)
public class WorkflowSchemaDraft extends AuditEntity {


    @ApiModelProperty("主键")
    private Long id;

    @ApiModelProperty("流程编码")
    private String code;

    @ApiModelProperty("流程模板名称")
    private String name;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("模板内容")
    private String xmlContent;

    @ApiModelProperty("模板Json")
    private String jsonContent;
}
