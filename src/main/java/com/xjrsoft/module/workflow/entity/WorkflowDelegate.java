package com.xjrsoft.module.workflow.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;

import com.xjrsoft.common.model.base.AuditEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 流程委托
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-05
 */
@TableName("xjr_workflow_delegate")
@ApiModel(value = "WorkflowDelegate对象", description = "流程委托")
@Data
@EqualsAndHashCode(callSuper = true)
public class WorkflowDelegate extends AuditEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    @ApiModelProperty("委托人")
    private String userId;

    @ApiModelProperty("被委托人")
    private String delegateUserIds;

    @ApiModelProperty("被委托人名称")
    private String delegateUserNames;

    @ApiModelProperty("委托模板ids 逗号隔开")
    private String schemaIds;

    @ApiModelProperty("开始时间")
    private LocalDateTime startTime;

    @ApiModelProperty("结束时间")
    private LocalDateTime endTime;

    @ApiModelProperty("说明备注")
    private String remark;


}
