package com.xjrsoft.module.workflow.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.xjrsoft.common.model.base.AuditEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <p>
 * 流程模板表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-04
 */
@TableName("xjr_workflow_schema")
@ApiModel(value = "WorkflowSchema对象", description = "流程模板表")
@Data
@EqualsAndHashCode(callSuper = true)
public class WorkflowSchema extends AuditEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键")
    private Long id;

    @ApiModelProperty("流程编码")
    private String code;

    @ApiModelProperty("流程模板名称")
    private String name;

    @ApiModelProperty("流程分类")
    private Long category;

    @ApiModelProperty("流程定义Id")
    private String definitionId;

    @ApiModelProperty("流程定义Key")
    private String definitionKey;

    @ApiModelProperty("部署ID")
    private String deploymentId;

    @ApiModelProperty("是否在App上允许发起 1允许 2不允许")
    private Integer appShow;

    @ApiModelProperty("表单id")
    private String formId;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("模板内容")
    private String xmlContent;

    @ApiModelProperty("模板Json")
    private String jsonContent;

    @ApiModelProperty("流程设计图")
    private String workflowChat;

    private Long  parentId;

    private String schemeId;



}
