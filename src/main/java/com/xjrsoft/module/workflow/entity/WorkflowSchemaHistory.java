package com.xjrsoft.module.workflow.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;

import com.xjrsoft.common.model.base.AuditEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 流程模板历史记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-16
 */
@TableName("xjr_workflow_schema_history")
@ApiModel(value = "WorkflowSchemaHistory对象", description = "流程模板历史记录表")
@Data
@EqualsAndHashCode(callSuper = true)
public class WorkflowSchemaHistory extends AuditEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    @ApiModelProperty("工作流模板id")
    private Long schemaId;

    @ApiModelProperty("版本")
    private Integer version;

    @ApiModelProperty("是否为活动版本")
    private Integer activityFlag;

    @ApiModelProperty("xml内容")
    private String xmlContent;

    @ApiModelProperty("json内容")
    private String jsonContent;

    @ApiModelProperty("流程图")
    private String workflowChat;

    @ApiModelProperty("流程定义Id")
    private String definitionId;

    @ApiModelProperty("流程定义Key")
    private String definitionKey;

    @ApiModelProperty("部署ID")
    private String deploymentId;


}
