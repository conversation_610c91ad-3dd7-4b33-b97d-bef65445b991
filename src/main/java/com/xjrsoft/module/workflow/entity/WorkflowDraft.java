package com.xjrsoft.module.workflow.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.xjrsoft.common.model.base.AuditEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 流程发起草稿
 * @Author: tzx
 * @Date: 2022/11/4 9:59
 */
@TableName("xjr_workflow_draft")
@ApiModel(value = "WorkflowSchema对象", description = "流程模板表")
@Data
@EqualsAndHashCode(callSuper = true)
public class WorkflowDraft extends AuditEntity {

    @ApiModelProperty("主键")
    private Long id;

    @ApiModelProperty("模板id")
    private Long schemaId;

    @ApiModelProperty("表单数据")
    private String formData;

    @ApiModelProperty("流程任务id")
    private String taskId;

    @ApiModelProperty("数据id")
    private String dataId;
}
