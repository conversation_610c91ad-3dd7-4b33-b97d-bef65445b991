package com.xjrsoft.module.system.entity;


import com.baomidou.mybatisplus.annotation.TableName;
import com.xjrsoft.common.model.base.AuditEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * 子系统
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-08
 */
@Data
@TableName("xjr_subsystem")
@ApiModel(value = "BaseSubsystem对象", description = "子系统")
public class Subsystem extends AuditEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键")
    private Long id;

    @ApiModelProperty("系统名")
    private String name;

    @ApiModelProperty("编码")
    private String code;

    @ApiModelProperty("图标")
    private String icon;

    @ApiModelProperty("排序吗")
    private Integer sortCode;


    @ApiModelProperty("备注")
    private String description;


    @ApiModelProperty("主页地址")
    private String indexUrl;

    @ApiModelProperty("翻译标记")
    private String lgmarkcode;


}
