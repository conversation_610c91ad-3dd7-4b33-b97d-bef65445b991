package com.xjrsoft.module.system.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 编号规则种子表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-23
 */
@TableName("xjr_code_rule_seed")
@ApiModel(value = "CodeRuleSeed对象", description = "编号规则种子表")
@Data
public class CodeRuleSeed implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键")
    private Long id;

    @ApiModelProperty("编码规则主键")
    private Long ruleId;

    @ApiModelProperty("用户主键")
    private String userId;

    @ApiModelProperty("种子值")
    private Integer seedValue;

    @ApiModelProperty("创建日期")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createDate;

    @ApiModelProperty("创建用户主键")
    @TableField(fill = FieldFill.INSERT)
    private String createUserId;

    @ApiModelProperty("修改日期")
    @TableField(fill = FieldFill.UPDATE)
    private LocalDateTime modifyDate;

    @ApiModelProperty("修改用户主键")
    @TableField(fill = FieldFill.UPDATE)
    private String modifyUserId;
}
