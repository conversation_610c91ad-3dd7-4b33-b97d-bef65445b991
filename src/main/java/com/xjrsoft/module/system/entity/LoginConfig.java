package com.xjrsoft.module.system.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xjrsoft.common.model.base.AuditEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * Login登录配置表【xjr_login_config】
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-20
 */
@TableName("xjr_login_config")
@ApiModel(value = "LoginConfig对象", description = "Login登录配置表【xjr_login_config】")
@Data
public class LoginConfig extends AuditEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键")
    private Long id;

    @ApiModelProperty("多端登录，存String形式，0-web，1-app")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String mulLogin;

    @ApiModelProperty("是否同端互斥,0-否，1-是")
    private Integer mutualExclusion;

    @ApiModelProperty("是否免登录,0-否，1-是")
    private Integer withoutLogin;

    @ApiModelProperty("密码策略,0-否，1-是")
    private Integer passwordStrategy;

    @ApiModelProperty("密码错误最大次数")
    private Integer strategyMaxNumber;

    @ApiModelProperty("https")
    private String https;
}
