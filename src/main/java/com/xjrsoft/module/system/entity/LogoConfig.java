package com.xjrsoft.module.system.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xjrsoft.common.model.base.AuditEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * Logo信息配置表【xjr_logo_config】
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-20
 */
@TableName("xjr_logo_config")
@ApiModel(value = "LogoConfig对象", description = "Logo信息配置表【xjr_logo_config】")
@Data
public class LogoConfig extends AuditEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键")
    private Long id;

    @ApiModelProperty("公司名称")
    private String companyName;

    @ApiModelProperty("公司简称")
    private String shortName;

    @ApiModelProperty("刷新logoId")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String refreshLogoId;

    @ApiModelProperty("登录页logoId")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String loginLogoId;

    @ApiModelProperty("菜单logoId")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String menuLogoId;

    @ApiModelProperty("设计器logoId")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String designerLogoId;

    @ApiModelProperty("登录页背景logoId")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String backgroundLogoId;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("地市名称")
    private String dsName;

    private String menuLogoDeepId;

    @ApiModelProperty("默认登录方式")
    private String defaultLoginType;

}
