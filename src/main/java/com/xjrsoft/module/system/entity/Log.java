package com.xjrsoft.module.system.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>
 * 系统日志表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-04
 */
@TableName("xjr_log")
@ApiModel(value = "Log对象", description = "系统日志表")
@Data
public class Log implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("日志主键")
    private Long id;

    @ApiModelProperty("分类Id 1-登陆2-访问3-操作4-异常")
    private Integer category;

    @ApiModelProperty("ip地址")
    private String ip;

    @ApiModelProperty("浏览器信息")
    private String userAgent;

    @ApiModelProperty("操作")
    private String operation;

    @ApiModelProperty("操作人")
    private String username;

    @ApiModelProperty("请求方式")
    private String method;

    @ApiModelProperty("请求地址")
    private String url;

    @ApiModelProperty("参数")
    private String params;

    @ApiModelProperty("请求时长")
    private Long time;

    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;


}
