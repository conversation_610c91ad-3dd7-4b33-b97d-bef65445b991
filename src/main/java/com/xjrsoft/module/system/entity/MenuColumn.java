package com.xjrsoft.module.system.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * 菜单列表字段
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-08
 */
@Data
@TableName("xjr_menu_column")
@ApiModel(value = "MenuColumn对象", description = "菜单列表字段")
public class MenuColumn implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("菜单主键")
    private String menuId;

    @ApiModelProperty("编码")
    private String code;

    @ApiModelProperty("名称")
    private String name;

    @ApiModelProperty("排序码")
    private Integer sortCode;

    @ApiModelProperty("描述")
    private String description;

    @ApiModelProperty("列表id")
    private String listId;

}
