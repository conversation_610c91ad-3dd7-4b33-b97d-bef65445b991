package com.xjrsoft.module.system.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.xjrsoft.common.model.base.AuditEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <p>
 * 数据字典详情
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-17
 */
@TableName("xjr_dictionary_detail")
@ApiModel(value = "DictionaryDetail对象", description = "数据字典详情")
@Data
@EqualsAndHashCode(callSuper = false)
public class DictionaryDetail extends AuditEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    @ApiModelProperty("名字")
    private String name;

    @ApiModelProperty("编码")
    private String code;

    @ApiModelProperty("字典项id")
    private Long itemId;

    @ApiModelProperty("值")
    private String value;

    @ApiModelProperty("排序号")
    private Integer sortCode;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("type code")
    private String dicTypeCode;

    @ApiModelProperty("type name")
    private String dicTypeName;
    private String url;
    private String hoverUrl;
}
