package com.xjrsoft.module.system.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;

import com.xjrsoft.common.model.base.AuditEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 数据源表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-08
 */
@Data
@TableName("xjr_datasource")
@ApiModel(value = "Datasource对象", description = "数据源表")
@EqualsAndHashCode(callSuper = false)
public class Datasource extends AuditEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键")
    private Long id;

    @ApiModelProperty("编号")
    private String code;

    @ApiModelProperty("名字")
    private String name;

    @ApiModelProperty("数据库主键")
    private Long databaselinkId;

    @ApiModelProperty("sql语句")
    private String sqlScript;

    @ApiModelProperty("排序码")
    private Integer sortCode;

    @ApiModelProperty("排序码")
    private String remark;


}
