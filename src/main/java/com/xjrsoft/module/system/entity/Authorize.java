package com.xjrsoft.module.system.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-16
 */
@Data
@TableName("xjr_authorize")
@ApiModel(value = "Authorize对象", description = "")
public class Authorize implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    private String roleId;

    private String objectId;

    private Integer authorizeType;


}
