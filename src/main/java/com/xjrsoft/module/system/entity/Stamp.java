package com.xjrsoft.module.system.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;

import com.xjrsoft.common.model.base.AuditEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 印章表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-24
 */
@TableName("xjr_stamp")
@ApiModel(value = "Stamp对象", description = "签章表")
@Data
@EqualsAndHashCode(callSuper = true)
public class Stamp extends AuditEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    @ApiModelProperty("签章名")
    private String name;

    @ApiModelProperty("签章分类")
    private Long stampCategory;

    @ApiModelProperty("签章类型 0私人签章 1 公共签章")
    private Integer stampType;

    @ApiModelProperty("1 上传图片，2 手写签名的类型")
    private Integer fileType;

    @ApiModelProperty("图片地址")
    private String fileUrl;

    @ApiModelProperty("密码")
    private String password;

    @ApiModelProperty("排序")
    private Integer sortCode;

    @ApiModelProperty("是否默认")
    private Integer isDefault;

    @ApiModelProperty("成员")
    private String member;

    @ApiModelProperty("维护人员")
    private String maintain;

    @ApiModelProperty("备注")
    private String remark;

}
