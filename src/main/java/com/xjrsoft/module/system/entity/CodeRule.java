package com.xjrsoft.module.system.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;

import com.xjrsoft.common.model.base.AuditEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>
 * 编号规则表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-23
 */
@TableName("xjr_code_rule")
@ApiModel(value = "CodeRule对象", description = "编号规则表")
@Data
public class CodeRule extends AuditEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键")
    private Long id;

    @ApiModelProperty("编号")
    private String code;

    @ApiModelProperty("名称")
    private String name;

    @ApiModelProperty("当前流水号")
    private String currentNumber;

    @ApiModelProperty("规则格式Json")
    private String formatJson;

    @ApiModelProperty("排序码")
    private Integer sortCode;

    @ApiModelProperty("备注")
    private String description;
}
