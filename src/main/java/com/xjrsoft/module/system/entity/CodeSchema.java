package com.xjrsoft.module.system.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.xjrsoft.common.model.base.AuditEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * 代码模板
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-29
 */
@Data
@TableName("xjr_code_schema")
@ApiModel(value = "CodeSchema对象", description = "代码模板")
public class CodeSchema extends AuditEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键")
    private Long id;

    @ApiModelProperty("名称")
    private String name;

    @ApiModelProperty("当前模板所关联的表单id")
    private String formId;

    @ApiModelProperty("类型（0-数据优先模板,1-界面优先模板,2-简易模板）")
    private Integer type;

    @ApiModelProperty("分类（关联数据字典）")
    private Long category;

    @ApiModelProperty("内容")
    private String content;

    @ApiModelProperty("描述")
    private String remark;

    @ApiModelProperty("模板状态（0-草稿，1-正式）")
    private Integer status;

    @ApiModelProperty("功能描述")
    private String description;
}
