package com.xjrsoft.module.system.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xjrsoft.common.model.base.AuditEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <p>
 * 数据字典项目
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-17
 */
@TableName("xjr_dictionary_item")
@ApiModel(value = "DictionaryItem对象", description = "数据字典项目")
@Data
@EqualsAndHashCode(callSuper = false)
public class DictionaryItem extends AuditEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;
    @TableField(value = "PARENT_ID")
    private Long parentId;

    @ApiModelProperty("名字")
    private String name;

    @ApiModelProperty("编码")
    private String code;

    @ApiModelProperty("排序号")
    private Integer sortCode;

    @ApiModelProperty("备注")
    private String remark;

}
