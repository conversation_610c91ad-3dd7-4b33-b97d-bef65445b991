package com.xjrsoft.module.system.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.xjrsoft.common.model.base.AuditEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <p>
 * 用户
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-16
 */
@Data
@TableName("xjr_menu")
@ApiModel(value = "Menu对象", description = "用户")
@EqualsAndHashCode(callSuper = false)
public class Menu extends AuditEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    private String id;

    @ApiModelProperty("上级Id")
    private String parentId;

    @ApiModelProperty("组件名（路由名称） --  与vue代码组件名必须一直 才能做到缓存页面 相关联")
    private String name;

    @ApiModelProperty("菜单名")
    private String title;

    @ApiModelProperty("菜单编号")
    private String code;

    @ApiModelProperty("菜单图标")
    private String icon;

    @ApiModelProperty("地址")
    private String path;

    @ApiModelProperty("组件地址")
    private String component;

    @ApiModelProperty("外链地址")
    private String iframeSrc;

    @ApiModelProperty("组件类型 默认组件 0 普通需要注册的组件 1 自定义表单 桌面设计 等已经默认注册进来的组件 ")
    private Integer componentType = 0;

    @ApiModelProperty("组件类型")
    private Integer menuType;

    @ApiModelProperty("菜单显示或者隐藏")
    private Integer display;

    @ApiModelProperty("是否允许修改")
    private Integer allowModify;

    @ApiModelProperty("是否允许删除")
    private Integer allowDelete;

    @ApiModelProperty("是否外链")
    private Integer outLink;

    @ApiModelProperty("页面持久化")
    private Integer keepAlive;

    @ApiModelProperty("排序码")
    private Integer sortCode;

    @ApiModelProperty("排序码")
    private String remark;

    @ApiModelProperty("系统主键（主系统默认为0）")
    private Long systemId;

    @ApiModelProperty("关联表单id（自定义表单 以及 代码生成器 生成的菜单才会有关联。）")
    private String formId;

    @ApiModelProperty("关联表单设计id")
    private String zyFormListMenuId;

    @ApiModelProperty("菜单额外配置")
    private String config;

    @ApiModelProperty("客户端类型，1PC2APP")
    private String clientType;

}
