package com.xjrsoft.module.system.service;

import com.github.yulichang.base.MPJBaseService;
import com.xjrsoft.module.system.dto.MenuTreeDto;
import com.xjrsoft.module.system.entity.Menu;
import com.baomidou.mybatisplus.extension.service.IService;
import com.xjrsoft.module.system.vo.MenuTreeVo;
import com.xjrsoft.module.system.vo.MenuVo;

import java.util.List;

/**
 * <p>
 * 用户 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-16
 */
public interface IMenuService extends MPJBaseService<Menu> {

    List<MenuVo> getAuthMenuList(MenuTreeDto dto);
}
