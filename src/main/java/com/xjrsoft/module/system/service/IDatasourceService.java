package com.xjrsoft.module.system.service;

import cn.hutool.db.Entity;
import com.github.yulichang.base.MPJBaseService;
import com.xjrsoft.module.system.dto.GetDataPageDto;
import com.xjrsoft.module.system.entity.Datasource;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 数据源表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-08
 */
public interface IDatasourceService extends MPJBaseService<Datasource> {

    /**
     * 根据datasourceid 获取数据源的列
     * @param id
     * @return
     */
    List<String> getColumns(Long id);

    /**
     * 根据datasourceid 获取数据源的数据
     * @param id
     * @return
     */
    List<Entity> getData(Long id);

    /**
     * 获取数据源的数据  分页
     * @param dto
     * @return
     */
    Map<String, Object> getDataPage(GetDataPageDto dto);
}
