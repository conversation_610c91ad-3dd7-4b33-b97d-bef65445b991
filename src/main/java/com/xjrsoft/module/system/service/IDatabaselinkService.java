package com.xjrsoft.module.system.service;

import cn.hutool.db.Entity;
import com.baomidou.mybatisplus.annotation.DbType;
import com.xjrsoft.common.model.datasource.MyColumnInfo;
import com.xjrsoft.common.model.datasource.MyTableInfo;
import com.xjrsoft.module.system.dto.AddDatabaseLinkDto;
import com.xjrsoft.module.system.dto.UpdateDatabaseLinkDto;
import com.xjrsoft.module.system.entity.Databaselink;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 数据库连接表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-04
 */
public interface IDatabaselinkService extends IService<Databaselink> {


    Boolean add(AddDatabaseLinkDto dto);

    Boolean update(UpdateDatabaseLinkDto dto);

    /**
     * 测试连接
     * @param databaselink
     * @return
     */
    Boolean test(Databaselink databaselink);

    /**
     * 获取所有表信息
     * @param id
     * @return
     */
    List<MyTableInfo> getTables(String id,String tableName);

    /**
     * 获取所有表数据
     * @param id
     * @return
     */
    List<Entity> getTablesData(String id, String tableName);

    /**
     * 获取表的所有字段信息
     * @param id
     * @param tableName
     * @return
     */
    List<MyColumnInfo> getTableColumns(String id, String tableName);


    /**
     * 获取表的所有字段信息
     * @param id
     * @param tableName
     * @return
     */
    List<String> getTableColumnName(String id, String tableName);

    /**
     * 获取多个表的所有字段西悉尼
     * @param id
     * @param tableNames
     * @return
     */
    Map<String, List<MyColumnInfo>> getMultiTableColumns(String id, String tableNames);


    /**
     * 获取多个表的所有字段名
     * @param id
     * @param tableNames
     * @return
     */
    Map<String, List<String>> getMultiTableColumnName(String id, String tableNames);

    DbType getDbType(String id);
}
