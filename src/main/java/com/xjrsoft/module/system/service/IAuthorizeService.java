package com.xjrsoft.module.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.xjrsoft.module.organization.dto.SetRoleAuthDto;
import com.xjrsoft.module.organization.vo.RoleAuthVo;
import com.xjrsoft.module.system.entity.Authorize;
import com.xjrsoft.module.system.vo.PermissionVo;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-16
 */
public interface IAuthorizeService extends IService<Authorize> {

    /**
     * 根据登陆人获取 权限
     *
     * @return
     */
    PermissionVo getPermissions(String clientType);

    /**
     *
     * @param dto 按钮id
     * @return 是否成功
     */
    Boolean setRoleAuth(SetRoleAuthDto dto);


    /**
     * 获取角色权限
     *
     * @param id 角色id
     * @return 角色权限
     */
    RoleAuthVo getRoleAuth(String id);

}
