package com.xjrsoft.module.system.service;

import com.alibaba.fastjson.JSONArray;
import com.github.yulichang.base.MPJBaseService;

import com.xjrsoft.module.system.dto.AddMemberDto;
import com.xjrsoft.module.system.dto.UpdateStampDto;
import com.xjrsoft.module.system.entity.Stamp;
import com.xjrsoft.module.system.vo.StampMemberVo;

import java.util.List;

/**
 * <p>
 * 印章表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-24
 */
public interface IStampService extends MPJBaseService<Stamp> {

    List<StampMemberVo> selectMember(Long id);

    boolean addMember(AddMemberDto dto);

    List<StampMemberVo> selectMaintain(Long id);

    boolean addMaintain(AddMemberDto dto);

    boolean updateStamp(UpdateStampDto dto);

    boolean deleteStamp(List<Long> ids);

    boolean enabled(Long id);

    boolean setDefaultStamp(Long id);


//    PageOutput stampPage(StampPageDto dto);
}
