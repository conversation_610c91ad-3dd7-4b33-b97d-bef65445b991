package com.xjrsoft.module.system.service;

import com.xjrsoft.module.system.entity.DictionaryItem;
import com.baomidou.mybatisplus.extension.service.IService;
import com.xjrsoft.module.system.vo.DictionaryItemDetailTreeVo;

import java.util.List;

/**
 * <p>
 * 数据字典项目 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-17
 */
public interface IDictionaryitemService extends IService<DictionaryItem> {

    List<DictionaryItemDetailTreeVo> getTree();
    List<DictionaryItem> load(Long itemId);

    void loadCaches();
}
