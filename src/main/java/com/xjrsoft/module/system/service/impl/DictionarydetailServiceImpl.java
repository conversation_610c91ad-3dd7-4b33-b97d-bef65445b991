package com.xjrsoft.module.system.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xjrsoft.common.constant.GlobalConstant;
import com.xjrsoft.common.utils.RedisUtil;
import com.xjrsoft.module.system.entity.DictionaryDetail;
import com.xjrsoft.module.system.mapper.DictionarydetailMapper;
import com.xjrsoft.module.system.service.IDictionarydetailService;
import com.xjrsoft.module.zy.form.service.XjrDictionaryItemService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 数据字典详情 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-17
 */
@Service
@Slf4j
@AllArgsConstructor
public class DictionarydetailServiceImpl extends ServiceImpl<DictionarydetailMapper, DictionaryDetail> implements IDictionarydetailService {

    private RedisUtil redisUtil;
    private XjrDictionaryItemService xjrDictionaryItemService;



    @Async

    public void loadCaches() {
        log.info("XJRSOFT: 加载所有数据字典详情缓存开始");
        List<DictionaryDetail> list = this.list();
        redisUtil.set(GlobalConstant.DIC_DETAIL_CACHE_KEY, list);
        log.info("XJRSOFT: 加载所有数据字典详情缓存结束");
        xjrDictionaryItemService.initDic();
    }
}
