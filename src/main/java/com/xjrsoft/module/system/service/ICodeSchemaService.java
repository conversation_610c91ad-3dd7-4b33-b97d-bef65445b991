package com.xjrsoft.module.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.github.yulichang.base.MPJBaseService;
import com.xjrsoft.common.page.PageOutput;
import com.xjrsoft.module.system.dto.CodeSchemaPageDto;
import com.xjrsoft.module.system.entity.CodeSchema;
import com.xjrsoft.module.system.vo.CodeSchemaPageVo;

/**
 * <p>
 * 代码模板 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-29
 */
public interface ICodeSchemaService extends MPJBaseService<CodeSchema> {

    PageOutput<CodeSchemaPageVo> page(CodeSchemaPageDto dto);
}
