package com.xjrsoft.module.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.github.yulichang.base.MPJBaseService;
import com.xjrsoft.common.page.PageInput;
import com.xjrsoft.common.page.PageOutput;
import com.xjrsoft.module.system.entity.CodeRule;
import com.xjrsoft.module.system.vo.CodeRuleVo;

import java.util.List;

/**
 * <p>
 * 编号规则表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-23
 */
public interface ICodeRuleService extends MPJBaseService<CodeRule> {

    PageOutput<CodeRuleVo> getCodeRulePageList(PageInput dto);

    String genEncode(String encode);

    boolean useEncode(String encode);

    boolean useEncode(List<String> encodeList);
}
