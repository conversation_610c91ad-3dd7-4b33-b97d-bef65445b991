package com.xjrsoft.module.system.service.impl;

import cn.dev33.satoken.session.SaSession;
import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;

import com.xjrsoft.common.constant.GlobalConstant;
import com.xjrsoft.common.enums.EnabledMark;
import com.xjrsoft.common.utils.RedisUtil;

import com.xjrsoft.config.LicenseConfig;
import com.xjrsoft.config.WechatEnterpriseConfig;
import com.xjrsoft.module.organization.entity.*;
import com.xjrsoft.module.organization.service.*;
import com.xjrsoft.module.system.model.DingTalkModel;
import com.xjrsoft.module.system.model.DingTalkUserInfoModel;
import com.xjrsoft.module.system.model.WechatEnterpriseModel;
import com.xjrsoft.module.system.model.WechatEnterpriseUserInfoModel;
import com.xjrsoft.module.system.service.IOauthService;
import com.xkcoding.http.config.HttpConfig;
import lombok.AllArgsConstructor;
import lombok.SneakyThrows;
import me.zhyd.oauth.config.AuthConfig;
import me.zhyd.oauth.enums.scope.*;
import me.zhyd.oauth.exception.AuthException;
import me.zhyd.oauth.model.AuthCallback;
import me.zhyd.oauth.model.AuthResponse;
import me.zhyd.oauth.request.*;
import me.zhyd.oauth.utils.AuthScopeUtils;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.net.InetSocketAddress;
import java.net.Proxy;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Author: tzx
 * @Date: 2023/8/8 10:40
 */
@Service
@AllArgsConstructor
public class OauthServiceImpl implements IOauthService {

    private final IUserService userService;

    private final IUserDeptRelationService userDeptRelationService;

    private final IUserPostRelationService userPostRelationService;

    private final IPostService postService;

    private final IDepartmentService departmentService;

    private final RedisUtil redisUtil;


    private final LicenseConfig licenseConfig;


    private final WechatEnterpriseConfig wechatEnterpriseConfig;

    @Override
    @SneakyThrows
    public void oAuthLogin(String source, AuthCallback callback, HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse) {

        String prevPage = httpServletRequest.getHeader("Referer");

        if (licenseConfig.getEnabled()) {
            //查出所有在线用户
            List<String> onlineUser = StpUtil.searchSessionId("", 0, Integer.MAX_VALUE,false);

            //如果已经登录人数超过授权人数  不允许登录
            if (onlineUser.size() >= licenseConfig.getLoginMax()) {
                httpServletResponse.sendRedirect("http://localhost:3100/#/login?error=登录人数超过授权人数，无法登录，请联系管理员！");
            }
        }

        AuthRequest authRequest = getAuthRequest(source);
        AuthResponse response = authRequest.login(callback);


        User loginUser = new User();

        if(response.getCode() == 2000){


        }
        else {
            httpServletResponse.sendRedirect("http://localhost:3100/#/login?error=" + response.getMsg());
        }

        if (loginUser.getEnabledMark() == EnabledMark.DISABLED.getCode()) {

            httpServletResponse.sendRedirect("http://localhost:3100/#/login?error=账户未启用");
            return;
        }

        //此登录接口登录web端
        StpUtil.login(loginUser.getId(), "PC");

        SaSession tokenSession = StpUtil.getTokenSession();

        List<UserDeptRelation> userDeptRelations = userDeptRelationService.list(Wrappers.lambdaQuery(UserDeptRelation.class)
                .eq(UserDeptRelation::getUserId, StpUtil.getLoginIdAsString()));

        List<UserPostRelation> userPostRelations = userPostRelationService.list(Wrappers.lambdaQuery(UserPostRelation.class)
                .eq(UserPostRelation::getUserId, StpUtil.getLoginIdAsString()));

        //获取登陆人所选择的身份缓存
        String postId = redisUtil.get(GlobalConstant.LOGIN_IDENTITY_CACHE_PREFIX + loginUser.getId());

        Post post = new Post();
        if (StrUtil.isNotBlank(postId)) {
            post = postService.getById(postId);
        }

        if (userPostRelations.size() > 0) {
            List<String> postIds = userPostRelations.stream().map(UserPostRelation::getPostId).collect(Collectors.toList());

            List<Post> postList = postService.listByIds(postIds);
            if (StrUtil.isBlank(postId) && CollectionUtils.isNotEmpty(postList)) {
                post = postList.get(0);
            }
            tokenSession.set(GlobalConstant.LOGIN_USER_POST_INFO_KEY, post);
            tokenSession.set(GlobalConstant.LOGIN_USER_POST_LIST_KEY, postList);
            loginUser.setPostId(post.getId());

            //将登陆人所选择的身份缓存起来
            //切换身份的时候 会一起修改
            redisUtil.set(GlobalConstant.LOGIN_IDENTITY_CACHE_PREFIX + loginUser.getId(), post.getId());
        }

        if (userDeptRelations.size() > 0) {
            // 存当前用户所有部门到缓存
            List<String> departmentIds = userDeptRelations.stream().map(UserDeptRelation::getDeptId).collect(Collectors.toList());
            List<Department> departmentList = departmentService.listByIds(departmentIds);
            tokenSession.set(GlobalConstant.LOGIN_USER_DEPT_LIST_KEY, departmentList);
            //如果此人有岗位  使用岗位的deptId  找到当前组织机构
            if (ObjectUtil.isNotNull(post.getId())) {
                Department department = departmentService.getById(post.getDeptId());
                tokenSession.set(GlobalConstant.LOGIN_USER_DEPT_INFO_KEY, department);
                loginUser.setDepartmentId(department.getId());
            } else {

                Department department = departmentList.get(0);

                tokenSession.set(GlobalConstant.LOGIN_USER_DEPT_INFO_KEY, department);

                loginUser.setDepartmentId(department.getId());
            }

        }

        //根据登录信息  将post  和 department 信息存入用户信息中
        tokenSession.set(GlobalConstant.LOGIN_USER_INFO_KEY, loginUser);

        httpServletResponse.sendRedirect("http://localhost:3100/#/login?token=" + StpUtil.getTokenValue());

    }

    /**
     * 根据具体的授权来源，获取授权请求工具类
     *
     * @param source
     * @return
     */
    public AuthRequest getAuthRequest(String source) {
        AuthRequest authRequest = null;

        return authRequest;
    }



}
