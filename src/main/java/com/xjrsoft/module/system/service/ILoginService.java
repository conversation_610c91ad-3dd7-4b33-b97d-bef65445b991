package com.xjrsoft.module.system.service;

import com.xjrsoft.module.system.dto.CreateTokenDto;
import com.xjrsoft.module.system.dto.LoginDto;
import com.xjrsoft.module.system.vo.CreateTokenVo;
import com.xjrsoft.module.system.vo.LoginVo;
import org.springframework.stereotype.Service;

/**
 * @Author: tzx
 * @Date: 2023/4/21 14:20
 */
public interface ILoginService {

    LoginVo login(LoginDto dto);

    CreateTokenVo createToken(CreateTokenDto dto);
}
