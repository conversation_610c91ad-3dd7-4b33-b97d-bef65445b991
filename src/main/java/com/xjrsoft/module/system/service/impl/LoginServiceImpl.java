package com.xjrsoft.module.system.service.impl;

import cn.dev33.satoken.secure.SaSecureUtil;
import cn.dev33.satoken.session.SaSession;
import cn.dev33.satoken.stp.SaLoginModel;
import cn.dev33.satoken.stp.StpUtil;
import cn.dev33.satoken.temp.SaTempUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.xjrsoft.common.constant.GlobalConstant;
import com.xjrsoft.common.enums.EnabledMark;
import com.xjrsoft.common.enums.YesOrNoEnum;
import com.xjrsoft.common.exception.MyException;
import com.xjrsoft.common.utils.RedisUtil;
import com.xjrsoft.config.LicenseConfig;
import com.xjrsoft.encrypt.CertUtil;
import com.xjrsoft.module.organization.entity.*;
import com.xjrsoft.module.organization.service.*;
import com.xjrsoft.module.system.dto.CreateTokenDto;
import com.xjrsoft.module.system.dto.LoginDto;
import com.xjrsoft.module.system.entity.LoginConfig;
import com.xjrsoft.module.system.service.ILoginConfigService;
import com.xjrsoft.module.system.service.ILoginService;
import com.xjrsoft.module.system.vo.CreateTokenVo;
import com.xjrsoft.module.system.vo.LoginVo;
import com.xjrsoft.module.system.vo.UserHj;
import com.xjrsoft.module.zy.form.pojo.entity.XjrUserGzhj;
import com.xjrsoft.module.zy.form.service.XjrUserGzhjService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;

import java.io.IOException;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Author: tzx
 * @Date: 2023/4/21 14:22
 */
@Service
@AllArgsConstructor
public class LoginServiceImpl implements ILoginService {

    private final IUserService userService;

    private final IUserDeptRelationService userDeptRelationService;

    private final IUserPostRelationService userPostRelationService;

    private final IPostService postService;

    private final IDepartmentService departmentService;

    private final RedisUtil redisUtil;


    private final LicenseConfig licenseConfig;

    private final ILoginConfigService loginConfigService;
    private final XjrUserGzhjService xjrUserGzhjService;

    @Override
    public LoginVo login(@RequestBody(required = false) LoginDto dto) {
        LoginVo result = new LoginVo();

        if (licenseConfig.getEnabled()) {
            //查出所有在线用户
            List<String> onlineUser = StpUtil.searchSessionId("", 0, Integer.MAX_VALUE, false);

            //如果已经登录人数超过授权人数  不允许登录
            if (onlineUser.size() >= licenseConfig.getLoginMax()) {
                throw new MyException("登录人数超过授权人数，无法登录，请联系管理员！");
            }
        }
        User loginUser = null;
        //用户名长度
        int userNameLength = dto.getUserName().length();
        if (dto.getUserName().startsWith("PKI_")) {
            String signedData = dto.getUserName().substring(4, userNameLength).trim();
            try {
                String sfzh = CertUtil.getX509CertSfzh(signedData);
                dto.setTh3Login(true);
                LambdaQueryWrapper<User> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(User::getIdentityCardNumber, sfzh);
                loginUser = userService.getOne(queryWrapper);
            } catch (IOException e) {

            }
        }else if (dto.getUserName().startsWith("APP_")) {
            String sfzh = dto.getUserName().substring(4, userNameLength).trim();
            try {
                dto.setTh3Login(true);
                LambdaQueryWrapper<User> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(User::getIdentityCardNumber, sfzh);
                loginUser = userService.getOne(queryWrapper);
            } catch (Exception e) {

            }
        }
        else {
            try {
                if (dto.getUserName().contains("@")) {
                    dto.setUserName(dto.getUserName().replace("@", ""));
                    dto.setTh3Login(true);
                }
                LambdaQueryWrapper<User> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(User::getUserName, dto.getUserName());
                loginUser = userService.getOne(queryWrapper);
            } catch (Exception e) {
                throw new MyException(e.getMessage());
            }
        }


        List<LoginConfig> list = loginConfigService.list();
        if (list.size() == 0) {//如果没有，则设置为默认配置,并进行保存
            LoginConfig loginConfig = new LoginConfig();
            loginConfig.setId(1L);
            loginConfig.setMulLogin("0,1");
            loginConfig.setMutualExclusion(YesOrNoEnum.NO.getCode());
            loginConfig.setWithoutLogin(YesOrNoEnum.YES.getCode());
            loginConfig.setPasswordStrategy(YesOrNoEnum.YES.getCode());
            loginConfig.setStrategyMaxNumber(7);
            list.add(loginConfig);
            loginConfigService.save(loginConfig);
        }
        LoginConfig loginConfig = list.get(0);

        if (StrUtil.isNotBlank(loginConfig.getMulLogin())) {
            if (ObjectUtil.isNotEmpty(dto.getDeviceType()) && dto.getDeviceType() == 0 && !loginConfig.getMulLogin().contains("0")) {
                throw new MyException("当前Web端登录未授权，请联系管理员！");
            } else if (ObjectUtil.isNotEmpty(dto.getDeviceType()) && dto.getDeviceType() == 1 && !loginConfig.getMulLogin().contains("1")) {
                throw new MyException("当前APP端登录未授权，请联系管理员！");
            }
        } else {
            throw new MyException("当前Web端、app端登录未授权，请联系管理员！");
        }
        if (loginUser == null) {
            throw new MyException("账号不存在");
        }
        if (!dto.isTh3Login()) {
            if (!StrUtil.equals(loginUser.getPassword(), SaSecureUtil.md5BySalt(dto.getPassword(), GlobalConstant.SECRET_KEY))) {
                throw new MyException("密码不正确!");
            }
            if (loginUser.getEnabledMark() == EnabledMark.DISABLED.getCode()) {
                throw new MyException("当前账号已被锁定，请联系管理员!");
            }
        }
        //登录成功之后将对应的密码错误次数给删除掉
        redisUtil.delete(GlobalConstant.LOGIN_ERROR_NUMBER + loginUser.getId());

//        SaTokenConfig oldConfig = SaManager.getConfig();
//
//        if (loginConfig.getMutualExclusion() == YesOrNoEnum.NO.getCode()){
//            //不开启同端互斥
//            oldConfig.setIsConcurrent(true);
//        }else {
//            //开启同端互斥
//            oldConfig.setIsConcurrent(false);
//        }
//        // 注入到 SaManager 中
//        SaManager.setConfig(oldConfig);

        //此登录接口
        if (loginConfig.getWithoutLogin() == YesOrNoEnum.YES.getCode()) {//开启就设置为7天免登录
            if (ObjectUtil.isNotEmpty(dto.getDeviceType()) && dto.getDeviceType() == 1) {
                StpUtil.login(loginUser.getId(), new SaLoginModel().setDevice("APP").setTimeout(60 * 60 * 24 * 7));
            } else {//默认为PC端登录
                StpUtil.login(loginUser.getId(), new SaLoginModel().setDevice("PC").setTimeout(60 * 60 * 24 * 7));
            }
        } else {
            //此登录接口登录web端,IsLastingCookie设置为false，也就是关闭浏览器后再次打开需要重新登录
            if (ObjectUtil.isNotEmpty(dto.getDeviceType()) && dto.getDeviceType() == 1) {
                StpUtil.login(loginUser.getId(), new SaLoginModel().setDevice("APP").setIsLastingCookie(false));
            } else {//默认为PC端登录
                StpUtil.login(loginUser.getId(), new SaLoginModel().setDevice("PC").setIsLastingCookie(false));
            }
        }
//        StpUtil.login(loginUser.getId(),"PC");

        SaSession tokenSession = StpUtil.getTokenSession();

        List<UserDeptRelation> userDeptRelations = userDeptRelationService.list(Wrappers.lambdaQuery(UserDeptRelation.class)
                .eq(UserDeptRelation::getUserId, StpUtil.getLoginIdAsString()));

        List<UserPostRelation> userPostRelations = userPostRelationService.list(Wrappers.lambdaQuery(UserPostRelation.class)
                .eq(UserPostRelation::getUserId, StpUtil.getLoginIdAsString()));

        //获取登陆人所选择的身份缓存
        String postId = redisUtil.get(GlobalConstant.LOGIN_IDENTITY_CACHE_PREFIX + loginUser.getId());

        Post post = new Post();
        if (StrUtil.isNotBlank(postId) && !postId.equals("null")) {
            post = postService.getById(Long.valueOf(postId));
        }

        if (userPostRelations.size() > 0) {
            List<String> postIds = userPostRelations.stream().map(UserPostRelation::getPostId).collect(Collectors.toList());

            List<Post> postList = postService.listByIds(postIds);
            if ((post == null || StrUtil.isBlank(postId)) && CollectionUtils.isNotEmpty(postList)) {
                post = postList.get(0);
            }
            tokenSession.set(GlobalConstant.LOGIN_USER_POST_INFO_KEY, post);
            tokenSession.set(GlobalConstant.LOGIN_USER_POST_LIST_KEY, postList);
            loginUser.setPostId(post.getId());

            //将登陆人所选择的身份缓存起来
            //切换身份的时候 会一起修改
            redisUtil.set(GlobalConstant.LOGIN_IDENTITY_CACHE_PREFIX + loginUser.getId(), post.getId());
        }

        if (userDeptRelations.size() > 0) {
            // 存当前用户所有部门到缓存
            List<String> departmentIds = userDeptRelations.stream().map(UserDeptRelation::getDeptId).collect(Collectors.toList());
            List<Department> departmentList = departmentService.listByIds(departmentIds);
            tokenSession.set(GlobalConstant.LOGIN_USER_DEPT_LIST_KEY, departmentList);
            //如果此人有岗位  使用岗位的deptId  找到当前组织机构
            if (ObjectUtil.isNotNull(post.getId())) {
                Department department = departmentService.getById(post.getDeptId());
                tokenSession.set(GlobalConstant.LOGIN_USER_DEPT_INFO_KEY, department);
                loginUser.setDepartmentId(department.getId());
            } else {

                Department department = departmentList.get(0);

                tokenSession.set(GlobalConstant.LOGIN_USER_DEPT_INFO_KEY, department);

                loginUser.setDepartmentId(department.getId());
            }

        }

        //根据登录信息  将post  和 department 信息存入用户信息中
        tokenSession.set(GlobalConstant.LOGIN_USER_INFO_KEY, loginUser);
        UserHj userHj = new UserHj();
        userHj.setId(loginUser.getId());
        userHj.setDw(loginUser.getDw());
        if (StrUtil.isNotBlank(loginUser.getDw())) {
            List<String> dws = new ArrayList<>();
            List<String> dwCodes = new ArrayList<>();
            String[] strings = loginUser.getDw().split(StringPool.COMMA);
            for (String string : strings) {
                Department department = departmentService.getById(string);
                if (department == null) {
                    continue;
                }
                dws.add(department.getName());
                dwCodes.add(department.getCode());
            }
            //dws转,分割的字符串
            String dwName = String.join(StringPool.COMMA, dws);
            userHj.setDwName(dwName);
            userHj.setDwCode(String.join(StringPool.COMMA, dwCodes));

        }
        if (StrUtil.isNotBlank(loginUser.getBm())) {
            List<String> bms = new ArrayList<>();
            List<String> bmCodes = new ArrayList<>();
            String[] strings = loginUser.getBm().split(StringPool.COMMA);
            for (String string : strings) {
                Department department = departmentService.getById(string);
                if (department == null) {
                    continue;
                }
                bms.add(department.getName());
                bmCodes.add(department.getCode());
            }
            String bmName = String.join(StringPool.COMMA, bms);
            userHj.setBmName(bmName);
            userHj.setBmCode(String.join(StringPool.COMMA, bmCodes));

        }
        userHj.setBm(loginUser.getBm());
        userHj.setYwrq(new Date());
        userHj.setNd(String.valueOf(LocalDate.now().getYear()));
        userHj.setQj(String.valueOf(LocalDate.now().getMonthValue()));
        userHj.setName(loginUser.getName());
        userHj.setUserName(loginUser.getUserName());
        XjrUserGzhj xjrUserGzhj = xjrUserGzhjService.getById(loginUser.getId());
        if (xjrUserGzhj == null) {
            xjrUserGzhj = new XjrUserGzhj();
            BeanUtil.copyProperties(userHj, xjrUserGzhj);
            xjrUserGzhjService.save(xjrUserGzhj);
        }
        result.setToken(StpUtil.getTokenValue());
        return result;
    }

    @Override
    public CreateTokenVo createToken(CreateTokenDto dto) {
        CreateTokenVo vo = new CreateTokenVo();

        if (dto.getExpire() == -1) {
            String token = SaTempUtil.createToken(IdUtil.fastSimpleUUID() + StringPool.UNDERSCORE + GlobalConstant.SECRET_KEY, Integer.MAX_VALUE);
            vo.setToken(token);
            return vo;
        } else {
            String token = SaTempUtil.createToken(IdUtil.fastSimpleUUID() + StringPool.UNDERSCORE + GlobalConstant.SECRET_KEY, dto.getExpire());
            vo.setToken(token);
            return vo;
        }

    }

}
