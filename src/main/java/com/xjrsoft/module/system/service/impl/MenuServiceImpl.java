package com.xjrsoft.module.system.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.yulichang.base.MPJBaseServiceImpl;
import com.github.yulichang.toolkit.MPJWrappers;
import com.xjrsoft.common.constant.GlobalConstant;
import com.xjrsoft.common.enums.AuthorizeType;
import com.xjrsoft.common.enums.EnabledMark;
import com.xjrsoft.common.utils.VoToColumnUtil;
import com.xjrsoft.module.organization.entity.UserRoleRelation;
import com.xjrsoft.module.organization.mapper.UserRoleRelationMapper;
import com.xjrsoft.module.system.dto.MenuTreeDto;
import com.xjrsoft.module.system.entity.Authorize;
import com.xjrsoft.module.system.entity.Menu;
import com.xjrsoft.module.system.entity.Subsystem;
import com.xjrsoft.module.system.mapper.AuthorizeMapper;
import com.xjrsoft.module.system.mapper.MenuMapper;
import com.xjrsoft.module.system.service.IMenuService;
import com.xjrsoft.module.system.vo.MenuTreeVo;
import com.xjrsoft.module.system.vo.MenuVo;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 用户 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-16
 */
@Service
@AllArgsConstructor
public class MenuServiceImpl extends MPJBaseServiceImpl<MenuMapper, Menu> implements IMenuService {

    private final UserRoleRelationMapper userRoleRelationMapper;

    private final AuthorizeMapper authorizeMapper;

    @Override
    public List<MenuVo> getAuthMenuList(MenuTreeDto dto) {
        List<UserRoleRelation> relations = userRoleRelationMapper.selectList(Wrappers.lambdaQuery(UserRoleRelation.class)
                .select(UserRoleRelation::getRoleId)
                .eq(UserRoleRelation::getUserId, StpUtil.getLoginIdAsString()));
        if (CollectionUtils.isEmpty(relations)) {
            return new ArrayList<>();
        }
        List<String> roleIds = relations.stream().map(UserRoleRelation::getRoleId).collect(Collectors.toList());
        List<String> authMenuIdList = null;
        // 非超级管理员权限过滤
        if (!roleIds.contains(GlobalConstant.SUPER_ADMIN_ROLE_ID)) {
            List<Authorize> authorizeList = authorizeMapper.selectList(Wrappers.<Authorize>lambdaQuery()
                    .eq(Authorize::getAuthorizeType, AuthorizeType.MENU.getCode())
                    .in(Authorize::getRoleId, roleIds));
            if (CollectionUtils.isEmpty(authorizeList)) {
                return new ArrayList<>();
            }
            authMenuIdList = authorizeList.stream().map(Authorize::getObjectId).collect(Collectors.toList());
        }

        return this.selectJoinList(MenuVo.class,
                MPJWrappers.<Menu>lambdaJoin()
                    .disableSubLogicDel()
                    .like(StrUtil.isNotBlank(dto.getTitle()), Menu::getTitle, dto.getTitle())
                    .like(StrUtil.isNotBlank(dto.getCode()), Menu::getCode, dto.getCode())
                    .like(StrUtil.isNotBlank(dto.getName()), Menu::getName, dto.getName())
    //                .like(ObjectUtil.isNotEmpty(dto.getEnabledMark()), Menu::getEnabledMark, dto.getEnabledMark())
                    .in(CollectionUtils.isNotEmpty(authMenuIdList), Menu::getId, authMenuIdList)
                    .select(Menu::getId)
                    .selectAs(Subsystem::getName, MenuVo::getSystemName)
                    .select(Menu.class, x -> VoToColumnUtil.fieldsToColumns(MenuTreeVo.class).contains(x.getProperty()))
                    .leftJoin(Subsystem.class, Subsystem::getId, Menu::getSystemId)
                    .eq(Menu::getEnabledMark, EnabledMark.ENABLED.getCode())
                    .orderByAsc(Menu::getSortCode));
    }
}
