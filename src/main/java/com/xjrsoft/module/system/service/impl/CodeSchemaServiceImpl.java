package com.xjrsoft.module.system.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.yulichang.base.MPJBaseServiceImpl;
import com.github.yulichang.toolkit.MPJWrappers;
import com.xjrsoft.common.page.ConventPage;
import com.xjrsoft.common.page.PageOutput;
import com.xjrsoft.common.utils.VoToColumnUtil;
import com.xjrsoft.module.system.constant.DictionaryConstant;
import com.xjrsoft.module.system.constant.StampConstant;
import com.xjrsoft.module.system.dto.CodeSchemaPageDto;
import com.xjrsoft.module.system.entity.CodeSchema;
import com.xjrsoft.module.system.entity.DictionaryDetail;
import com.xjrsoft.module.system.entity.DictionaryItem;
import com.xjrsoft.module.system.mapper.CodeSchemaMapper;
import com.xjrsoft.module.system.service.ICodeSchemaService;
import com.xjrsoft.module.system.vo.CodeSchemaPageVo;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 代码模板 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-29
 */
@Service
public class CodeSchemaServiceImpl extends MPJBaseServiceImpl<CodeSchemaMapper, CodeSchema> implements ICodeSchemaService {

    @Override
    public PageOutput<CodeSchemaPageVo> page(CodeSchemaPageDto dto) {
        IPage<CodeSchemaPageVo> page = this.selectJoinListPage(ConventPage.getPage(dto), CodeSchemaPageVo.class,
                MPJWrappers.<CodeSchema>lambdaJoin()
                        .select(CodeSchema::getId)
                        .select(CodeSchema.class, x -> VoToColumnUtil.fieldsToColumns(CodeSchemaPageVo.class).contains(x.getProperty()))
                        .selectAs(DictionaryDetail::getName, CodeSchemaPageVo::getCategoryName)
                        .leftJoin(DictionaryDetail.class, DictionaryDetail::getId, CodeSchema::getCategory)
                        .eq(StrUtil.isNotBlank(dto.getCategory()), CodeSchema::getCategory, dto.getCategory())
                        .eq(dto.getType() != null, CodeSchema::getType, dto.getType())
                        .like(StrUtil.isNotBlank(dto.getKeyword()), CodeSchema::getName, dto.getKeyword())
                        .orderByDesc(CodeSchema::getCreateDate));
        return ConventPage.getPageOutput(page);
    }
}
