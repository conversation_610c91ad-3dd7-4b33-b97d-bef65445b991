package com.xjrsoft.module.system.service.impl;

import com.github.yulichang.base.MPJBaseServiceImpl;
import com.xjrsoft.module.system.entity.File;
import com.xjrsoft.module.system.mapper.FileMapper;
import com.xjrsoft.module.system.service.IFileService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 文件关联关系表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-08
 */
@Service
public class FileServiceImpl extends MPJBaseServiceImpl<FileMapper, File> implements IFileService {

    @Override
    public boolean deleteFile(String encode) {
        return false;
    }
}
