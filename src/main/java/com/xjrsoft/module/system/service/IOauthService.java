package com.xjrsoft.module.system.service;

import me.zhyd.oauth.model.AuthCallback;
import me.zhyd.oauth.request.AuthRequest;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * @Author: tzx
 * @Date: 2023/8/8 10:39
 */
public interface IOauthService {
    void oAuthLogin(String source, AuthCallback callback, HttpServletRequest request, HttpServletResponse response);

    AuthRequest getAuthRequest(String source);
}
