package com.xjrsoft.module.system.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.xjrsoft.module.system.entity.CodeRuleSeed;
import com.xjrsoft.module.system.mapper.CodeRuleSeedMapper;
import com.xjrsoft.module.system.service.ICodeRuleSeedService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 编号规则种子表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-23
 */
@Service
public class CodeRuleSeedServiceImpl extends ServiceImpl<CodeRuleSeedMapper, CodeRuleSeed> implements ICodeRuleSeedService {

    @Override
    public CodeRuleSeed getCodeRuleSeedBy(Long ruleId, String userId) {
        return this.getOne(Wrappers.<CodeRuleSeed>query().lambda()
                .eq(CodeRuleSeed::getRuleId, ruleId)
                .eq(CodeRuleSeed::getUserId, userId), false);
    }
}
