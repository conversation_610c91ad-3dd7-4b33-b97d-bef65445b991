package com.xjrsoft.module.system.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.xjrsoft.common.exception.MyException;
import com.xjrsoft.module.system.entity.File;
import com.xjrsoft.module.system.entity.LogoConfig;
import com.xjrsoft.module.system.mapper.LogoConfigMapper;
import com.xjrsoft.module.system.service.IFileService;
import com.xjrsoft.module.system.service.ILogoConfigService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xjrsoft.module.system.vo.LogoInfoVo;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * Logo信息配置表【xjr_logo_config】 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-20
 */
@Service
@AllArgsConstructor
public class LogoConfigServiceImpl extends ServiceImpl<LogoConfigMapper, LogoConfig> implements ILogoConfigService {
    private final IFileService fileService;

    @Override
    public LogoInfoVo logoInfo() {
        List<LogoConfig> list = this.list();
        List<LogoInfoVo> logoInfoVos = new ArrayList<>();
        if (list.size() == 0){//如果没有，则设置为默认配置,并进行保存
            LogoConfig logoConfig = new LogoConfig();
            logoConfig.setId(1L);
            logoConfig.setCompanyName("海政互联信息技术（苏州）有限公司");
            logoConfig.setShortName("海政");
            this.save(logoConfig);

            LogoInfoVo logoInfoVo = BeanUtil.toBean(logoConfig, LogoInfoVo.class);
            logoInfoVos.add(logoInfoVo);
        }else {
            LogoConfig logoConfig = list.get(0);
            LogoInfoVo logoInfoVo = BeanUtil.toBean(logoConfig, LogoInfoVo.class);
            if (ObjectUtil.isNotEmpty(logoConfig.getRefreshLogoId())){
                File file = getFileByFolderId(logoConfig.getRefreshLogoId());
                logoInfoVo.setRefreshLogoUrl(file.getFileUrl());
            }
            if (ObjectUtil.isNotEmpty(logoConfig.getLoginLogoId())){
                File file = getFileByFolderId(logoConfig.getLoginLogoId());
                logoInfoVo.setLoginLogoUrl(file.getFileUrl());
            }
            if (ObjectUtil.isNotEmpty(logoConfig.getBackgroundLogoId())){
                File file = getFileByFolderId(logoConfig.getBackgroundLogoId());
                logoInfoVo.setBackgroundLogoUrl(file.getFileUrl());
            }
            if (ObjectUtil.isNotEmpty(logoConfig.getMenuLogoId())){
                File file = getFileByFolderId(logoConfig.getMenuLogoId());
                logoInfoVo.setMenuLogoUrl(file.getFileUrl());
            }
            if (ObjectUtil.isNotEmpty(logoConfig.getDesignerLogoId())){
                File file = getFileByFolderId(logoConfig.getDesignerLogoId());
                logoInfoVo.setDesignerLogoUrl(file.getFileUrl());
            }
            logoInfoVos.add(logoInfoVo);
        }
        return logoInfoVos.get(0);
    }

    private File getFileByFolderId(String folderId){
        List<File> list = fileService.list(Wrappers.lambdaQuery(File.class).eq(File::getFolderId, folderId).orderByDesc(File::getCreateDate));
        if (list.size() < 1){
            throw new MyException("登录配置的图片不存在");
        }
        return list.get(0);
    }
}
