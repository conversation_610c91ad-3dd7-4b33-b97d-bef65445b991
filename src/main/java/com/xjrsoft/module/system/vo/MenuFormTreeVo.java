package com.xjrsoft.module.system.vo;

import com.xjrsoft.common.model.tree.ITreeNode;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class MenuFormTreeVo implements ITreeNode<MenuFormTreeVo,String>, Serializable {
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("菜单主键")
    private String menuId;

    @ApiModelProperty("父级字段")
    private String parentId;

    @ApiModelProperty("编码")
    private String code;

    @ApiModelProperty("名称")
    private String name;

    @ApiModelProperty("排序码")
    private Integer sortCode;

    @ApiModelProperty("是否必填，0-非必填，1-必填")
    private Integer isRequired;

    private List<MenuFormTreeVo> children;
}
