package com.xjrsoft.module.system.vo;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class SubsystemListVo {

    @ApiModelProperty("主键")
    private Long id;

    @ApiModelProperty("系统名")
    private String name;

    @ApiModelProperty("编码")
    private String code;

    @ApiModelProperty("图标")
    private String icon;

    @ApiModelProperty("排序码")
    private Integer sortCode;


    @ApiModelProperty("备注")
    private String description;


//    @ApiModelProperty("主页地址")
//    private String indexUrl;
//
//    @ApiModelProperty("翻译标记")
//    private String lgmarkcode;

}
