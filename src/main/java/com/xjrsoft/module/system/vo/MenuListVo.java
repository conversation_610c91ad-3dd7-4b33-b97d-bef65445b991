package com.xjrsoft.module.system.vo;

import com.xjrsoft.common.model.tree.ITreeNode;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 获取菜单列表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-20
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class MenuListVo implements Serializable, ITreeNode<MenuListVo,Long> {
    private static final long serialVersionUID = 1L;


    /**
     * 主键
     */
    private Long id;
    /**
     * 上级Id
     */
    private Long parentId;

    /**
     * 组件名（路由名称） --  与vue代码组件名必须一直 才能做到缓存页面 相关联
     */
    private String name;

    /**
     * 菜单名
     */
    private String title;

    /**
     * 菜单编号
     */
    private String code;

    /**
     * 菜单图标
     */
    private String icon;

    /**
     * 地址
     */
    private String path;

    /**
     * 组件地址
     */
    private String component;

    /**
     * 组件类型 默认组件 0 普通需要注册的组件 1 自定义表单 桌面设计 等已经默认注册进来的组件
     */
    private Integer componentType;

    /**
     * 外链地址
     */
    private String iframeSrc;

    /**
     * 组件类型
     */
    private Integer menuType;

    /**
     * 菜单显示或者隐藏
     */
    private Integer display;

    /**
     * 是否允许修改
     */
    private Integer allowModify;

    /**
     * 是否允许删除
     */
    private Integer allowDelete;

    /**
     * 是否外链
     */
    private Integer outLink;

    /**
     * 页面持久化
     */
    private Integer keepAlive;

    /**
     * 排序码
     */
    private Integer sortCode;

    /**
     * 排序码
     */
    private String remark;


    private Integer enabledMark;

    private String clientType;

    private List<MenuListVo> children;
}
