package com.xjrsoft.module.system.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: tzx
 * @Date: 2023/3/9 15:29
 */
@Data
public class FileListVo {
    @ApiModelProperty("文件主键")
    private Long id;

    @ApiModelProperty("附件夹主键")
    private Long folderId;

    @ApiModelProperty("文件名称")
    private String fileName;

    @ApiModelProperty("文件路径")
    private String fileUrl;

    @ApiModelProperty("文件大小")
    private Long fileSize;

    @ApiModelProperty("文件后缀")
    private String fileSuffiex;

    @ApiModelProperty("文件类型")
    private String fileType;

    @ApiModelProperty("缩略图名称")
    private String thName;

    @ApiModelProperty("缩略图路径")
    private String thUrl;

    @ApiModelProperty("缩略图文件大小")
    private Long thSize;

    @ApiModelProperty("缩略图文件类型")
    private String thType;

    @ApiModelProperty("下载次数")
    private Integer downloadCount;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("上传人员")
    private String createUserName;

    @ApiModelProperty("上传人员")
    private String createDate;

    @ApiModelProperty("业务id")
    private String bizId;

    @ApiModelProperty("盖章后的pdfUrl")
    private String gzFileUrl;

    @ApiModelProperty("已盖章")
    private String ygz;

    private String fileCategory;
    private String fileId;
    private String required;
    private String type;
    private String px;
    private String json;
    /**
     * 起始地点
     */
    @ApiModelProperty("STATUS")
    @TableField(value = "STATUS")
    private String status;

    /**
     * file url 临时保存
     */
    @ApiModelProperty("FILE_URL_TEMP")
    @TableField(value = "FILE_URL_TEMP")
    private String fileUrlTemp;

}
