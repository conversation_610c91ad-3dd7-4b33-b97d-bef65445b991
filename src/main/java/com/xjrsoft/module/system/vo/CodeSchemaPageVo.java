package com.xjrsoft.module.system.vo;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class CodeSchemaPageVo {

    @ApiModelProperty("主键")
    private Long id;

    @ApiModelProperty("名称")
    private String name;

    @ApiModelProperty("类型（0-数据优先模板,1-界面优先模板,2-简易模板）")
    private Integer type;


    @ApiModelProperty("当前模板所关联的表单id")
    private Long formId;

    @ApiModelProperty("分类（关联数据字典）")
    private Long category;

    @ApiModelProperty("分类名称")
    private String categoryName;

    @ApiModelProperty("描述")
    private String remark;

    @ApiModelProperty("创建时间")
    private LocalDateTime createDate;

    @ApiModelProperty("模板状态（0-草稿，1-正式）")
    private Integer status;

    @ApiModelProperty("功能描述")
    private String description;
}
