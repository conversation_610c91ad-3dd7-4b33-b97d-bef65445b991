package com.xjrsoft.module.system.vo;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @title: MenuButtonListVo
 * <AUTHOR>
 * @Date: 2022/4/4 19:11
 * @Version 1.0
 */
@Data
@JsonAutoDetect(getterVisibility = JsonAutoDetect.Visibility.ANY)
public class MenuButtonListVo implements Serializable {

    private static final long serialVersionUID = 1L;

    private String id;

    @ApiModelProperty("按钮名")
    private String name;

    @ApiModelProperty("菜单Id")
    private String menuId;

    @ApiModelProperty("图标")
    private String icon;

    @ApiModelProperty("编码")
    private String code;

    @ApiModelProperty("请求地址")
    private String url;

    @ApiModelProperty("请求方式")
    private Integer method;

    @ApiModelProperty("标题")
    private String title;

    @ApiModelProperty("列表id")
    private String listId;


    public String getTitle() {
        return name;
    }



}

