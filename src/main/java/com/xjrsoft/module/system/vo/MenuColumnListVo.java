package com.xjrsoft.module.system.vo;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@JsonAutoDetect(getterVisibility = JsonAutoDetect.Visibility.ANY)
public class MenuColumnListVo implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("菜单主键")
    private String menuId;

    @ApiModelProperty("编码")
    private String code;

    @ApiModelProperty("名称")
    private String name;

    @ApiModelProperty("排序码")
    private Integer sortCode;

    @ApiModelProperty("描述")
    private String description;

    @ApiModelProperty("标题")
    private String title;

    @ApiModelProperty("列表id")
    private String listId;

    public String getTitle() {
        return name;
    }


}
