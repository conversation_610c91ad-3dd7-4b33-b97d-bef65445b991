package com.xjrsoft.module.system.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 用户
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-02
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class UserHj implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("登录账号id")
    private String id;

    @ApiModelProperty("登录账号")
    private String userName ="";

    @ApiModelProperty("登录账号名称")
    private String name ="";

    @ApiModelProperty("单位")
    private String dw ="";
    @ApiModelProperty("单位代码")
    private String dwCode ="";
    @ApiModelProperty("单位")
    private String dwName ="";

    @ApiModelProperty("bm")
    private String bm ="";
    @ApiModelProperty("部门代码")
    private String bmCode="";
    @ApiModelProperty("bm")
    private String bmName="";

    @ApiModelProperty("业务日期")
    private Date ywrq = new Date();

    @ApiModelProperty("年度")
    private String nd ="";

    @ApiModelProperty("期间")
    private String qj ="";

    @ApiModelProperty("一级部门")
    private String yjbm ="";
    @ApiModelProperty("一级部门名称")
    private String yjbmName ="";
}
