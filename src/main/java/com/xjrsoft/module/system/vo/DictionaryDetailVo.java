package com.xjrsoft.module.system.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: tzx
 * @Date:2022/3/30 14:53
 */
@Data
public class DictionaryDetailVo {

    @ApiModelProperty("名字")
    private String name;

    @ApiModelProperty("编码")
    private String code;

    @ApiModelProperty("字典项id")
    private String itemId;

    @ApiModelProperty("值")
    private String value;

    @ApiModelProperty("排序号")
    private Integer sortCode;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("type code")
    private String dicTypeCode;

    @ApiModelProperty("type name")
    private String dicTypeName;

    private String url;
    private String hoverUrl;
}
