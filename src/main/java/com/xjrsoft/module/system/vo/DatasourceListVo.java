package com.xjrsoft.module.system.vo;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @title: DatasourceListVo
 * <AUTHOR>
 * @Date: 2022/4/4 18:45
 * @Version 1.0
 */
@Data
public class DatasourceListVo implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("编号")
    private String code;

    @ApiModelProperty("名字")
    private String name;

    @ApiModelProperty("数据库主键")
    private String databaselinkId;

    @ApiModelProperty("sql语句")
    private String sqlScript;

    @ApiModelProperty("备注")
    private String remark;

}
