package com.xjrsoft.module.system.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @Author: hnyyzy
 * @Date: 2023/11/23 14:32
 */
@Data
public class LogoInfoVo implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键")
    private Long id;

    @ApiModelProperty("公司名称")
    private String companyName;

    @ApiModelProperty("公司简称")
    private String shortName;

    @ApiModelProperty("刷新logoId")
    private String refreshLogoUrl;

    @ApiModelProperty("登录页logoId")
    private String loginLogoUrl;

    @ApiModelProperty("菜单logoId")
    private String menuLogoUrl;

    @ApiModelProperty("设计器logoId")
    private String designerLogoUrl;

    @ApiModelProperty("登录页背景logoId")
    private String backgroundLogoUrl;

    @ApiModelProperty("备注")
    private String remark;
    private String dsName;
    @ApiModelProperty("默认登录方式")
    private String defaultLoginType;

    private String menuLogoDeepId;


}
