package com.xjrsoft.module.system.vo;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Data
public class AuthMenuVo {

    private List<MenuListVo> menuList;

    private Map<String, List<MenuButtonVo>> buttonListMap;

    private Map<String, List<MenuColumnVo>> columnListMap;

    private Map<String, List<MenuFormVo>> formListMap;

    public void addButton(String menuId, MenuButtonVo menuButtonVo) {
        List<MenuButtonVo> menuButtonVoList = this.buttonListMap.computeIfAbsent(menuId, k -> new ArrayList<>());
        menuButtonVoList.add(menuButtonVo);
    }

    public void addColumn(String menuId, MenuColumnVo menuColumnVo) {
        List<MenuColumnVo> menuColumnVoList = this.columnListMap.computeIfAbsent(menuId, k -> new ArrayList<>());
        menuColumnVoList.add(menuColumnVo);
    }

    public void addForm(String menuId, MenuFormVo menuFormVo) {
        List<MenuFormVo> menuFormVoList = this.formListMap.computeIfAbsent(menuId, k -> new ArrayList<>());
        menuFormVoList.add(menuFormVo);
    }
}
