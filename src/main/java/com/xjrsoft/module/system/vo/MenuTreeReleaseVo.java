package com.xjrsoft.module.system.vo;


import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.xjrsoft.common.enums.YesOrNoEnum;
import com.xjrsoft.common.model.tree.ITreeNode;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 获取菜单树vo
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-20
 */
@Data
@JsonAutoDetect(getterVisibility = JsonAutoDetect.Visibility.ANY)
public class MenuTreeReleaseVo implements ITreeNode<MenuTreeReleaseVo, String>, Serializable {
    private static final long serialVersionUID = 1L;

    private String zyFormListMenuId;

    private String config;

    /**
     * id
     */
    private String id;

    /**
     * 上级Id
     */
    private String parentId;

    /**
     * 组件名（路由名称） --  与vue代码组件名必须一直 才能做到缓存页面 相关联
     */
    private String name;

    /**
     * 菜单名
     */
    private String title;

    /**
     * 菜单编号
     */
    private String code;

    /**
     * 菜单图标
     */
    private String icon;

    /**
     * 地址
     */
    private String path;

    /**
     * 组件地址
     */
    private String component;

    /**
     * 组件类型 默认组件 0 普通需要注册的组件 1 自定义表单 桌面设计 等已经默认注册进来的组件
     */
    private Integer componentType;

    /**
     * 外链地址
     */
    private String iframeSrc;

    /**
     * 组件类型
     */
    private Integer menuType;

    /**
     * 菜单显示或者隐藏
     */
    private Integer display;

    /**
     * 是否允许修改
     */
    private Integer allowModify;

    /**
     * 是否允许删除
     */
    private Integer allowDelete;

    /**
     * 是否外链
     */
    private Integer outLink;

    /**
     * 页面持久化
     */
    private Integer keepAlive;

    /**
     * 排序码
     */
    private Integer sortCode;

    /**
     * 排序码
     */
    private String remark;

    /**
     * 系统主键（主系统默认为0）
     */
    private Long systemId;

    /**
     * 系统名称
     */
    private String systemName;

    /**
     * 表单id
     */
    private Long formId;
    private String clientType;


    private Integer enabledMark;

    private List<MenuTreeReleaseVo> children;

    private Boolean disabled;


    public Boolean getDisabled() {
        return this.menuType != null && this.menuType == 1;
    }


    private JSONObject meta;

    public JSONObject getMeta() {
        if (meta == null) {
            meta = new JSONObject();
        }
        meta.put("affix", false);
        meta.put("icon", this.icon);
        meta.put("orderNo", this.sortCode);
        meta.put("title", this.title);
        meta.put("outLink", this.outLink);
        meta.put("systemId", this.systemId);
        meta.put("formId", this.formId);
        meta.put("menuId", this.id);
        meta.put("menuType", this.menuType);
        meta.put("hideMenu", this.display == YesOrNoEnum.NO.getCode());

        meta.put("remark", this.remark);

        if (StrUtil.isBlank(this.component)) {
            meta.put("ignoreRoute", true);
        }

        //如果是外部连接 将path 作为 meta 的frameSrc 传递出去
        if (this.outLink != null && this.outLink == 1) {
            meta.put("frameSrc", this.iframeSrc);
        }
        return meta;
    }
    @Override
    public String getParentId() {
        if (this.parentId == null || "0".equals(this.parentId)) {
            return null;
        }
        return parentId;
    }
}
