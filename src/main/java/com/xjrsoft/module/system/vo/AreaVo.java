package com.xjrsoft.module.system.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @Author: tzx
 * @Date: 2022/6/16 17:00
 */
@Data
public class AreaVo  implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("父级主键")
    private String parentId;

    @ApiModelProperty("区域编码")
    private String code;

    @ApiModelProperty("区域名称")
    private String name;

    @ApiModelProperty("快速查询")
    private String quickQuery;

    @ApiModelProperty("简拼")
    private String simpleSpelling;

    @ApiModelProperty("层次")
    private Integer layer;

    @ApiModelProperty("排序码")
    private Integer sortCode;

    @ApiModelProperty("备注")
    private String remark;
}
