package com.xjrsoft.module.system.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
public class DatabaseLinkListVo implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("数据库连接主键")
    private String id;

    @ApiModelProperty("连接地址")
    private String host;

    @ApiModelProperty("账号")
    private String username;

    @ApiModelProperty("密码")
    private String password;

    @ApiModelProperty("驱动")
    private String driver;

    @ApiModelProperty("数据库名称")
    private String dbName;

    @ApiModelProperty("数据库别名")
    private String dbAlisa;

    @ApiModelProperty("数据库类型")
    private String dbType;

    @ApiModelProperty("数据库版本")
    private String dbVersion;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("排序码")
    private Integer sortCode;

    @ApiModelProperty("创建时间")
    private LocalDateTime createDate;
}
