package com.xjrsoft.module.system.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class CodeSchemaVo {

    @ApiModelProperty("主键")
    private Long id;

    @ApiModelProperty("名称")
    private String name;

    @ApiModelProperty("类型（0-数据优先模板,1-界面优先模板,2-简易模板）")
    private Integer type;


    @ApiModelProperty("当前模板所关联的表单id")
    private Long formId;

    @ApiModelProperty("分类（关联数据字典）")
    private Long category;

    @ApiModelProperty("内容")
    private String content;

    @ApiModelProperty("描述")
    private String remark;
}
