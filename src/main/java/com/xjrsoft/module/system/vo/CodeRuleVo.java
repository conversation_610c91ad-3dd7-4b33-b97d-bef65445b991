package com.xjrsoft.module.system.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

@Data

public class CodeRuleVo {

    private static final long serialVersionUID = 1L;

    /**
     * 编码规则主键
     */
    @ApiModelProperty(name = "编码规则主键",required = true)
    private String id;

    /**
     * 编号
     */
    @ApiModelProperty("编号")
    private String code;

    /**
     * 名称
     */
    @ApiModelProperty("名称")
    private String name;

    /**
     * 当前流水号
     */
    @ApiModelProperty("当前流水号")
    private String currentNumber;

    /**
     * 规则格式Json
     */
    @ApiModelProperty("规则格式Json")
    private String formatJson;

    /**
     * 排序码
     */
    @ApiModelProperty("排序码")
    private Integer sortCode;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String description;

    /**
     * 创建日期
     */
    @ApiModelProperty("创建日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createDate;

    /**
     * 创建用户
     */
    @ApiModelProperty("创建用户")
    private String createUserName;
}
