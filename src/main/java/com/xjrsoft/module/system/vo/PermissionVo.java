package com.xjrsoft.module.system.vo;

import com.xjrsoft.module.desktop.vo.DesktopSchemaInfoVo;
import lombok.Data;

import java.util.List;

/**
 * @Author: tzx
 * @Date: 2023/4/20 18:17
 */
@Data
public class PermissionVo {

    /**
     * 当前登录岗位id
     */
    private String postId;
    /**
     * 当前登录岗位名
     */
    private String postName;

    /**
     * 当前登录组织id
     */
    private String departmentId;

    /**
     * 当前登录组织名
     */
    private String departmentName;

    /**
     * 所有菜单 权限数据
     */
    private List<MenuAuthVo> menuAuthList;

    private DesktopSchemaInfoVo desktopSchema;

    private List<DesktopSchemaInfoVo> desktopSchemaInfoVoList;

}
