package com.xjrsoft.module.system.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.img.ImgUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.URLUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.yulichang.toolkit.MPJWrappers;
import com.xjrsoft.common.constant.GlobalConstant;
import com.xjrsoft.common.model.result.R;
import com.xjrsoft.common.page.ConventPage;
import com.xjrsoft.common.page.PageOutput;
import com.xjrsoft.common.utils.UploadUtil;
import com.xjrsoft.common.utils.VoToColumnUtil;
import com.xjrsoft.config.OSSConfig;
import com.xjrsoft.module.organization.entity.User;
import com.xjrsoft.module.oss.factory.OssFactory;
import com.xjrsoft.module.system.dto.FileListDto;
import com.xjrsoft.module.system.dto.FilePageDto;
import com.xjrsoft.module.system.entity.File;
import com.xjrsoft.module.system.service.IFileService;
import com.xjrsoft.module.system.vo.FileListVo;
import com.xjrsoft.module.system.vo.FileVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.security.PermitAll;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URLEncoder;
import java.nio.ByteBuffer;
import java.nio.channels.Channels;
import java.nio.channels.ReadableByteChannel;
import java.nio.charset.StandardCharsets;
import java.util.*;


/**
 * <p>
 * 文件关联关系表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-08
 */
@RestController
@RequestMapping(GlobalConstant.SYSTEM_MODULE_PREFIX + "/file")
@Api(value = GlobalConstant.SYSTEM_MODULE_PREFIX + "/file", tags = "文件")
@AllArgsConstructor
public class FileController {
    private static final Map<String, MediaType> mediaTypeMap = new HashMap<>();

    static {
        mediaTypeMap.put("txt", MediaType.TEXT_PLAIN);
        mediaTypeMap.put("html", MediaType.TEXT_HTML);
        mediaTypeMap.put("css", MediaType.TEXT_PLAIN);
        mediaTypeMap.put("xml", MediaType.APPLICATION_XML);
        mediaTypeMap.put("json", MediaType.APPLICATION_JSON);
        mediaTypeMap.put("pdf", MediaType.APPLICATION_PDF);
        mediaTypeMap.put("PDF", MediaType.APPLICATION_PDF);
        mediaTypeMap.put("jpg", MediaType.IMAGE_JPEG);
        mediaTypeMap.put("svg", MediaType.IMAGE_JPEG);
        mediaTypeMap.put("jpeg", MediaType.IMAGE_JPEG);
        mediaTypeMap.put("png", MediaType.IMAGE_PNG);
        mediaTypeMap.put("gif", MediaType.IMAGE_GIF);
    }

    private IFileService fileService;
    private OSSConfig ossConfig;

    @GetMapping("/info")
    @ApiOperation(value = "根据文件Id获取单个文件数据")
    public R info(@RequestParam String id) {
        return R.ok(fileService.getById(id));
    }

    @GetMapping
    @ApiOperation(value = "查询文件列表（不分页）")
    public R list(@Valid FileListDto dto) {
        String[] strings = dto.getFolderId().split(",");
        List<FileListVo> allList = new ArrayList<>();
        for (String string : strings) {
            List<FileListVo> list = fileService.selectJoinList(FileListVo.class, MPJWrappers.<File>lambdaJoin()
                    .like(StrUtil.isNotBlank(dto.getFileName()), File::getFileName, dto.getFileName())
                    .eq(ObjectUtil.isNotNull(dto.getFolderId()), File::getFolderId, string)
                    .eq(StrUtil.isNotBlank(dto.getProcessId()), File::getProcessId, dto.getProcessId())
                    .select(File::getId)
                    .select(File.class, x -> VoToColumnUtil.fieldsToColumns(FileListVo.class).contains(x.getProperty()))
                    .selectAs(User::getName, FileListVo::getCreateUserName)
                    .leftJoin(User.class, User::getId, File::getCreateUserId)
                    .last(StrUtil.isNotBlank(dto.getField()), GlobalConstant.ORDER_BY + StringPool.SPACE + StrUtil.toUnderlineCase(dto.getField()) + StringPool.SPACE + ConventPage.getOrder(dto.getOrder())));
            allList.addAll(list);
        }
        return R.ok(allList);
    }

    @GetMapping("/page")
    @ApiOperation(value = "查询文件列表(分页)")
    public R page(@Valid FilePageDto dto) {
        LambdaQueryWrapper<File> wrapper = Wrappers.<File>query().lambda()
                .like(StrUtil.isNotBlank(dto.getFileName()), File::getFileName, dto.getFileName())
                .eq(ObjectUtil.isNotNull(dto.getFolderId()), File::getFolderId, dto.getFolderId())
                .eq(StrUtil.isNotBlank(dto.getProcessId()), File::getProcessId, dto.getProcessId())
                .orderByDesc(File::getPx);

        IPage<File> page = fileService.page(ConventPage.getPage(dto), wrapper);
        PageOutput<File> pageOutput = ConventPage.getPageOutput(page, File.class);
        return R.ok(pageOutput);
    }

    @DeleteMapping
    @ApiOperation(value = "删除文件(可批量)")
    public R delete(@RequestBody List<Long> ids) {

        return R.ok(fileService.removeBatchByIds(ids));
    }

    @DeleteMapping("/delete-single")
    @ApiOperation(value = "删除组件 删除单个文件")
    public R delete(@RequestBody String id) {

        File deleteFile = fileService.getById(id);
        if (deleteFile == null) {
            return R.error("文件不存在");
        }
        fileService.removeById(id);
        return R.ok(deleteFile.getFileId());

    }

    @PostMapping
    @ApiOperation(value = "单文件上传")
    public R uploadFile(@RequestParam(value = "file", required = true) MultipartFile multipartFile, @RequestParam(required = false) String px, @RequestParam(required = false) String json) throws Exception {
        String folderId = IdWorker.getId() + "";
        String fileId = IdWorker.getId() + "";
        File file = uploadFile(multipartFile, folderId, fileId, px, json);
        return R.ok(file.getFileUrl());
    }

    @PostMapping(value = "/updateFile")
    @ApiOperation(value = "更新文件信息")
    public R updateFile(@RequestBody File file) throws Exception {
        File fileServiceById = fileService.getById(file.getId());
        fileServiceById.setPx(file.getPx());
        if (StringUtils.isNotBlank(file.getJson())) {
            fileServiceById.setJson(file.getJson());
        }
        fileService.updateById(fileServiceById);
        return R.ok();
    }


    @PostMapping("/folder")
    @ApiOperation(value = "单文件上传")
    public R uploadFileToFolder(@RequestParam String folderId, @RequestParam(required = false) String px, @RequestParam(required = false) String json, @RequestParam MultipartFile multipartFile) throws Exception {
        String fileId = IdWorker.getId() + "";
        File file = uploadFile(multipartFile, folderId, fileId, px, json);
        return R.ok(file.getFileUrl());
    }


    @PostMapping("/multiple-file-upload")
    @ApiOperation(value = "多文件上传")
    public R uploadFiles(@RequestParam(value = "file") MultipartFile[] multipartFiles) throws Exception {
        List<String> urlList = new ArrayList<>();
        if (multipartFiles != null && multipartFiles.length > 0) {
            String folderId = IdWorker.getId() + "";
            for (MultipartFile multipartFile : multipartFiles) {
                String fileId = IdWorker.getId() + "";
                File file = uploadFile(multipartFile, folderId, fileId, null, null);
                urlList.add(file.getFileUrl());
            }

        }
        return R.ok(urlList);
    }

    @PostMapping("/multiple-file-upload/folder")
    @ApiOperation(value = "多文件上传")
    @Transactional
    public R uploadFilesToFolder(@RequestParam(required = false) String folderId, @RequestParam(required = false) String px, @RequestParam(value = "file") MultipartFile[] multipartFiles, @RequestParam(required = false) String json) throws Exception {
        List<FileVo> fileVoList = new ArrayList<>();
        if (multipartFiles != null && multipartFiles.length > 0) {
            for (MultipartFile multipartFile : multipartFiles) {
                String fileId = IdWorker.getId() + "";
                if (folderId == null) {
                    folderId = IdWorker.getId() + "";
                }
                File file = uploadFile(multipartFile, folderId, fileId, px, json);
                fileVoList.add(BeanUtil.toBean(file, FileVo.class));
            }
        }
        return R.ok(fileVoList);
    }

    private File uploadFile(MultipartFile file, String folderId, String fileId, String px, String json) throws Exception {
        String filename = file.getOriginalFilename();
        String suffix = StringUtils.substringAfterLast(filename, StringPool.DOT);
        //保存到云服务器
        String filePath = UploadUtil.uploadFile(file);

        File fileEntity = new File();
        fileEntity.setId(fileId + "");
        fileEntity.setFolderId(folderId + "");
        fileEntity.setFileName(filename);
        fileEntity.setFileUrl(filePath);
        fileEntity.setFileSize(file.getSize());
        fileEntity.setFileSuffiex(StringPool.DOT + suffix);
        fileEntity.setFileType(suffix);
        fileEntity.setPx(px);
        fileEntity.setJson(json);

        if (GlobalConstant.imageType.contains(StringUtils.lowerCase(suffix.replace(StringPool.DOT, StringPool.EMPTY)))) {

            String thSuffix = StringPool.DOT + ImgUtil.IMAGE_TYPE_JPEG;

            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            ImgUtil.scale(file.getInputStream(), outputStream, 200, 200, null);

            byte[] thBytes = outputStream.toByteArray();

            String thUrl = Objects.requireNonNull(OssFactory.build()).uploadSuffix(thBytes, StringPool.DOT + ImgUtil.IMAGE_TYPE_JPEG);
            outputStream.close();

            fileEntity.setThUrl(thUrl);
            fileEntity.setThType(thSuffix);
            fileEntity.setThName(file.getOriginalFilename().replace(suffix, StringPool.EMPTY) + "-缩略图");
            fileEntity.setThSize(Convert.toLong(thBytes.length));
        }
        fileService.save(fileEntity);
        return fileEntity;
    }

    @GetMapping("/download/{fileId}")
    @PermitAll
    public void download(HttpServletRequest request, HttpServletResponse response, @PathVariable("fileId") String fileId) throws Exception {
        File fileListById = fileService.getById(fileId);
        // 获取请求的路径
        String path = fileListById.getFileUrl();
        if (StrUtil.isEmpty(path)) {
            throw new IllegalArgumentException("结尾的 path 路径必须传递");
        }
        // 解码，解决中文路径的问题 https://gitee.com/zhijiantianya/ruoyi-vue-pro/pulls/807/
        path = URLUtil.decode(path);
        // 读取内容
        byte[] bytesFromInputStream = getBytesFromInputStream(UploadUtil.download(path));
        writeAttachment2(response, fileListById.getFileName() + fileListById.getFileType(), bytesFromInputStream);
    }

    @GetMapping("/preview/{fileId}")
    public ResponseEntity<byte[]> preview(HttpServletRequest request, HttpServletResponse response, @PathVariable("fileId") String fileId) throws Exception {
        File fileListById = fileService.getById(fileId);
        // 获取请求的路径
        String path = fileListById.getFileUrl();
        if (StrUtil.isEmpty(path)) {
            throw new IllegalArgumentException("结尾的 path 路径必须传递");
        }
        path = URLUtil.decode(path);
        // 读取内容
        byte[] bytesFromInputStream = getBytesFromInputStream(UploadUtil.download(path));
        MediaType mediaType = mediaTypeMap.getOrDefault(fileListById.getFileSuffiex(), MediaType.APPLICATION_OCTET_STREAM);
        return R.fileStream(bytesFromInputStream, fileListById.getFileName() + fileListById.getFileType(), mediaType);
    }

    public static void writeAttachment2(HttpServletResponse response, String filename, byte[] content) throws IOException {
        // 设置 header 和 contentType
        String encodedFilename = URLEncoder.encode(filename, StandardCharsets.UTF_8).replaceAll("\\+", "%20");
        response.setHeader("Content-Disposition", "attachment; filename=\"" + encodedFilename + "\"; filename*=UTF-8''" + encodedFilename);
        response.setContentType("'application/octet-stream'");
        // 输出附件
        IoUtil.write(response.getOutputStream(), false, content);
    }

    public static byte[] getBytesFromInputStream(InputStream inputStream) throws IOException {
        try (ReadableByteChannel readableByteChannel = Channels.newChannel(inputStream)) {
            ByteBuffer byteBuffer = ByteBuffer.allocate(1024);
            ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();

            while (readableByteChannel.read(byteBuffer) != -1) {
                byteBuffer.flip();
                byteArrayOutputStream.write(byteBuffer.array(), 0, byteBuffer.limit());
                byteBuffer.clear();
            }

            return byteArrayOutputStream.toByteArray();
        }
    }
}
