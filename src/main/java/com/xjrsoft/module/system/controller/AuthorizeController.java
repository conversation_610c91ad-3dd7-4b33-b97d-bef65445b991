package com.xjrsoft.module.system.controller;

import com.xjrsoft.common.constant.GlobalConstant;
import com.xjrsoft.common.model.result.R;
import com.xjrsoft.module.system.service.IAuthorizeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-16
 */
@RestController
@RequestMapping(GlobalConstant.SYSTEM_MODULE_PREFIX + "/authorize")
@Api(value = GlobalConstant.SYSTEM_MODULE_PREFIX + "/authorize",tags = "授权表")
@AllArgsConstructor
public class AuthorizeController {

    private final IAuthorizeService authorizeService;

    @GetMapping("/permissions")
    @ApiOperation(value="授权信息表")
    public R permissions(@RequestParam(value = "clientType",required = false) String clientType){
        return R.ok(authorizeService.getPermissions(clientType));
    }
}
