package com.xjrsoft.module.system.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.xjrsoft.common.constant.GlobalConstant;
import com.xjrsoft.common.model.result.R;
import com.xjrsoft.module.generator.dto.CodeFirstGeneratorDto;
import com.xjrsoft.module.generator.entity.OutputConfig;
import com.xjrsoft.module.system.dto.AddCodeSchemaDto;
import com.xjrsoft.module.system.dto.CodeSchemaPageDto;
import com.xjrsoft.module.system.dto.UpdateCodeSchemaDto;
import com.xjrsoft.module.system.entity.CodeSchema;
import com.xjrsoft.module.system.service.ICodeSchemaService;
import com.xjrsoft.module.system.vo.CodeSchemaVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.SneakyThrows;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <p>
 * 代码模板 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-29
 */
@RestController
@RequestMapping(GlobalConstant.SYSTEM_MODULE_PREFIX + "/code-schema")
@Api(value = "/code-schema", tags = "代码模板模块")
@AllArgsConstructor
public class CodeSchemaController {

    private ICodeSchemaService codeSchemeService;

    private ObjectMapper objectMapper;

    @ApiOperation(value="获取代码模板分页")
    @GetMapping("/page")
    public R page(CodeSchemaPageDto dto){
        return R.ok(codeSchemeService.page(dto));
    }

    @GetMapping("/info")
    @ApiOperation(value="根据id 查询代码模板详情")
    public R info(Long id){
        CodeSchema codeSchema = codeSchemeService.getById(id);
        return R.ok(BeanUtil.toBean(codeSchema, CodeSchemaVo.class));
    }

    @PostMapping
    @ApiOperation(value="新增代码模板")
    public R add(@RequestBody AddCodeSchemaDto codeSchemaDto) {
        CodeSchema codeSchema = BeanUtil.toBean(codeSchemaDto, CodeSchema.class);
        buildCodeSchema(codeSchema, codeSchemaDto.getContent());
        return R.ok(codeSchemeService.save(codeSchema));
    }

    @PutMapping
    @ApiOperation(value="修改代码模板")
    public R update(@RequestBody UpdateCodeSchemaDto codeSchemaDto) {
        CodeSchema codeSchema = BeanUtil.toBean(codeSchemaDto, CodeSchema.class);
        buildCodeSchema(codeSchema, codeSchemaDto.getContent());
        return R.ok(codeSchemeService.updateById(codeSchema));
    }
    @DeleteMapping
    @ApiOperation(value="删除代码模板")
    public R delete(@RequestBody List<Long> ids) {
        return R.ok(codeSchemeService.removeBatchByIds(ids));
    }

    @SneakyThrows
    private void buildCodeSchema(CodeSchema codeSchema, String content) {
        CodeFirstGeneratorDto dto = objectMapper.readValue(content, CodeFirstGeneratorDto.class);
        OutputConfig outputConfig = dto.getOutputConfig();
        codeSchema.setCategory(Long.parseLong(outputConfig.getOutputArea()));
        codeSchema.setDescription(outputConfig.getComment());
    }
}
