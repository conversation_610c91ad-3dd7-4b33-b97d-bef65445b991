package com.xjrsoft.module.system.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.pinyin.PinyinUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.xjrsoft.common.constant.GlobalConstant;
import com.xjrsoft.common.model.result.R;
import com.xjrsoft.module.organization.entity.User;
import com.xjrsoft.module.organization.service.IUserService;
import com.xjrsoft.module.system.dto.AddAreaDto;
import com.xjrsoft.module.system.dto.UpdateAreaDto;
import com.xjrsoft.module.system.dto.UpdateDictionaryItemDto;
import com.xjrsoft.module.system.entity.Area;
import com.xjrsoft.module.system.entity.DictionaryItem;
import com.xjrsoft.module.system.service.IAreaService;
import com.xjrsoft.module.system.vo.AreaListVo;
import com.xjrsoft.module.system.vo.AreaVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;
import org.springframework.stereotype.Controller;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * <p>
 * 行政区域表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-16
 */
@RestController
@RequestMapping(GlobalConstant.SYSTEM_MODULE_PREFIX + "/area")
@Api(value = GlobalConstant.SYSTEM_MODULE_PREFIX + "/area", tags = "行政区域")
@AllArgsConstructor
public class AreaController {

    private final IAreaService areaService;

    private final IUserService userService;

    @GetMapping("/province")
    @ApiOperation(value = "获取所有省份")
    public R getAllProvinceArea(@RequestParam(required = false) String keyword) {
        List<Area> provinceList = areaService.list(Wrappers.<Area>query().lambda().eq(Area::getParentId, GlobalConstant.FIRST_NODE_VALUE).like(StrUtil.isNotBlank(keyword), Area::getName, keyword));
        List<AreaListVo> resultList = BeanUtil.copyToList(provinceList, AreaListVo.class);
        return R.ok(resultList);
    }


    @GetMapping("/child")
    @ApiOperation(value = "根据id 查询下级区域")
    public R getAreaByParentId(@RequestParam(required = false) Long id, @RequestParam(required = false) String keyword) {
        List<Area> provinceList = areaService.list(Wrappers.<Area>query().lambda()
                .eq(id != null, Area::getParentId, id)
                .eq(id == null, Area::getParentId, GlobalConstant.FIRST_NODE_VALUE)
                .and(StrUtil.isNotBlank(keyword), x -> {
                    x.like(StrUtil.isNotBlank(keyword), Area::getName, keyword).or();
                    x.like(StrUtil.isNotBlank(keyword), Area::getSimpleSpelling, keyword).or();
                    x.like(StrUtil.isNotBlank(keyword), Area::getQuickQuery, keyword);
                }));

        List<AreaListVo> resultList = BeanUtil.copyToList(provinceList, AreaListVo.class);
        return R.ok(resultList);
    }


    @GetMapping("/info")
    @ApiOperation(value = "根据id 获取详情信息")
    public R info(@RequestParam Long id) {
        Area area = areaService.getById(id);
        if (area == null) {
            R.error("找不到此区域!");
        }
        return R.ok(BeanUtil.toBean(area, AreaVo.class));
    }

    @GetMapping("/info/multi")
    @ApiOperation(value = "根据id 获取详情信息")
    public R infoMulti(@RequestParam String ids) {
        List<String> idList = StrUtil.split(ids, StringPool.COMMA);
        List<Area> areaList = areaService.listByIds(idList);
        return R.ok(BeanUtil.copyToList(areaList, AreaVo.class));
    }

    @PostMapping
    @ApiOperation(value = "新增")
    public R add(@Valid @RequestBody AddAreaDto dto) {
        long count = areaService.count(Wrappers.<Area>query().lambda().eq(Area::getCode, dto.getCode()));
        if (count > 0) {
            return R.error("区域编码已经存在！");
        }

        Area area = BeanUtil.toBean(dto, Area.class);
        area.setQuickQuery(PinyinUtil.getPinyin(area.getName(), StringPool.EMPTY));
        return R.ok(areaService.save(area));
    }

    @PutMapping
    @ApiOperation(value = "修改")
    public R update(@Valid @RequestBody UpdateAreaDto dto) {
        long count = areaService.count(Wrappers.<Area>query().lambda()
                .eq(Area::getCode, dto.getCode())
                .ne(Area::getId, dto.getId()));
        if (count > 0) {
            return R.error("区域编码已经存在！");
        }
        Area area = BeanUtil.toBean(dto, Area.class);
        area.setQuickQuery(PinyinUtil.getPinyin(area.getName(), StringPool.EMPTY));

        return R.ok(areaService.updateById(area));
    }

    @DeleteMapping
    @ApiOperation(value = "删除")
    public R delete(@Valid @RequestBody List<Long> ids) {
        return R.ok(areaService.removeByIds(ids));
    }

}
