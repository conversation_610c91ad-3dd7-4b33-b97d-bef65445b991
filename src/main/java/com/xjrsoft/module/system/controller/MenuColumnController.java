package com.xjrsoft.module.system.controller;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.xjrsoft.common.constant.GlobalConstant;
import com.xjrsoft.common.model.result.R;
import com.xjrsoft.common.utils.VoToColumnUtil;
import com.xjrsoft.module.system.entity.MenuColumn;
import com.xjrsoft.module.system.service.IMenuColumnService;
import com.xjrsoft.module.system.vo.MenuColumnListVo;
import com.xjrsoft.module.system.vo.MenuColumnVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 菜单列表字段 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-08
 */
@RestController
@RequestMapping(GlobalConstant.SYSTEM_MODULE_PREFIX + "/menu-colum")
@Api(value = GlobalConstant.SYSTEM_MODULE_PREFIX + "/menu-column",tags = "菜单字段")
@AllArgsConstructor
public class MenuColumnController {

    private  final IMenuColumnService menuColumnService;

    @GetMapping(value = "/list")
    @ApiOperation(value="根据菜单id查询字段列表")
    public R getMenuColumnListByMenuId(@RequestParam(required = false) String menuId){
        if (menuId == null) {
            return R.ok(new ArrayList<>(0));
        }
        List<MenuColumn> list = menuColumnService.list(Wrappers.<MenuColumn>lambdaQuery().eq(!"0".equals(menuId), MenuColumn::getMenuId, menuId)
                .select(MenuColumn.class, x -> VoToColumnUtil.fieldsToColumns(MenuColumnVo.class).contains(x.getProperty())));
        List<MenuColumnListVo> menuColumnListVos = BeanUtil.copyToList(list, MenuColumnListVo.class);
        return R.ok(menuColumnListVos);
    }

}
