package com.xjrsoft.module.system.controller;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.xjrsoft.common.constant.GlobalConstant;
import com.xjrsoft.common.model.result.R;
import com.xjrsoft.common.utils.VoToColumnUtil;
import com.xjrsoft.module.system.dto.AddMenuButtonDto;
import com.xjrsoft.module.system.dto.UpdateMenuButtonDto;
import com.xjrsoft.module.system.entity.MenuButton;
import com.xjrsoft.module.system.service.IMenuButtonService;
import com.xjrsoft.module.system.vo.MenuButtonListVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 菜单按钮 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-16
 */
@RestController
@RequestMapping(GlobalConstant.SYSTEM_MODULE_PREFIX + "/menu-button")
@Api(value = GlobalConstant.SYSTEM_MODULE_PREFIX + "/menu-button",tags = "菜单按钮")
@AllArgsConstructor
public class MenuButtonController {

    private final IMenuButtonService menuButtonService;

    @GetMapping(value = "/list")
    @ApiOperation(value="根据菜单id查询按钮列表")
    public R getMenuButtonListByMenuId(@RequestParam(required = false) String menuId){
        if (menuId == null) {
            return R.ok(new ArrayList<>(0));
        }
        List<MenuButton> list = menuButtonService.list(Wrappers.<MenuButton>lambdaQuery().eq(!"0".equals(menuId), MenuButton::getMenuId, menuId)
                .select(MenuButton.class, x -> VoToColumnUtil.fieldsToColumns(MenuButtonListVo.class).contains(x.getProperty())));
        List<MenuButtonListVo> menuButtonListVos = BeanUtil.copyToList(list, MenuButtonListVo.class);
        return R.ok(menuButtonListVos);
    }

    @GetMapping(value = "/info")
    @ApiOperation(value="根据id查询按钮信息")
    public R getMenuButtonInfo(@RequestParam String id){
        return R.ok(menuButtonService.getById(id));
    }


    @PostMapping
    @ApiOperation(value="新增按钮")
    public R add(@Valid @RequestBody AddMenuButtonDto dto){
        long count = menuButtonService.count(Wrappers.<MenuButton>query().lambda()
                .eq(MenuButton::getName,dto.getName())
                .eq(MenuButton::getMenuId,dto.getMenuId())
                .or()
                .eq(MenuButton::getCode,dto.getCode())
                .eq(MenuButton::getMenuId,dto.getMenuId()));

        if(count > 0){
            return R.error("当前菜单下已经有相同名字或者编码的按钮！");
        }
        MenuButton menuButton = BeanUtil.toBean(dto, MenuButton.class);

        return R.ok(menuButtonService.save(menuButton));
    }

    @PutMapping
    @ApiOperation(value="修改按钮")
    public R update(@Valid @RequestBody UpdateMenuButtonDto dto){
        long count = menuButtonService.count(Wrappers.<MenuButton>query().lambda()
                .eq(MenuButton::getName,dto.getName())
                .eq(MenuButton::getMenuId,dto.getMenuId())
                .ne(MenuButton::getId,dto.getId())
                .or()
                .eq(MenuButton::getCode,dto.getCode())
                .eq(MenuButton::getMenuId,dto.getMenuId())
                .ne(MenuButton::getId,dto.getId()));

        if(count > 0){
            return R.error("当前菜单下已经有相同名字或者编码的按钮！");
        }
        MenuButton menuButton = BeanUtil.toBean(dto, MenuButton.class);

        return R.ok(menuButtonService.updateById(menuButton));
    }


}
