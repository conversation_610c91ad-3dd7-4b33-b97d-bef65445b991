package com.xjrsoft.module.system.controller;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.yulichang.toolkit.MPJWrappers;
import com.jdcloud.sdk.utils.StringUtils;
import com.xjrsoft.common.constant.GlobalConstant;
import com.xjrsoft.common.enums.AuthorizeType;
import com.xjrsoft.common.enums.EnabledMark;
import com.xjrsoft.common.enums.MenuType;
import com.xjrsoft.common.enums.YesOrNoEnum;
import com.xjrsoft.common.model.result.R;
import com.xjrsoft.common.page.ConventPage;
import com.xjrsoft.common.page.PageInput;
import com.xjrsoft.common.page.PageOutput;
import com.xjrsoft.common.utils.BeanUtils;
import com.xjrsoft.common.utils.TreeUtil;
import com.xjrsoft.common.utils.VoToColumnUtil;
import com.xjrsoft.module.organization.service.IUserRoleRelationService;
import com.xjrsoft.module.system.dto.*;
import com.xjrsoft.module.system.entity.*;
import com.xjrsoft.module.system.service.*;
import com.xjrsoft.module.system.vo.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 用户 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-16
 */
@RestController
@RequestMapping(GlobalConstant.SYSTEM_MODULE_PREFIX + "/menu")
@Api(value = GlobalConstant.SYSTEM_MODULE_PREFIX + "/menu", tags = "菜单模块")
@AllArgsConstructor
public class MenuController {

    private final IMenuService menuService;

    private final IMenuButtonService menuButtonService;

    private final IMenuColumnService menuColumnService;

    private final IMenuFormService menuFormService;

    private final IAuthorizeService authorizeService;

    private final IUserRoleRelationService userRoleRelationService;

    @GetMapping("/list")
    @ApiOperation(value = "获取所有菜单（不分页）")
    public R list() {

        List<Menu> list = menuService.list(Wrappers.lambdaQuery(Menu.class)
                .select(Menu.class, x -> VoToColumnUtil.fieldsToColumns(MenuListVo.class).contains(x.getProperty())));
        List<MenuListVo> menuListVos = BeanUtil.copyToList(list, MenuListVo.class);
        return R.ok(TreeUtil.build(menuListVos));
    }

    @GetMapping("/tree")
    @ApiOperation(value = "获取所有菜单（树结构）")
    public R menuTree(@Valid MenuTreeDto dto) {
        List<MenuVo> list = menuService.getAuthMenuList(dto);
        List<MenuTreeVo> voList = BeanUtil.copyToList(list, MenuTreeVo.class);
        List<MenuTreeVo> treeVoList = TreeUtil.build(voList);

        return R.ok(treeVoList);
    }

    @GetMapping("/all-tree")
    @ApiOperation(value = "获取所有菜单（树结构）")
    public R allMenuTree(@Valid MenuTreeDto dto) {

        List<MenuTreeVo> list = menuService.selectJoinList(MenuTreeVo.class,
                MPJWrappers.<Menu>lambdaJoin()
                        .disableSubLogicDel()
                        .like(StrUtil.isNotEmpty(dto.getTitle()), Menu::getTitle, dto.getTitle())
                        .like(StrUtil.isNotEmpty(dto.getName()), Menu::getName, dto.getName())
                        .isNotNull(StrUtil.isNotEmpty(dto.getIsRelease()), Menu::getZyFormListMenuId)
                        .select(Menu::getId)
                        .select(Menu.class, x -> VoToColumnUtil.fieldsToColumns(MenuTreeVo.class).contains(x.getProperty()))
                        .selectAs(Subsystem::getName, MenuTreeVo::getSystemName)
                        .eq(ObjectUtils.isNotEmpty(dto.getEnabledMark()), Menu::getEnabledMark, dto.getEnabledMark())
                        .leftJoin(Subsystem.class, Subsystem::getId, Menu::getSystemId)
                        .orderByAsc(Menu::getSortCode));

        List<MenuTreeVo> treeVoList = TreeUtil.build(list);

        return R.ok(treeVoList);
    }

    @GetMapping("/relese-tree")
    @ApiOperation(value = "获取发布的表单菜单")
    public R releseMenuTree(@Valid MenuTreeDto dto) {

        List<MenuTreeReleaseVo> listTreeVoList = new ArrayList<>();
        List<MenuTreeReleaseVo> list = menuService.selectJoinList(MenuTreeReleaseVo.class,
                MPJWrappers.<Menu>lambdaJoin()
                        .disableSubLogicDel()
                        .like(StrUtil.isNotEmpty(dto.getTitle()), Menu::getTitle, dto.getTitle())
                        .like(StrUtil.isNotEmpty(dto.getName()), Menu::getName, dto.getName())
                        .isNotNull(true, Menu::getZyFormListMenuId)
                        .select(Menu::getId)
                        .select(Menu.class, x -> VoToColumnUtil.fieldsToColumns(MenuTreeVo.class).contains(x.getProperty()))
                        .selectAs(Subsystem::getName, MenuTreeVo::getSystemName)
                        .eq(ObjectUtils.isNotEmpty(dto.getEnabledMark()), Menu::getEnabledMark, dto.getEnabledMark())
                        .leftJoin(Subsystem.class, Subsystem::getId, Menu::getSystemId)
                        .orderByAsc(Menu::getSortCode));
        listTreeVoList.addAll(list);
        Map<String,String> map = new HashMap<>();
        list.forEach(menuTreeVo -> {
            menuTreeVo.setMenuType(0);
            map.put(String.valueOf(menuTreeVo.getId()),String.valueOf(menuTreeVo.getId()));
            if (menuTreeVo.getParentId() != null) {
                getParentMenu(menuTreeVo.getParentId(), listTreeVoList,map);
            }
        });

        List<MenuTreeReleaseVo> treeVoList = TreeUtil.build(listTreeVoList);

        return R.ok(treeVoList);
    }

    public void getParentMenu(String id, List<MenuTreeReleaseVo> list,Map<String,String> map) {
        Menu menu = menuService.getById(id);
        if (menu != null && !map.containsKey(String.valueOf(menu.getId()))) {
            map.put(String.valueOf(menu.getId()),String.valueOf(menu.getId()));
            MenuTreeReleaseVo bean = BeanUtils.toBean(menu, MenuTreeReleaseVo.class);
            list.add(bean);
            if (menu.getParentId() != null && !menu.getParentId().equals(id)) {
                getParentMenu(bean.getParentId(), list,map);
            }
        }
    }

    @GetMapping("/page")
    @ApiOperation(value = "获取菜单（分页）")
    public R page(String id, PageInput dto) {
        if (StrUtil.isEmpty(id)) {
            return R.error("id不能为空！");
        }
        Menu menu = menuService.getById(id);
        if (menu == null) {
            return R.error("找不到此菜单！");
        }
        List<String> ids = new ArrayList<>();
        ids.add(id);
        getChildren(id, ids);
        LambdaQueryWrapper<Menu> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(Menu::getId, ids);
        queryWrapper.isNotNull(Menu::getZyFormListMenuId);
        IPage<Menu> page = menuService.page(ConventPage.getPage(dto), queryWrapper);
        PageOutput<MenuDTO> pageOutput = ConventPage.getPageOutput(page, MenuDTO.class);
        return R.ok(pageOutput);
    }

    public void getChildren(String id, List<String> ids) {
        List<Menu> children = menuService.list(Wrappers.<Menu>query().lambda().eq(Menu::getParentId, id));
        if (children != null && children.size() > 0) {
            for (Menu child : children) {
                ids.add(child.getId());
                getChildren(child.getId(), ids);
            }
        }
    }

    @GetMapping("/child-tree")
    @ApiOperation(value = "获取所有菜单（树结构，禁用父级）")
    public R menuChildTree(@Valid MenuTreeDto dto) {
        List<MenuVo> list = menuService.getAuthMenuList(dto);
        List<MenuChildTreeVo> voList = BeanUtil.copyToList(list, MenuChildTreeVo.class);
        List<MenuChildTreeVo> treeVoList = TreeUtil.build(voList);
        voList.forEach(treeVo -> treeVo.setFullPath(true));
        return R.ok(treeVoList);
    }

    @GetMapping("/simple-tree")
    @ApiOperation(value = "获取所有菜单（树结构）")
    public R menuAuthTree(@Valid MenuTreeDto dto) {
        List<MenuVo> list = menuService.getAuthMenuList(dto);
        List<MenuSimpleTreeVo> voList = BeanUtil.copyToList(list, MenuSimpleTreeVo.class);
        List<MenuSimpleTreeVo> treeVoList = TreeUtil.build(voList);
        return R.ok(treeVoList);
    }

    @GetMapping(value = "/info")
    @ApiOperation(value = "根据id查询菜单信息")
    public R info(String id) {
        Menu menu = menuService.getById(id);
        if (menu == null) {
            R.error("找不到此菜单！");
        }
        return R.ok(BeanUtil.toBean(menu, MenuVo.class));
    }

    @PostMapping
    @ApiOperation(value = "新增菜单")
    @Transactional(rollbackFor = Exception.class)
    public R add(@Valid @RequestBody AddMenuDto dto) {
        long count = menuService.count(Wrappers.<Menu>query().lambda().eq(Menu::getName, dto.getName()).or().eq(Menu::getCode, dto.getCode()));
        if (count > 0) {
            return R.error("菜单名或者菜单编码已经存在！");
        }
        Menu menu = BeanUtil.toBean(dto, Menu.class);

        if (ObjectUtil.isEmpty(menu.getSystemId())) {
            menu.setSystemId(1L);
        }

        //判断是否为菜单
        if (menu.getMenuType() == YesOrNoEnum.NO.getCode()) {
            //如果是菜单 需要判断 path 第一个字符 是否为 /  菜单必须要 / 开头
            if (!menu.getPath().startsWith(StringPool.SLASH)) {
                menu.setPath(StringPool.SLASH + menu.getPath());
            }
        }

        //判断是否为外链
        if (menu.getOutLink() == YesOrNoEnum.YES.getCode()) {
            //如果是外链 所有组件地址 改为 IFrame
            menu.setComponent("IFrame");
        }

        menu.setAllowDelete(YesOrNoEnum.YES.getCode());
        menu.setAllowModify(YesOrNoEnum.YES.getCode());

        boolean isSuccess = menuService.save(menu);

        //按钮保存
        List<MenuButton> menuButtons = BeanUtil.copyToList(dto.getButtonList(), MenuButton.class);
        menuButtons.forEach(menuButton -> menuButton.setMenuId(menu.getId()));
        //删除原按钮
        menuButtonService.remove(Wrappers.<MenuButton>query().lambda().eq(MenuButton::getMenuId, menu.getId()));
        menuButtonService.saveBatch(menuButtons);

        // 列表字段保存
        List<MenuColumn> columnList = BeanUtil.copyToList(dto.getColumnList(), MenuColumn.class);
        columnList.forEach(menuColumn -> menuColumn.setMenuId(menu.getId()));
        menuColumnService.remove(Wrappers.<MenuColumn>query().lambda().eq(MenuColumn::getMenuId, menu.getId()));
        menuColumnService.saveBatch(columnList);

        // 表单字段保存
        List<MenuForm> formList = new ArrayList<>();
        for (AddMenuFormDto addMenuFormDto : dto.getFormList()) {
            formList.add(BeanUtil.toBean(addMenuFormDto, MenuForm.class));
            List<AddMenuFormDto> children = addMenuFormDto.getChildren();
            if (org.apache.commons.collections.CollectionUtils.isNotEmpty(children)) {
                formList.addAll(BeanUtil.copyToList(children, MenuForm.class));
            }
        }
        formList.forEach(menuForm -> menuForm.setMenuId(menu.getId()));
        menuFormService.remove(Wrappers.<MenuForm>query().lambda().eq(MenuForm::getMenuId, menu.getId()));
        menuFormService.saveBatch(formList);


        return R.ok(isSuccess);
    }

    @PutMapping
    @ApiOperation(value = "修改菜单")
    @Transactional(rollbackFor = Exception.class)
    public R edit(@Valid @RequestBody UpdateMenuDto dto) {
        long count = menuService.count(Wrappers.<Menu>query().lambda()
                .ne(Menu::getId, dto.getId()).and(wrapper ->
                        wrapper.eq(Menu::getName, dto.getName())
                                .or()
                                .eq(Menu::getCode, dto.getCode())));

        if (count > 0) {
            return R.error("菜单名或者菜单编码已经存在！");
        }
        Menu menu = BeanUtil.toBean(dto, Menu.class);

        //判断是否为菜单
        if (menu.getMenuType() == YesOrNoEnum.NO.getCode()) {
            //如果是菜单 需要判断 path 第一个字符 是否为 /  菜单必须要 / 开头
            if (!menu.getPath().startsWith(StringPool.SLASH)) {
                menu.setPath(StringPool.SLASH + menu.getPath());
            }
        }

        //判断是否为外链
        if (menu.getOutLink() == YesOrNoEnum.YES.getCode()) {
            //如果是外链 所有组件地址 改为 IFrame
            menu.setComponent("IFrame");
        }

        menuService.updateById(menu);
        return R.ok();
    }

    @GetMapping(value = "/button")
    @ApiOperation(value = "根据id查询按钮")
    public R button(@NotNull @Valid String id) {
        List<MenuButton> list = menuButtonService.list(Wrappers.<MenuButton>query().lambda().eq(MenuButton::getMenuId, id));
        return R.ok(list);
    }


    @DeleteMapping
    @ApiOperation(value = "批量删除菜单")
    public R delete(@Valid @RequestBody List<String> ids) {
        return R.ok(menuService.removeByIds(ids));
    }

    @GetMapping("/auth-tree")
    @ApiOperation(value = "获取功能授权页面的菜单树结构")
    public R fullAuthTree() {
        List<Long> roleIdList = StpUtil.getTokenSession().get(GlobalConstant.LOGIN_USER_ROLE_ID_KEY, new ArrayList<>());
        LambdaQueryWrapper<Menu> queryMenuWrapper = Wrappers.<Menu>query().lambda().eq(Menu::getEnabledMark, EnabledMark.ENABLED.getCode());
        LambdaQueryWrapper<MenuButton> queryBtnWrapper = Wrappers.<MenuButton>query().lambda();
        LambdaQueryWrapper<MenuColumn> queryColWrapper = Wrappers.<MenuColumn>query().lambda();
        LambdaQueryWrapper<MenuForm> queryFormWrapper = Wrappers.<MenuForm>query().lambda();
        boolean isAdmin = roleIdList.contains(GlobalConstant.SUPER_ADMIN_ROLE_ID);
        // 不是管理员
        if (!isAdmin) {
            List<Authorize> authorizeList = authorizeService.list(Wrappers.<Authorize>query().lambda()
                    .select(Authorize::getObjectId, Authorize::getAuthorizeType)
                    .in(Authorize::getAuthorizeType, AuthorizeType.MENU.getCode(), AuthorizeType.BUTTON.getCode(), AuthorizeType.COLUMN.getCode(), AuthorizeType.FORM.getCode())
                    .in(Authorize::getRoleId, roleIdList));
            if (CollectionUtils.isEmpty(authorizeList)) {
                return R.ok();
            }
            List<Long> authMenuIdList = new ArrayList<>();
            List<Long> authMenuBtnIdList = new ArrayList<>();
            List<Long> authMenuIdColList = new ArrayList<>();
            List<Long> authMenuIdFormList = new ArrayList<>();
            for (Authorize authorize : authorizeList) {
                Long id = authorize.getId();
                switch (authorize.getAuthorizeType()) {
                    case 1:
                        authMenuIdList.add(id);
                        break;
                    case 2:
                        authMenuBtnIdList.add(id);
                        break;
                    case 3:
                        authMenuIdColList.add(id);
                        break;
                    case 4:
                        authMenuIdFormList.add(id);
                        break;
                    default:
                        break;
                }
            }
            queryMenuWrapper.in(Menu::getId, authMenuIdList);
            queryBtnWrapper.in(MenuButton::getId, authMenuBtnIdList);
            queryColWrapper.in(MenuColumn::getId, authMenuIdColList);
            queryFormWrapper.in(MenuForm::getId, authMenuIdFormList);
        }
        List<Menu> menuList = menuService.list(queryMenuWrapper);
        List<MenuButton> menuBtnList = menuButtonService.list(queryBtnWrapper);
        List<MenuColumn> menuColList = menuColumnService.list(queryColWrapper);
        List<MenuForm> menuFormList = menuFormService.list(queryFormWrapper);
        List<MenuListVo> menuVoList = BeanUtil.copyToList(menuList, MenuListVo.class);
        AuthMenuVo resultVo = new AuthMenuVo();
        //菜单
        resultVo.setMenuList(TreeUtil.build(menuVoList));
        //按钮
        for (MenuButton menuBtn : menuBtnList) {
            resultVo.addButton(menuBtn.getMenuId(), BeanUtil.toBean(menuBtn, MenuButtonVo.class));
        }
        //列表
        for (MenuColumn menuColumn : menuColList) {
            resultVo.addColumn(menuColumn.getMenuId(), BeanUtil.toBean(menuColumn, MenuColumnVo.class));
        }
        //表单
        for (MenuForm menuForm : menuFormList) {
            resultVo.addForm(menuForm.getMenuId(), BeanUtil.toBean(menuForm, MenuFormVo.class));
        }
        return R.ok(resultVo);
    }

    @GetMapping("/parent-tree")
    @ApiOperation(value = "获取菜单树结构, 不包含页面类型的菜单")
    public R menuTree() {
        List<Menu> menuList = menuService.list(Wrappers.<Menu>query().lambda()
                .eq(Menu::getMenuType, MenuType.MENU.getCode())
                .eq(Menu::getEnabledMark, EnabledMark.ENABLED.getCode())
                .select(Menu.class, x -> VoToColumnUtil.fieldsToColumns(MenuListVo.class).contains(x.getProperty()))
                .orderByAsc(Menu::getSortCode));
        List<MenuListVo> voList = BeanUtil.copyToList(menuList, MenuListVo.class);
        List<MenuListVo> treeVoList = TreeUtil.build(voList);
        return R.ok(treeVoList);
    }

    @GetMapping("/validate-url")
    @ApiOperation(value = "验证菜单路由是否存在")
    public R validateUrl(@RequestParam String outputArea, String className) {
        String component = StringPool.SLASH + StringUtils.lowerCase(outputArea) + StringPool.SLASH + StringUtils.lowerCase(className) + StringPool.SLASH + "index";
        long count = menuService.count(Wrappers.lambdaQuery(Menu.class).eq(Menu::getComponent, component));
        return R.ok(count > 0);
    }

    @GetMapping("/validate-code")
    @ApiOperation(value = "验证菜单编码是否存在")
    public R validateCode(@RequestParam(required = false) Long id, @RequestParam String code) {
        long count = menuService.count(Wrappers.lambdaQuery(Menu.class).eq(Menu::getCode, code).ne(id != null, Menu::getId, id));
        return R.ok(count > 0);
    }
}
