package com.xjrsoft.module.system.controller;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.xjrsoft.common.constant.GlobalConstant;
import com.xjrsoft.common.model.result.R;
import com.xjrsoft.common.utils.TreeUtil;
import com.xjrsoft.common.utils.VoToColumnUtil;
import com.xjrsoft.module.system.entity.MenuForm;
import com.xjrsoft.module.system.service.IMenuFormService;
import com.xjrsoft.module.system.vo.MenuFormListVo;
import com.xjrsoft.module.system.vo.MenuFormVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 菜单表单字段 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-08
 */
@RestController
@RequestMapping(GlobalConstant.SYSTEM_MODULE_PREFIX + "/menu-form")
@Api(value = GlobalConstant.SYSTEM_MODULE_PREFIX + "/menu-form",tags = "菜单表单")
@AllArgsConstructor
public class MenuFormController {

    private final IMenuFormService menuFormService;

    @GetMapping(value = "/list")
    @ApiOperation(value="根据菜单id查询表单列表")
    public R getMenuColumnListByMenuId(@RequestParam(required = false) String menuId){
        if (menuId == null) {
            return R.ok(new ArrayList<>(0));
        }
        List<MenuForm> list = menuFormService.list(Wrappers.<MenuForm>lambdaQuery().eq(!"0".equals(menuId), MenuForm::getMenuId, menuId)
                .select(MenuForm.class, x -> VoToColumnUtil.fieldsToColumns(MenuFormVo.class).contains(x.getProperty())));
        List<MenuFormListVo> menuFormListVos = BeanUtil.copyToList(list, MenuFormListVo.class);
        return R.ok(TreeUtil.build(menuFormListVos));
    }
}
