package com.xjrsoft.module.system.controller;


import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.xjrsoft.common.constant.GlobalConstant;
import com.xjrsoft.common.model.result.R;
import com.xjrsoft.common.page.ConventPage;
import com.xjrsoft.common.utils.VoToColumnUtil;
import com.xjrsoft.module.system.dto.*;
import com.xjrsoft.module.system.entity.Subsystem;
import com.xjrsoft.module.system.service.IMenuService;
import com.xjrsoft.module.system.service.ISubsystemService;
import com.xjrsoft.module.system.vo.MenuVo;
import com.xjrsoft.module.system.vo.SubsystemListVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <p>
 * 子系统 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-08
 */
@RestController
@RequestMapping(GlobalConstant.SYSTEM_MODULE_PREFIX + "/subsystem")
@Api(value = GlobalConstant.SYSTEM_MODULE_PREFIX + "/subsystem", tags = "子系统管理")
@AllArgsConstructor
public class SubsystemController {

    private final ISubsystemService subsystemService;

    private final IMenuService menuService;

    @GetMapping("/list")
    @ApiOperation("查询子系统表列表数据(不分页)")
    public R list(@Valid SubsystemListDto dto) {
        List<String> roleIdList = StpUtil.getTokenSession().get(GlobalConstant.LOGIN_USER_ROLE_ID_KEY, new ArrayList<>());
        boolean isAdmin = roleIdList.contains(GlobalConstant.SUPER_ADMIN_ROLE_ID);
        Set<Long> systemIds = null;
        if (!isAdmin) {
            List<MenuVo> list = menuService.getAuthMenuList(new MenuTreeDto());
            systemIds = list.stream().map(menuVo -> menuVo.getSystemId()).collect(Collectors.toSet());
        }
        LambdaQueryWrapper<Subsystem> wrapper = Wrappers.<Subsystem>query().lambda()
                .like(StrUtil.isNotBlank(dto.getKeyword()), Subsystem::getName, dto.getKeyword())
                .in(!isAdmin, Subsystem::getId, systemIds)
                .last(StringUtils.isNotEmpty(dto.getField()), GlobalConstant.ORDER_BY + StringPool.SPACE + StrUtil.toUnderlineCase(dto.getField()) + StringPool.SPACE + ConventPage.getOrder(dto.getOrder()))
                .select(Subsystem.class, x -> VoToColumnUtil.fieldsToColumns(SubsystemListVo.class).contains(x.getProperty()))
                .orderByAsc(Subsystem::getSortCode);
        List<SubsystemListVo> subsystemListVos = BeanUtil.copyToList(subsystemService.list(wrapper), SubsystemListVo.class);
        return R.ok(subsystemListVos);
    }

    @GetMapping("/page")
    @ApiOperation("查询子系统表列表数据(分页)")
    public R page(@Valid SubsystemPageDto dto) {
        LambdaQueryWrapper<Subsystem> wrapper = Wrappers.<Subsystem>query().lambda()
                .like(StrUtil.isNotBlank(dto.getKeyword()), Subsystem::getName, dto.getKeyword())
                .last(StringUtils.isNotEmpty(dto.getField()), GlobalConstant.ORDER_BY + StringPool.SPACE + StrUtil.toUnderlineCase(dto.getField()) + StringPool.SPACE + ConventPage.getOrder(dto.getOrder()))
                .select(Subsystem.class, x -> VoToColumnUtil.fieldsToColumns(SubsystemListVo.class).contains(x.getProperty()))
                .orderByAsc(Subsystem::getSortCode);
        IPage<Subsystem> page = subsystemService.page(ConventPage.getPage(dto), wrapper);
        return R.ok(ConventPage.getPageOutput(page, SubsystemListVo.class));
    }


    @PostMapping
    @ApiOperation(value = "新增子系统")
    public R add(@Valid @RequestBody AddSubsystemDto dto) {
        Subsystem subsystem = BeanUtil.toBean(dto, Subsystem.class);
        return R.ok(subsystemService.save(subsystem));
    }

    @PutMapping
    @ApiOperation(value = "更新子系统")
    public R update(@Valid @RequestBody UpdateSubsystemDto dto){
        Subsystem subsystem = BeanUtil.toBean(dto, Subsystem.class);
        return R.ok(subsystemService.updateById(subsystem));
    }

//TODo:  用户授权相关表xjr_base_relation xjr_base_Authorize
    @GetMapping
    @ApiOperation("查询当前用户授权的子系统")
    public R AuthList(){

        return R.ok();
    }


    @DeleteMapping
    @ApiOperation(value = "删除")
    public R delete(@RequestBody List<Long> ids) {
        if (ids.contains(1L)) {
            return R.error("不能删除主系统！");
        }
        return R.ok(subsystemService.removeBatchByIds(ids));
    }

}
