package com.xjrsoft.module.system.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.xjrsoft.common.constant.GlobalConstant;
import com.xjrsoft.common.exception.MyException;
import com.xjrsoft.common.model.result.R;
import com.xjrsoft.module.organization.entity.User;
import com.xjrsoft.module.system.dto.UpdateLogoDto;
import com.xjrsoft.module.system.dto.UpdateMenuDto;
import com.xjrsoft.module.system.entity.File;
import com.xjrsoft.module.system.entity.LogoConfig;
import com.xjrsoft.module.system.entity.Menu;
import com.xjrsoft.module.system.service.IFileService;
import com.xjrsoft.module.system.service.ILogoConfigService;
import com.xjrsoft.module.system.vo.LogoInfoVo;
import com.xjrsoft.module.system.vo.MenuVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * <p>
 * Logo信息配置表【xjr_logo_config】 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-20
 */
@RestController
@RequestMapping(GlobalConstant.SYSTEM_MODULE_PREFIX+"/logoConfig")
@Api(value = GlobalConstant.SYSTEM_MODULE_PREFIX + "/logoConfig", tags = "Logo信息配置")
@AllArgsConstructor
public class LogoConfigController {

    private final ILogoConfigService logoConfigService;

    @GetMapping(value = "/logo-info")
    @ApiOperation(value = "登录时获取Logo信息配置")
    public R logoInfo() {
        LogoInfoVo logoInfoVo = logoConfigService.logoInfo();
        return R.ok(logoInfoVo);
    }


    @GetMapping(value = "/info")
    @ApiOperation(value = "Logo配置页获取Logo信息配置")
    public R info() {
        List<LogoConfig> list = logoConfigService.list();
        if (list.size() == 0){//如果没有，则设置为默认配置,并进行保存
            LogoConfig logoConfig = new LogoConfig();
            logoConfig.setId(1L);
            logoConfig.setCompanyName("力软信息技术（苏州）有限公司");
            logoConfig.setShortName("力软");
            list.add(logoConfig);
            logoConfigService.save(logoConfig);
        }
        return R.ok(list.get(0));
    }

    @PutMapping
    @ApiOperation(value = "修改Logo信息配置")
    @Transactional(rollbackFor = Exception.class)
    public R edit(@Valid @RequestBody UpdateLogoDto dto) {
        LogoConfig logoConfig = BeanUtil.toBean(dto, LogoConfig.class);
        return R.ok(logoConfigService.updateById(logoConfig));
    }

}
