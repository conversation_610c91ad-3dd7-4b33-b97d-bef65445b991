package com.xjrsoft.module.system.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.xjrsoft.common.constant.GlobalConstant;
import com.xjrsoft.common.model.result.R;
import com.xjrsoft.common.page.ConventPage;
import com.xjrsoft.common.page.PageOutput;
import com.xjrsoft.common.utils.VoToColumnUtil;
import com.xjrsoft.module.system.dto.*;
import com.xjrsoft.module.system.entity.DictionaryDetail;
import com.xjrsoft.module.system.entity.DictionaryItem;
import com.xjrsoft.module.system.service.IDictionarydetailService;
import com.xjrsoft.module.system.service.IDictionaryitemService;
import com.xjrsoft.module.system.vo.DictionaryItemDetailVo;
import com.xjrsoft.module.system.vo.DictionaryItemPageVo;
import com.xjrsoft.module.system.vo.DictionaryItemVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 数据字典项目 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-17
 */
@RestController
@RequestMapping(GlobalConstant.SYSTEM_MODULE_PREFIX + "/dictionary-item")
@Api(value = GlobalConstant.SYSTEM_MODULE_PREFIX + "/dictionary-item", tags = "数据字典项目")
@AllArgsConstructor
public class DictionaryitemController {

    private final IDictionaryitemService dictionaryitemService;

    private final IDictionarydetailService dictionarydetailService;


    @GetMapping("/list")
    @ApiOperation(value = "获取 数据字典项目列表（不分页）")
    public R list(@Valid DictionaryItemPageDto dto) {

        LambdaQueryWrapper<DictionaryItem> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.like(StrUtil.isNotBlank(dto.getName()), DictionaryItem::getName, dto.getName())
                .like(StrUtil.isNotBlank(dto.getCode()), DictionaryItem::getCode, dto.getCode())
                .select(DictionaryItem.class, x -> VoToColumnUtil.fieldsToColumns(DictionaryItemPageVo.class).contains(x.getProperty()));

        List<DictionaryItem> list = dictionaryitemService.list(queryWrapper);
        List<DictionaryItemVo> dictionaryItemVos = BeanUtil.copyToList(list, DictionaryItemVo.class);
        return R.ok(dictionaryItemVos);
    }

    @GetMapping("/page")
    @ApiOperation(value = "获取所有数据字典项目（分页）")
    public R page(@Valid DictionaryItemPageDto dto) {

        LambdaQueryWrapper<DictionaryItem> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.like(StrUtil.isNotBlank(dto.getName()), DictionaryItem::getName, dto.getName())
                .like(StrUtil.isNotBlank(dto.getCode()), DictionaryItem::getCode, dto.getCode())
                .select(DictionaryItem.class, x -> VoToColumnUtil.fieldsToColumns(DictionaryItemPageVo.class).contains(x.getProperty()))
                .orderByAsc(DictionaryItem::getSortCode);

        IPage<DictionaryItem> page = dictionaryitemService.page(ConventPage.getPage(dto), queryWrapper);
        PageOutput<DictionaryItemPageVo> pageOutput = ConventPage.getPageOutput(page, DictionaryItemPageVo.class);

        return R.ok(pageOutput);
    }

    @GetMapping("/tree")
    @ApiOperation(value = "获取所有数据字典项目")
    public R tree(@Valid DictionaryItemPageDto dto) {
        JSONArray jsonArray = new JSONArray();
        //第一级
        List<DictionaryItem> list1 = new ArrayList<>();
        if (StrUtil.isNotBlank(dto.getId())) {
            list1 = dictionaryitemService.list(new LambdaQueryWrapper<DictionaryItem>()
                    .eq(DictionaryItem::getId, dto.getId())
                    .orderBy(StrUtil.isNotEmpty(dto.getOrder()) ,"asc".equals(dto.getOrder()),DictionaryItem::getSortCode)
            );
        } else {
            list1 = dictionaryitemService.list(new LambdaQueryWrapper<DictionaryItem>()
                    .eq(DictionaryItem::getParentId, 0)
                    .orderBy(StrUtil.isNotEmpty(dto.getOrder()) ,"asc".equals(dto.getOrder()),DictionaryItem::getSortCode)
            );
        }

        //第二级
        list1.forEach(item -> {
            JSONArray jsonArray2 = new JSONArray();
            List<DictionaryItem> list2 = dictionaryitemService.list(new LambdaQueryWrapper<DictionaryItem>()
                    .eq(DictionaryItem::getParentId, item.getId())
                    .orderBy(StrUtil.isNotEmpty(dto.getOrder()) ,"asc".equals(dto.getOrder()),DictionaryItem::getSortCode)


            );
            //第三级
            list2.forEach(item2 -> {
                List<DictionaryItem> list3 = dictionaryitemService.list(new LambdaQueryWrapper<DictionaryItem>()
                        .eq(DictionaryItem::getParentId, item2.getId())
                        .orderBy(StrUtil.isNotEmpty(dto.getOrder()) ,"asc".equals(dto.getOrder()),DictionaryItem::getSortCode)

                );
                JSONArray jsonArray3 = new JSONArray();
                list3.forEach(item3 -> {
                    JSONObject jsonObject3 = new JSONObject();
                    jsonObject3.put("name", item3.getName());
                    jsonObject3.put("id", item3.getId());
                    jsonObject3.put("code", item3.getCode());
                    jsonObject3.put("remark", item3.getRemark());
                    jsonObject3.put("parentId", item2.getId());
                    jsonObject3.put("sortCode", item3.getSortCode());
                    jsonArray3.add(jsonObject3);
                });
                JSONObject jsonObject2 = new JSONObject();
                jsonObject2.put("name", item2.getName());
                jsonObject2.put("code", item2.getCode());
                jsonObject2.put("remark", item2.getRemark());
                jsonObject2.put("id", item2.getId());
                jsonObject2.put("parentId", item.getId());
                jsonObject2.put("sortCode", item2.getSortCode());
                jsonObject2.put("children", jsonArray3);
                jsonArray2.add(jsonObject2);
            });
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("name", item.getName());
            jsonObject.put("code", item.getCode());
            jsonObject.put("remark", item.getRemark());
            jsonObject.put("id", item.getId());
            jsonObject.put("parentId", "0");
            jsonObject.put("sortCode", item.getSortCode());
            jsonObject.put("children", jsonArray2);
            jsonArray.add(jsonObject);
        });


        return R.ok(jsonArray);
    }

    @GetMapping("/info")
    @ApiOperation(value = "根据id获取项目详情")
    public R info(@RequestParam Long id) {
        DictionaryItem dictionaryItem = dictionaryitemService.getById(id);
        if (dictionaryItem == null) {
            R.error("找不到此字典项目!");
        }

        return R.ok(BeanUtil.toBean(dictionaryItem, DictionaryItemVo.class));
    }

    @GetMapping("/detail")
    @ApiOperation(value = "获取当前数据字典项目下所有字典详情")
    public R detail(@Valid DictionaryItemDetailDto dto) {
        List<String> values = StrUtil.split(dto.getValues(), StringPool.COMMA);
        LambdaQueryWrapper<DictionaryDetail> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DictionaryDetail::getItemId, dto.getId())
                .in(CollectionUtils.isNotEmpty(values), DictionaryDetail::getValue, values)
                .select(DictionaryDetail.class, x -> VoToColumnUtil.fieldsToColumns(DictionaryItemDetailVo.class).contains(x.getProperty()));

        List<DictionaryDetail> list = dictionarydetailService.list(queryWrapper);

        List<DictionaryItemDetailVo> dictionaryItemDetailVos = BeanUtil.copyToList(list, DictionaryItemDetailVo.class);
        return R.ok(dictionaryItemDetailVos);
    }

    @GetMapping("/detail/tree")
    @ApiOperation(value = "获取数据字典项目 + 详情  树结构")
    public R tree() {
        return R.ok(dictionaryitemService.getTree());
    }


    @GetMapping("/detail/page")
    @ApiOperation(value = "获取当前数据字典项目下所有字典详情(分页)")
    public R detail(@Valid DictionaryItemDetailPageDto dto) {

        LambdaQueryWrapper<DictionaryDetail> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DictionaryDetail::getItemId, dto.getId())
                .select(DictionaryDetail.class, x -> VoToColumnUtil.fieldsToColumns(DictionaryItemDetailVo.class).contains(x.getProperty()));

        IPage<DictionaryDetail> page = dictionarydetailService.page(ConventPage.getPage(dto), queryWrapper);
        PageOutput<DictionaryItemDetailVo> pageOutput = ConventPage.getPageOutput(page, DictionaryItemDetailVo.class);
        return R.ok(pageOutput);
    }

    @PostMapping
    @ApiOperation(value = "新增数据字典项")
    public R add(@Valid @RequestBody AddDictionaryItemDto dto) {

        long count = dictionaryitemService.count(Wrappers.<DictionaryItem>query().lambda().eq(DictionaryItem::getCode, dto.getCode()).or().eq(DictionaryItem::getName, dto.getName()));
        if (count > 0) {
            return R.error("项目名或者编码已经存在！");
        }

        DictionaryItem dictionaryItem = BeanUtil.toBean(dto, DictionaryItem.class);

        boolean isSuccess = dictionaryitemService.save(dictionaryItem);
        if (isSuccess) dictionaryitemService.loadCaches();
        return R.ok(isSuccess);
    }

    @PutMapping
    @ApiOperation(value = "更新数据字典项")
    public R update(@Valid @RequestBody UpdateDictionaryItemDto dto) {

        long count = dictionaryitemService.count(Wrappers.<DictionaryItem>query().lambda()
                .eq(DictionaryItem::getCode, dto.getCode())
                .ne(DictionaryItem::getId, dto.getId())
                .or()
                .eq(DictionaryItem::getName, dto.getName())
                .ne(DictionaryItem::getId, dto.getId()));
        if (count > 0) {
            return R.error("项目名或者编码已经存在！");
        }
        DictionaryItem dictionaryItem = BeanUtil.toBean(dto, DictionaryItem.class);

        boolean isSuccess = dictionaryitemService.updateById(dictionaryItem);
        if (isSuccess) dictionaryitemService.loadCaches();
        return R.ok(isSuccess);
    }

    @DeleteMapping
    @ApiOperation(value = "删除数据字典项")
    @Transactional(rollbackFor = Exception.class)
    public R delete(@RequestBody List<Long> ids) {
        //删除字典详情
        dictionaryitemService.removeBatchByIds(ids);
        //删除字典项
        dictionarydetailService.remove(Wrappers.lambdaQuery(DictionaryDetail.class).in(DictionaryDetail::getItemId, ids));
        return R.ok();
    }

    /**
     * 初始化字典项
     */
    @RequestMapping("/initDictItem")
    public R initDictItem() {
        JSONObject jsonObject = new JSONObject();
        List<DictionaryItem> list = dictionaryitemService.list();
        list.forEach(item -> {
            JSONArray jsonArray = new JSONArray();
            dictionarydetailService.list(Wrappers.<DictionaryDetail>query().lambda().eq(DictionaryDetail::getItemId, item.getId())).forEach(item2 -> {
                JSONObject itemDetail = new JSONObject();
                itemDetail.put("label", item2.getName());
                itemDetail.put("value", item2.getValue());
                itemDetail.put("id", item2.getId());
                jsonArray.add(itemDetail);
            });
            jsonObject.put(item.getCode(), jsonArray);
        });
        return R.ok(jsonObject);
    }


}
