package com.xjrsoft.module.system.controller;


import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.yulichang.toolkit.MPJWrappers;
import com.xjrsoft.common.constant.GlobalConstant;
import com.xjrsoft.common.enums.StampEnum;
import com.xjrsoft.common.enums.YesOrNoEnum;
import com.xjrsoft.common.model.result.R;
import com.xjrsoft.common.page.ConventPage;
import com.xjrsoft.common.page.PageOutput;
import com.xjrsoft.common.utils.VoToColumnUtil;
import com.xjrsoft.module.system.dto.*;
import com.xjrsoft.module.system.entity.DictionaryDetail;
import com.xjrsoft.module.system.entity.Stamp;
import com.xjrsoft.module.system.service.IStampService;
import com.xjrsoft.module.system.vo.StampListVo;
import com.xjrsoft.module.system.vo.StampPageVo;
import com.xjrsoft.module.system.vo.StampVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * <p>
 * 印章表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-24
 */
@RestController
@RequestMapping(GlobalConstant.SYSTEM_MODULE_PREFIX + "/stamp")
@Api(value = GlobalConstant.SYSTEM_MODULE_PREFIX + "/stamp", tags = "签章模块")
@AllArgsConstructor
public class StampController {

    private final IStampService stampService;


    @GetMapping("/list")
    @ApiOperation("查询列表数据(不分页)")
    public R list(@Valid StampListDto dto) {
        List<StampListVo> stampListVos = stampService.selectJoinList(StampListVo.class, MPJWrappers.<Stamp>lambdaJoin()
                .disableSubLogicDel()
                .eq(Stamp::getStampType, dto.getStampType())
                .eq(dto.getStampType() == StampEnum.PRIVATE.getCode(), Stamp::getCreateUserId, StpUtil.getLoginIdAsString())
                .like(StrUtil.isNotBlank(dto.getKeyword()), Stamp::getName, dto.getKeyword())
                .eq(ObjectUtil.isNotNull(dto.getEnabledMark()), Stamp::getEnabledMark, dto.getEnabledMark())
                .eq(ObjectUtil.isNotNull(dto.getStampCategory()), Stamp::getStampCategory, dto.getStampCategory())
                .select(Stamp::getId)
                .select(Stamp.class, x -> VoToColumnUtil.fieldsToColumns(StampPageVo.class).contains(x.getProperty()))
                .selectAs(DictionaryDetail::getValue, StampPageVo::getStampCategoryName)
                .leftJoin(DictionaryDetail.class, DictionaryDetail::getId, Stamp::getStampCategory));

        return R.ok(stampListVos);
    }

    @GetMapping("/page")
    @ApiOperation("查询列表数据(分页)-所有未禁用签章")
    public R page(@Valid StampPageDto dto) {
        IPage<StampPageVo> page = stampService.selectJoinListPage(ConventPage.getPage(dto), StampPageVo.class,
                MPJWrappers.<Stamp>lambdaJoin()
                        .disableSubLogicDel()
//                        .eq(Stamp::getStampType, dto.getStampType())
                        .eq(Stamp::getEnabledMark,YesOrNoEnum.YES.getCode())
                        .eq(dto.getStampType() == StampEnum.PRIVATE.getCode(), Stamp::getCreateUserId, StpUtil.getLoginIdAsString())
                        .like(StrUtil.isNotBlank(dto.getKeyword()), Stamp::getName, dto.getKeyword())
                        .eq(ObjectUtil.isNotNull(dto.getEnabledMark()), Stamp::getEnabledMark, dto.getEnabledMark())
                        .eq(ObjectUtil.isNotNull(dto.getStampCategory()), Stamp::getStampCategory, dto.getStampCategory())
                        .select(Stamp::getId)
                        .select(Stamp.class, x -> VoToColumnUtil.fieldsToColumns(StampPageVo.class).contains(x.getProperty()))
                        .selectAs(DictionaryDetail::getName, StampPageVo::getStampCategoryName)
                        .leftJoin(DictionaryDetail.class, DictionaryDetail::getId, Stamp::getStampCategory));
        PageOutput<StampPageVo> pageOutput = ConventPage.getPageOutput(page, StampPageVo.class);

        return R.ok(pageOutput);
    }

    @GetMapping("/page-one")
    @ApiOperation("查询列表数据(分页)-单独电子签章或公共签章")
    public R pageOne(@Valid StampPageDto dto) {
        IPage<StampPageVo> page = stampService.selectJoinListPage(ConventPage.getPage(dto), StampPageVo.class,
                MPJWrappers.<Stamp>lambdaJoin()
                        .disableSubLogicDel()
                        .eq(Stamp::getStampType, dto.getStampType())
                        .eq(dto.getStampType() == StampEnum.PRIVATE.getCode(), Stamp::getCreateUserId, StpUtil.getLoginIdAsString())
                        .like(StrUtil.isNotBlank(dto.getKeyword()), Stamp::getName, dto.getKeyword())
                        .eq(ObjectUtil.isNotNull(dto.getEnabledMark()), Stamp::getEnabledMark, dto.getEnabledMark())
                        .eq(ObjectUtil.isNotNull(dto.getStampCategory()), Stamp::getStampCategory, dto.getStampCategory())
                        .orderByAsc(Stamp::getSortCode)
                        .select(Stamp::getId)
                        .select(Stamp.class, x -> VoToColumnUtil.fieldsToColumns(StampPageVo.class).contains(x.getProperty()))
                        .selectAs(DictionaryDetail::getName, StampPageVo::getStampCategoryName)
                        .leftJoin(DictionaryDetail.class, DictionaryDetail::getId, Stamp::getStampCategory));
        PageOutput<StampPageVo> pageOutput = ConventPage.getPageOutput(page, StampPageVo.class);

        return R.ok(pageOutput);
    }

    @GetMapping(value = "/info")
    @ApiOperation(value = "查询签章详情")
    public R info(@RequestParam Long id) {
        Stamp stamp = stampService.getById(id);
        if (stamp == null) {
            R.error("找不到此用户！");
        }
        return R.ok(BeanUtil.toBean(stamp, StampVo.class));
    }


    @PostMapping
    @ApiOperation(value = "新增")
    public R add(@Valid @RequestBody AddStampDto dto) {
        Stamp stamp = BeanUtil.toBean(dto, Stamp.class);
        stamp.setIsDefault(YesOrNoEnum.NO.getCode());
        return R.ok(stampService.save(stamp));
    }

    @PutMapping
    @ApiOperation(value = "修改")
    public R update(@Valid @RequestBody UpdateStampDto dto) {
        return R.ok(stampService.updateStamp(dto));
    }


    @DeleteMapping
    @ApiOperation(value = "删除签章(可批量删除)")
    public R delete(@Valid @RequestBody List<Long> ids) {
        return R.ok(stampService.deleteStamp(ids));
    }


    @PutMapping("/enabled")
    @ApiOperation(value = "启用/禁用")
    public R enabled(@Valid @RequestBody StampEnabledDto dto) {
        return R.ok(stampService.enabled(dto.getId()));

    }

    @PutMapping("/set-default")
    @ApiOperation(value = "设定为默认签章")
    public R setDefault(@Valid @RequestBody SetDefaultStampDto dto) {
        return R.ok(stampService.setDefaultStamp(dto.getId()));
    }


    @GetMapping("/member")
    @ApiOperation(value = "查询签章成员")
    public R getMember(Long id) {
        return R.ok(stampService.selectMember(id));
    }

    //添加成员
    @PutMapping("/add-member")
    @ApiOperation(value = "添加成员")
    public R addMember(@Valid @RequestBody AddMemberDto dto) {
        return R.ok(stampService.addMember(dto));
    }

    @GetMapping("/maintain")
    @ApiOperation(value = "查询维护成员")
    public R getMaintain(Long id) {
        return R.ok(stampService.selectMaintain(id));
    }


    @PutMapping("/add-maintain")
    @ApiOperation(value = "添加维护成员")
    public R addMaintain(@Valid @RequestBody AddMemberDto dto) {
        return R.ok(stampService.addMaintain(dto));
    }
}




