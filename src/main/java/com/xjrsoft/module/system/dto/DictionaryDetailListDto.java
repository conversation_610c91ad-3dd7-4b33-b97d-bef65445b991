package com.xjrsoft.module.system.dto;

import lombok.Data;
import org.hibernate.validator.constraints.Length;

/**
 * @Author: tzx
 * @Date:2022/3/30 14:31
 */
@Data
public class DictionaryDetailListDto {

    @Length(max = 20,message = "字典名不超过20字符！")
    private String name;

    @Length(max = 10,message = "编码不超过20字符！")
    private String code;

    private Long itemId;
    private String url;
    private String hoverUrl;
}
