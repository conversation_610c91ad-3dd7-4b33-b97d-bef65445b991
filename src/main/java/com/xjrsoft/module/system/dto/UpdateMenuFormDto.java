package com.xjrsoft.module.system.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class UpdateMenuFormDto {

    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("菜单主键")
    private String menuId;

    @ApiModelProperty("父级字段")
    private Long parentId;

    @ApiModelProperty("编码")
    private String code;

    @ApiModelProperty("名称")
    private String name;

    @ApiModelProperty("排序码")
    private Integer sortCode;

    @ApiModelProperty("是否必填，0-非必填，1-必填")
    private Integer isRequired;

    private List<UpdateMenuFormDto> children;
}
