package com.xjrsoft.module.system.dto;

import com.xjrsoft.common.page.PageInput;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * @Author: tzx
 * @Date:2022/3/18 17:08
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class DictionaryItemDetailPageDto extends PageInput {
    /**
     * 数据字典分类id
     */
    @NotNull(message = "数据字典分类 id 不能为空")
    private Long id;
}
