package com.xjrsoft.module.system.dto;

import com.xjrsoft.common.page.PageInput;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.validator.constraints.Length;

/**
 * @Author: tzx
 * @Date: 2022/3/30 14:59
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class DatabaseLinkPageDto extends PageInput {

    @Length(max = 10,message = "连接名不超过10个字符！")
    private String dbName;

    @Length(max = 10,message = "数据库类型不超过10个字符！")
    private String dbType;
}
