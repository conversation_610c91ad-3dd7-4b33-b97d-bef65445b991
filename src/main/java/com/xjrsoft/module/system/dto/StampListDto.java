package com.xjrsoft.module.system.dto;

import com.xjrsoft.common.page.ListInput;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotNull;

/**
 * @Author: tzx
 * @Date: 2023/2/22 15:48
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class StampListDto extends ListInput {

    @ApiModelProperty("签章类型 0 私人 1 公共")
    @NotNull(message = "签章类型不能为空！")
    private Integer stampType;

    @ApiModelProperty("启用 停用")
    private Integer enabledMark;

    @ApiModelProperty("签章分类(关联数据字典)")
    private Long stampCategory;
}
