package com.xjrsoft.module.system.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class LogExportDto {

    @ApiModelProperty("分类Id 1-登陆2-访问3-操作4-异常")
    @ExcelProperty("分类Id(1-登陆2-访问3-操作4-异常)")
    private Integer category;

    @ApiModelProperty("ip地址")
    @ExcelProperty("ip地址")
    private String ip;

    @ApiModelProperty("浏览器信息")
    @ExcelProperty("浏览器信息")
    private String userAgent;

    @ApiModelProperty("操作")
    @ExcelProperty("操作")
    private String operation;

    @ApiModelProperty("操作人")
    @ExcelProperty("操作人")
    private String username;

    @ApiModelProperty("请求方式")
    @ExcelProperty("请求方式")
    private String method;

    @ApiModelProperty("请求地址")
    @ExcelProperty("请求地址")
    private String url;

    @ApiModelProperty("参数")
    @ExcelProperty("参数")
    private String params;

    @ApiModelProperty("请求时长")
    @ExcelProperty("请求时长")
    private Long time;

    @ApiModelProperty("创建时间")
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;
}
