package com.xjrsoft.module.system.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class UpdateCodeSchemaDto {

    @ApiModelProperty("主键")
    private Long id;

    @ApiModelProperty("名称")
    private String name;

    @ApiModelProperty("类型（0-数据优先模板,1-界面优先模板,2-简易模板）")
    private Integer type;

    @ApiModelProperty("分类（关联数据字典）")
    private Long category;

    @ApiModelProperty("内容")
    private String content;

    @ApiModelProperty("描述")
    private String remark;

    @ApiModelProperty("模板状态（0-草稿，1-正式）")
    private Integer status;

    @ApiModelProperty("功能描述")
    private String description;
}
