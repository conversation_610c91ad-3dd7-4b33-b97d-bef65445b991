package com.xjrsoft.module.system.dto;

import com.xjrsoft.common.page.ListInput;
import com.xjrsoft.common.page.PageInput;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = true)
public class StampPageDto extends PageInput {


    @ApiModelProperty("签章类型 0 私人 1 公共")
    @NotNull(message = "签章类型不能为空！")
    private Integer stampType;

    @ApiModelProperty("启用 停用")
    private Integer enabledMark;

    @ApiModelProperty("签章分类(关联数据字典)")
    private Long stampCategory;



}
