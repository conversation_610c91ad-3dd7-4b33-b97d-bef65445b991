package com.xjrsoft.module.system.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
public class UpdateCodeRuleDto {

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空！")
    @ApiModelProperty("主键")
    private Long id;

    /**
     * 编号
     */
    @NotBlank(message = "编号不能为空！")
    @Length(max = 100, message = "编号长度不超过100字符！")
    @ApiModelProperty("编号")
    private String code;

    /**
     * 名称
     */
    @NotBlank(message = "名称不能为空！")
    @Length(max = 200, message = "名称长度不超过200字符！")
    @ApiModelProperty("名称")
    private String name;

    /**
     * 规则格式Json
     */
    @NotNull(message = "规则格式Json不能为空！")
    @ApiModelProperty("规则格式Json")
    private String formatJson;

    /**
     * 排序码
     */
    @ApiModelProperty("排序码")
    private Integer sortCode;

    /**
     * 有效标志
     */
    @ApiModelProperty("有效标志")
    private Integer enabledMark;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String description;
}
