package com.xjrsoft.module.system.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * @title: UpdateMenuDto
 * <AUTHOR>
 * @Date: 2023/11/20 14:04
 * @Version 1.0
 */
@Data
public class UpdateLogoDto implements Serializable {

    private static final long serialVersionUID = 1L;

    @NotNull(message = "主键不能为空")
    @ApiModelProperty("主键")
    private Long id;

    @ApiModelProperty("公司名称")
    private String companyName;

    @ApiModelProperty("公司简称")
    private String shortName;

    @ApiModelProperty("刷新logoId")
    private Long refreshLogoId;

    @ApiModelProperty("登录页logoId")
    private Long loginLogoId;

    @ApiModelProperty("菜单logoId")
    private Long menuLogoId;

    @ApiModelProperty("设计器logoId")
    private Long designerLogoId;

    @ApiModelProperty("登录页背景logoId")
    private Long backgroundLogoId;

    @ApiModelProperty("备注")
    private String remark;

    private String menuLogoDeepId;

    private String dsName;
    @ApiModelProperty("默认登录方式")
    private String defaultLoginType;
}
