package com.xjrsoft.module.system.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * @Author: tzx
 * @Date: 2022/11/29 9:39
 */
@Data
public class KeyCloakLoginInfoDto {

    @ApiModelProperty(value = "token")
    @NotBlank(message = "token不能为空！")
    private String token;

    @ApiModelProperty(value = "设备 PC 或者  APP")
    private String device = "PC";
}
