package com.xjrsoft.module.system.dto;

import com.xjrsoft.module.generator.entity.TableStructureConfig;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

@Data
public class ValidateTableDto {

    /**
     * 数据库id
     */
    @NotNull(message = "数据库id不能为空！")
    private String databaseId;

    /**
     * 表结构配置
     */
    @Valid
    @NotNull(message = "表结构配置不能为空！")
    private List<TableStructureConfig> tableStructureConfigs;
}
