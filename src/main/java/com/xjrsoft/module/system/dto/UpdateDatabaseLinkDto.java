package com.xjrsoft.module.system.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class UpdateDatabaseLinkDto implements Serializable {

    private static final long serialVersionUID = 1L;

    @NotNull(message = "主键不能为空！")
    @ApiModelProperty("主键")
    private Long id;

    @NotNull(message = "连接地址不能为空！")
    @Length(max = 255,message = "连接地址长度不超过255字符！")
    @ApiModelProperty("连接地址")
    private String host;

    @NotNull(message = "账号不能为空！")
    @Length(max = 50,message = "数据库账户不能超过50个字符！")
    @ApiModelProperty("账号")
    private String username;

    @NotNull(message = "密码不能为空！")
    @Length(max = 50,message = "数据库密码不能大于50个字符！")
    @ApiModelProperty("密码")
    private String password;

//    @NotNull(message = "密码不能为空！")
    @Length(max = 50,message = "驱动不能大于50个字符！")
    @ApiModelProperty("驱动")
    private String driver;

    @NotNull(message = "数据库名不能为空！")
    @Length(max = 50,message = "数据库名不能大于50个字符！")
    @ApiModelProperty("数据库名称")
    private String dbName;

    @NotNull(message = "数据库类型不能为空！")
    @Length(max = 50,message = "数据库类型不能大于50个字符！")
    @ApiModelProperty("数据库类型")
    private String dbType;

    @ApiModelProperty("数据库版本")
    private String dbVersion;

    @ApiModelProperty("备注")
    private String remark;

}
