package com.xjrsoft.module.system.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * @title: UpdateLoginConfigDto
 * <AUTHOR>
 * @Date: 2023/11/20 14:04
 * @Version 1.0
 */
@Data
public class UpdateLoginConfigDto implements Serializable {

    private static final long serialVersionUID = 1L;

    @NotNull(message = "主键不能为空")
    @ApiModelProperty("主键")
    private Long id;

    @ApiModelProperty("多端登录，存String形式，0-web，1-app")
    private String mulLogin;

    @ApiModelProperty("是否同端互斥,0-否，1-是")
    private Integer mutualExclusion;

    @ApiModelProperty("是否免登录,0-否，1-是")
    private Integer withoutLogin;

    @ApiModelProperty("密码策略,0-否，1-是")
    private Integer passwordStrategy;

    @ApiModelProperty("密码错误最大次数")
    private Integer strategyMaxNumber;
}