package com.xjrsoft.module.system.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
public class AddSubsystemDto implements Serializable {

    private static final long serialVersionUID = 1L;
    @ApiModelProperty("系统名")
    @NotNull(message = "系统名不能为空！")
    @Length(max = 20, message = "分类名称最多20个字符！")
    private String name;

    @ApiModelProperty("编码")
    @NotNull(message = "编码值不能为空！")
    @Length(min = 1,max = 20,message = "系统编码最少2个字符，最多20个字符！")
    private String code;

    @ApiModelProperty("图标")
    private String icon;

    @ApiModelProperty("排序码")
    @NotNull(message = "排序码值不能为空！")
    @Min(value = 0, message = "排序码必须大于0")
    private Integer sortCode;

    @ApiModelProperty("备注")
    private String description;

    @ApiModelProperty("主页地址")
    private String indexUrl;

    @ApiModelProperty("翻译标记")
    private String lgmarkcode;
}
