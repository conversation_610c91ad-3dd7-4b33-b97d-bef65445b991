package com.xjrsoft.module.system.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * @title: UpdateMenuDto
 * <AUTHOR>
 * @Date: 2022/4/4 19:04
 * @Version 1.0
 */
@Data
public class UpdateMenuDto implements Serializable {

    private static final long serialVersionUID = 1L;

    @NotNull(message = "菜单ID不能为空")
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("上级Id")
    private String parentId = "0";

    @ApiModelProperty("组件名（路由名称） --  与vue代码组件名必须一直 才能做到缓存页面 相关联")
    private String name;

    @NotNull(message = "菜单名称不能为空!")
    @ApiModelProperty("菜单名")
    private String title;

    @NotNull(message = "菜单编码不能为空!")
    @ApiModelProperty("菜单编号")
    private String code;

    @ApiModelProperty("菜单图标")
    private String icon;


    @ApiModelProperty("组件地址")
    private String component;


    private Integer componentType;

    @NotNull(message = "组件类型不能为空!")
    @ApiModelProperty("组件类型")
    private Integer menuType;

    @ApiModelProperty("菜单显示或者隐藏")
    private Integer display;

    @ApiModelProperty("是否允许修改")
    private Integer allowModify;

    @ApiModelProperty("是否允许删除")
    private Integer allowDelete;

    @ApiModelProperty("是否外链")
    private Integer outLink;

    @ApiModelProperty("外链地址")
    private String iframeSrc;

    @ApiModelProperty("页面持久化")
    private Integer keepAlive;

    @ApiModelProperty("排序码")
    private Integer sortCode;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("启用状态")
    private Integer enabledMark;

    @ApiModelProperty("系统主键")
    private Long systemId = 1L;

    @ApiModelProperty("按钮列表")
    private List<AddMenuButtonDto> buttonList;

    @ApiModelProperty("列表字段列表")
    private List<UpdateMenuColumnDto> columnList;

    @ApiModelProperty("表单字段列表")
    private List<UpdateMenuFormDto> formList;

    private String config;
    private String path;
    private String clientType;


}
