package com.xjrsoft.module.system.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * @title: UpdateMenuButtonDto
 * <AUTHOR>
 * @Date: 2022/4/4 19:09
 * @Version 1.0
 */
@Data
public class UpdateMenuButtonDto implements Serializable {

    private static final long serialVersionUID = 1L;

    @NotNull(message = "按钮id不能为空")
    private String id;

    @NotNull(message = "按钮名称不能为空!")
    @Length(min = 1,max = 20,message = "按钮不能大于20个字符！")
    @ApiModelProperty("按钮名")
    private String name;

    @NotNull(message = "菜单id不能为空!")
    @ApiModelProperty("菜单id")
    private String menuId;

    @Length(max = 50,message = "图标不能大于20个字符！")
    @ApiModelProperty("图标")
    private String icon;

    @NotNull(message = "按钮编码不能为空!")
    @Length(max = 20,message = "按钮编码大于20个字符！")
    @ApiModelProperty("编码")
    private String code;

    @Length(max = 200,message = "请求地址不能大于20个字符！")
    @ApiModelProperty("请求地址")
    private String url;

    @NotNull(message = "请求方式不能为空!")
    @ApiModelProperty("请求方式")
    private Integer method;

    @ApiModelProperty("列表id")
    private String listId;

}
