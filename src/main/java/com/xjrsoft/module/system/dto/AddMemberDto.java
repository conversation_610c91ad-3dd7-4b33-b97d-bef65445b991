package com.xjrsoft.module.system.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @Author: tzx
 * @Date: 2023/2/21 14:47
 */
@Data
public class AddMemberDto {

    @ApiModelProperty("签章id")
    @NotNull(message = "签章id不能为空")
    private Long id;

    @ApiModelProperty("成员id/维护人员")
    private List<Long> userIds;
}
