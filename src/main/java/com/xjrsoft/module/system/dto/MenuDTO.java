package com.xjrsoft.module.system.dto;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xjrsoft.common.model.base.AuditEntity;
import com.xjrsoft.common.utils.MySpringUtil;
import com.xjrsoft.module.system.entity.Menu;
import com.xjrsoft.module.system.service.IMenuService;
import com.xjrsoft.module.zy.form.pojo.entity.ZyFormListMenu;
import com.xjrsoft.module.zy.form.service.ZyFormListMenuService;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <p>
 * 用户
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-16
 */
@TableName("xjr_menu")
@ApiModel(value = "Menu对象", description = "用户")
@EqualsAndHashCode(callSuper = false)
public class MenuDTO extends AuditEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    private String id;

    @ApiModelProperty("上级Id")
    private String parentId;
    private String parentName;

    @ApiModelProperty("组件名（路由名称） --  与vue代码组件名必须一直 才能做到缓存页面 相关联")
    private String name;

    @ApiModelProperty("菜单名")
    private String title;

    @ApiModelProperty("菜单编号")
    private String code;

    @ApiModelProperty("菜单图标")
    private String icon;

    @ApiModelProperty("地址")
    private String path;

    @ApiModelProperty("组件地址")
    private String component;

    @ApiModelProperty("外链地址")
    private String iframeSrc;

    @ApiModelProperty("组件类型 默认组件 0 普通需要注册的组件 1 自定义表单 桌面设计 等已经默认注册进来的组件 ")
    private Integer componentType = 0;

    @ApiModelProperty("组件类型")
    private Integer menuType;

    @ApiModelProperty("菜单显示或者隐藏")
    private Integer display;

    @ApiModelProperty("是否允许修改")
    private Integer allowModify;

    @ApiModelProperty("是否允许删除")
    private Integer allowDelete;

    @ApiModelProperty("是否外链")
    private Integer outLink;

    @ApiModelProperty("页面持久化")
    private Integer keepAlive;

    @ApiModelProperty("排序码")
    private Integer sortCode;

    @ApiModelProperty("排序码")
    private String remark;

    @ApiModelProperty("系统主键（主系统默认为0）")
    private Long systemId;

    @ApiModelProperty("关联表单id（自定义表单 以及 代码生成器 生成的菜单才会有关联。）")
    private Long formId;

    @ApiModelProperty("关联表单设计id")
    private String zyFormListMenuId;

    @ApiModelProperty("菜单额外配置")
    private String config;
    private String clientType;


    private ZyFormListMenu menu;


    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getParentId() {
        return parentId;
    }

    public void setParentId(String parentId) {
        this.parentId = parentId;
        if (StrUtil.isNotBlank(parentId)) {
            Menu paramMenu = MySpringUtil.getBean(IMenuService.class).getById(parentId);
            if (paramMenu == null) {
                return;
            }
            setParentName(paramMenu.getName());
        }
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    public String getPath() {
        return path;
    }

    public void setPath(String path) {
        this.path = path;
    }

    public String getComponent() {
        return component;
    }

    public void setComponent(String component) {
        this.component = component;
    }

    public String getIframeSrc() {
        return iframeSrc;
    }

    public void setIframeSrc(String iframeSrc) {
        this.iframeSrc = iframeSrc;
    }

    public Integer getComponentType() {
        return componentType;
    }

    public void setComponentType(Integer componentType) {
        this.componentType = componentType;
    }

    public Integer getMenuType() {
        return menuType;
    }

    public void setMenuType(Integer menuType) {
        this.menuType = menuType;
    }

    public Integer getDisplay() {
        return display;
    }

    public void setDisplay(Integer display) {
        this.display = display;
    }

    public Integer getAllowModify() {
        return allowModify;
    }

    public void setAllowModify(Integer allowModify) {
        this.allowModify = allowModify;
    }

    public Integer getAllowDelete() {
        return allowDelete;
    }

    public void setAllowDelete(Integer allowDelete) {
        this.allowDelete = allowDelete;
    }

    public Integer getOutLink() {
        return outLink;
    }

    public void setOutLink(Integer outLink) {
        this.outLink = outLink;
    }

    public Integer getKeepAlive() {
        return keepAlive;
    }

    public void setKeepAlive(Integer keepAlive) {
        this.keepAlive = keepAlive;
    }

    public Integer getSortCode() {
        return sortCode;
    }

    public void setSortCode(Integer sortCode) {
        this.sortCode = sortCode;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Long getSystemId() {
        return systemId;
    }

    public void setSystemId(Long systemId) {
        this.systemId = systemId;
    }

    public Long getFormId() {
        return formId;
    }

    public void setFormId(Long formId) {
        this.formId = formId;
    }

    public String getZyFormListMenuId() {
        return zyFormListMenuId;
    }

    public void setZyFormListMenuId(String zyFormListMenuId) {
        this.zyFormListMenuId = zyFormListMenuId;
        if (StrUtil.isNotEmpty(zyFormListMenuId)) {
            ZyFormListMenuService zyFormListMenuService = MySpringUtil.getBean(ZyFormListMenuService.class);
            ZyFormListMenu menu = zyFormListMenuService.getById(zyFormListMenuId);
            setMenu(menu);
        }
    }

    public ZyFormListMenu getMenu() {
        return menu;
    }

    public void setMenu(ZyFormListMenu menu) {
        this.menu = menu;
    }

    public String getParentName() {
        return parentName;
    }

    public void setParentName(String parentName) {
        this.parentName = parentName;
    }

    public String getConfig() {
        return config;
    }

    public void setConfig(String config) {
        this.config = config;
    }

    public String getClientType() {
        return clientType;
    }

    public void setClientType(String clientType) {
        this.clientType = clientType;
    }
}
