package com.xjrsoft.module.system.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * @Author: tzx
 * @Date: 2022/6/17 15:18
 */
@Data
public class UpdateAreaDto {

    @ApiModelProperty("主键")
    @NotNull
    private Long id;

    @ApiModelProperty("父级主键")
    private Long parentId;

    @ApiModelProperty("区域编码")
    @NotBlank(message = "区域编码不能为空!")
    private String code;

    @ApiModelProperty("区域名称")
    @NotBlank(message = "区域名称不能为空!")
    private String name;

    @ApiModelProperty("快速查询")
    private String quickQuery;

    @ApiModelProperty("简拼")
    private String simpleSpelling;

    @ApiModelProperty("层次")
    private Integer layer;

    @ApiModelProperty("排序码")
    private Integer sortCode;

    @ApiModelProperty("备注")
    private String remark;
}
