package com.xjrsoft.module.system.dto;

import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @Author: tzx
 * @Date:2022/3/30 11:40
 */
@Data
public class AddDictionaryItemDto {

    private String parentId;

    @NotNull(message = "数据字典项名称不能为空!")
    private String name;

    @NotNull(message = "数据字典项编码不能为空!")
    private String code;

    private Integer sortCode;

    private String remark;
}
