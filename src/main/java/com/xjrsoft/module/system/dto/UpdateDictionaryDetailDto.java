package com.xjrsoft.module.system.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @Author: tzx
 * @Date: 2022/3/30 14:59
 */
@Data
public class UpdateDictionaryDetailDto {

    @NotNull(message = "主键不能为空！")
    @ApiModelProperty("主键")
    private Long id;

    @NotNull(message = "名称不能为空！")
    @ApiModelProperty("名字")
    private String name;

    @ApiModelProperty("编码")
    private String code;

    @ApiModelProperty("字典项id")
    private Long itemId;

//    @NotNull(message = "数据值不能为空！")
    @ApiModelProperty("值")
    private String value;

    @ApiModelProperty("排序号")
    private Integer sortCode;

    @ApiModelProperty("备注")
    private String remark;

    private String url;
    private String hoverUrl;

}
