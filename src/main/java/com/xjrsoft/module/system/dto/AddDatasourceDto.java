package com.xjrsoft.module.system.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * @title: AddDatasouceDto
 * <AUTHOR>
 * @Date: 2022/4/4 18:48
 * @Version 1.0
 */
@Data
public class AddDatasourceDto implements Serializable {
    private static final long serialVersionUID = -726382958240986898L;

    @ApiModelProperty("编号")
    @NotNull(message = "编码不能为空！")
    private String code;

    @NotNull(message = "数据源名称不能为空！")
    @ApiModelProperty("名字")
    private String name;

    @NotNull(message = "数据库连接主键不能为空！")
    @ApiModelProperty("数据库主键")
    private Long databaselinkId;

    @NotNull(message = "sql不能为空！")
    @ApiModelProperty("sql语句")
    private String sqlScript;

    @Length(max = 255,message = "备注不能大于255个字符！")
    @ApiModelProperty("备注")
    private String remark;


}
