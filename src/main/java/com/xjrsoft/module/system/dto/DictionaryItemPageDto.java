package com.xjrsoft.module.system.dto;

import com.xjrsoft.common.page.PageInput;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.validator.constraints.Length;

/**
 * @Author: tzx
 * @Date:2022/3/17 15:54
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class DictionaryItemPageDto extends PageInput {

    @ApiModelProperty(value = "id")
    @Length(max = 20,message = "id")
    private String id;

    @ApiModelProperty(value = "数据字典项目名称")
    @Length(max = 20,message = "项目名长度不超过20个字符")
    private String name;

    @ApiModelProperty(value = "数据字典项目编码")
    private String code;
}
