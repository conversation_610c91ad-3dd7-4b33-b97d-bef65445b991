package com.xjrsoft.module.system.dto;

import lombok.Data;
import org.hibernate.validator.constraints.Length;

/**
 * @title: MenuTreeDto
 * <AUTHOR>
 * @Date: 2022/4/10 20:41
 * @Version 1.0
 */
@Data
public class MenuTreeDto {

    @Length(max = 20,message = "菜单名不能大于20个字符！")
    private String title;

    @Length(max = 20,message = "组件名称不能大于20个字符！")
    private String name;

    @Length(max = 10,message = "组件编码不能大于10个字符！")
    private String code;

    private Integer enabledMark;

    private String isRelease;
}
