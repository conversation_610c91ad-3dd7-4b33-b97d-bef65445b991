package com.xjrsoft.module.system.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;


/**
 * <AUTHOR>
 */
@Data
public class LoginDto  {

    @ApiModelProperty(value = "账号")
    @NotBlank(message = "账号不能为空！")
    private String userName;

    @ApiModelProperty(value = "密码")
    private String password;

    @ApiModelProperty(value = "设备类型-默认为PC，pc为0，app为1",required = false)
    private Integer deviceType;


    private boolean th3Login =false;

}
