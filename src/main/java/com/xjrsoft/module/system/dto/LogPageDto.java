package com.xjrsoft.module.system.dto;

import com.xjrsoft.common.page.PageInput;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import java.time.LocalDateTime;
import java.util.List;

/**
 * @title: LogPageDto
 * <AUTHOR>
 * @Date: 2022/4/4 19:29
 * @Version 1.0
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class LogPageDto extends PageInput {

    /**
     * 日志类型 分类Id 1-登陆2-访问3-操作4-异常
     */
    @Min(value = 1, message = "日志类型不能小于1")
    @Max(value = 4, message = "日志类型不能大于4")
    private Integer category = 0;

    /**
     * 查询类型 0 无 1 当前一天 2 当前一周 3 当前一个月
     */
    @Min(value = 0, message = "查询类型不能小于0")
    @Max(value = 3, message = "查询类型不能大于3")
    private Integer queryType = 0;

    /**
     * 起始创建时间
     */
    private LocalDateTime createTimeStart;

    /**
     * 结束创建时间
     */
    private LocalDateTime createTimeEnd;
}
