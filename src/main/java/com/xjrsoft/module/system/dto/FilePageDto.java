package com.xjrsoft.module.system.dto;

import com.xjrsoft.common.page.ListInput;
import com.xjrsoft.common.page.PageInput;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.validator.constraints.Length;

/**
 * @Author: tzx
 * @Date: 2022/7/28 9:45
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class FilePageDto extends PageInput {
    /**
     * 文件夹id
     */
    private String folderId;

    /**
     * 名称
     */
    @Length(max = 50,message = "文件名不能超过50个字符！")
    private String fileName;

    /**
     * 流程id
     */
    @Length(max = 64,message = "流程id 不能超过64个字符！")
    private String processId;
}
