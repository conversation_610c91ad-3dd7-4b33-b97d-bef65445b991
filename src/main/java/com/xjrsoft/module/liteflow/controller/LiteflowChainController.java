package com.xjrsoft.module.liteflow.controller;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.xjrsoft.common.constant.GlobalConstant;
import com.xjrsoft.common.model.result.R;
import com.xjrsoft.common.page.ConventPage;
import com.xjrsoft.common.page.PageOutput;
import com.xjrsoft.common.utils.VoToColumnUtil;
import com.xjrsoft.module.demo.dto.ChainPageDto;
import com.xjrsoft.module.demo.entity.Demo;
import com.xjrsoft.module.demo.vo.DemoPageVo;
import com.xjrsoft.module.liteflow.dto.AddChainDto;
import com.xjrsoft.module.liteflow.dto.ExecuteFlowDto;
import com.xjrsoft.module.liteflow.dto.UpdateChainDto;
import com.xjrsoft.module.liteflow.entity.LiteflowChain;
import com.xjrsoft.module.liteflow.service.ILiteflowChainService;
import com.xjrsoft.module.liteflow.vo.ChainPageVo;
import com.yomahub.liteflow.flow.FlowBus;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;
import org.springframework.stereotype.Controller;

import javax.validation.Valid;
import java.util.List;

/**
 * <p>
 * 规则表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-06
 */
@RestController
@RequestMapping(GlobalConstant.LITEFLOW_MODULE_PREFIX + "/chain")
@Api(value = GlobalConstant.LITEFLOW_MODULE_PREFIX + "/chain", tags = "规则引擎规则文件模块")
@AllArgsConstructor
public class LiteflowChainController {

    private ILiteflowChainService chainService;

    @GetMapping(value = "/list")
    @ApiOperation(value = "规则文件(不分页)")
    public R list(){
        return R.ok(chainService.list());
    }

    @GetMapping(value = "/page")
    @ApiOperation(value = "规则文件(分页)")
    public R page(@Valid ChainPageDto dto){

        LambdaQueryWrapper<LiteflowChain> queryWrapper = Wrappers.lambdaQuery(LiteflowChain.class)
                .like(StrUtil.isNotBlank(dto.getKeyword()), LiteflowChain::getChainName, dto.getKeyword())
                .or()
                .like(StrUtil.isNotBlank(dto.getKeyword()), LiteflowChain::getChainDesc, dto.getKeyword())
                .orderByDesc(LiteflowChain::getId)
                .select(LiteflowChain.class, x -> VoToColumnUtil.fieldsToColumns(ChainPageVo.class).contains(x.getProperty()));
        IPage<LiteflowChain> page = chainService.page(ConventPage.getPage(dto), queryWrapper);
        PageOutput<ChainPageVo> pageOutput = ConventPage.getPageOutput(page, ChainPageVo.class);
        return R.ok(pageOutput);
    }

    @GetMapping(value = "/info")
    @ApiOperation(value = "规则文件详情")
    public R info(@RequestParam Long id) {
        return R.ok(chainService.getById(id));
    }

    @PostMapping
    @ApiOperation(value = "新增规则")
    public R add(@Valid @RequestBody AddChainDto dto){
        return R.ok(chainService.addChain(dto));
    }

    @PutMapping
    @ApiOperation(value = "修改规则")
    public R update(@Valid @RequestBody UpdateChainDto dto){
        return R.ok(chainService.updateChain(dto));
    }

    @DeleteMapping
    @ApiOperation(value = "删除")
    public R delete(@Valid @RequestBody List<Long> ids) {

        return R.ok(chainService.deleteChain(ids));
    }

    @PostMapping("/execute")
    @ApiOperation(value = "执行规则接口")
    public R execute(@Valid @RequestBody ExecuteFlowDto dto){
        return R.ok(chainService.execute(dto));
    }
}
