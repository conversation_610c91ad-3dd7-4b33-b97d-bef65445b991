package com.xjrsoft.module.liteflow.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Map;

/**
 * @Author: tzx
 * @Date: 2023/4/7 9:12
 */
@Data
public class ExecuteFlowDto {

    @ApiModelProperty("规则表id")
    private Long id;

    @ApiModelProperty("规则name（规则id）")
    private String chainName;

    @ApiModelProperty("参数id")
    private Map<String,Object> params;
}
