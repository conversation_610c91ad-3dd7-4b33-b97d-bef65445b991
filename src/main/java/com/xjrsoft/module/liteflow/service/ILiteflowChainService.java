package com.xjrsoft.module.liteflow.service;

import com.xjrsoft.module.liteflow.dto.AddChainDto;
import com.xjrsoft.module.liteflow.dto.ExecuteFlowDto;
import com.xjrsoft.module.liteflow.dto.UpdateChainDto;
import com.xjrsoft.module.liteflow.entity.LiteflowChain;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 规则表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-06
 */
public interface ILiteflowChainService extends IService<LiteflowChain> {

    boolean addChain(AddChainDto dto);

    boolean updateChain(UpdateChainDto dto);


    boolean deleteChain(List<Long> ids);


    boolean execute(ExecuteFlowDto dto);
}
