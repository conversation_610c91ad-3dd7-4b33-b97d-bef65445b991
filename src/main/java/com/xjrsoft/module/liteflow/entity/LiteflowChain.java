package com.xjrsoft.module.liteflow.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>
 * 规则表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-06
 */
@Data
@TableName("xjr_liteflow_chain")
@ApiModel(value = "LiteflowChain对象", description = "规则表")
public class LiteflowChain implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    @ApiModelProperty("应用名")
    private String applicationName;

    @ApiModelProperty("规则名")
    private String chainName;

    @ApiModelProperty("备注")
    private String chainDesc;

    @ApiModelProperty("规则文件")
    private String elData;

    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;


}
