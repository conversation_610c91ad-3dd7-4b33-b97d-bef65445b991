package com.xjrsoft.module.liteflow.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>
 * 规则脚本表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-06
 */
@Data
@TableName("xjr_liteflow_script")
@ApiModel(value = "LiteflowScript对象", description = "规则脚本表")
public class LiteflowScript implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    @ApiModelProperty("应用名")
    private String applicationName;

    @ApiModelProperty("脚本id")
    private String scriptId;

    @ApiModelProperty("脚本名")
    private String scriptName;

    @ApiModelProperty("脚本内容")
    private String scriptData;

    @ApiModelProperty("脚本类型")
    private String scriptType;

    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;


}
