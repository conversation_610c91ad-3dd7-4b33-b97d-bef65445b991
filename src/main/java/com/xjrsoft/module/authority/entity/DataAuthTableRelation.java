package com.xjrsoft.module.authority.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>
 * 数据权限 与 表 关联关系表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-28
 */
@TableName("xjr_data_auth_table_relation")
@ApiModel(value = "DataAuthTableRelation对象", description = "数据权限 与 表 关联关系表")
@Data
public class DataAuthTableRelation implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    @ApiModelProperty("表名")
    private String tableName;

    @ApiModelProperty("数据权限id")
    private Long dataAuthId;


}
