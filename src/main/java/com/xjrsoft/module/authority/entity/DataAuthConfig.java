package com.xjrsoft.module.authority.entity;

import com.baomidou.mybatisplus.annotation.OrderBy;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>
 * 数据权限自定义配置详情表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-27
 */
@TableName("xjr_data_auth_config")
@ApiModel(value = "DataAuthConfig对象", description = "数据权限自定义配置详情表")
@Data
public class DataAuthConfig implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键")
    private Long id;

    @ApiModelProperty("数据权限表id")
    private Long dataAuthId;

    @ApiModelProperty("排序号")
    @OrderBy(asc = true)
    private Integer orderNumber;

    @ApiModelProperty("字段名")
    private String fieldName;

    @ApiModelProperty("条件")
    private Integer conditionType;

    @ApiModelProperty("字段类型")
    private Integer fieldType;

    @ApiModelProperty("根据字段类型区分 如果是 登陆人信息 默认不需要存储值")
    private String fieldValue;

}
