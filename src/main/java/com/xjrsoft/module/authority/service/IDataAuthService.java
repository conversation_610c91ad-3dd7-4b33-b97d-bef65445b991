package com.xjrsoft.module.authority.service;

import com.github.yulichang.base.MPJBaseService;
import com.xjrsoft.module.authority.dto.AddDataAuthDto;
import com.xjrsoft.module.authority.dto.UpdateDataAuthDto;
import com.xjrsoft.module.authority.entity.DataAuth;
import com.xjrsoft.module.authority.vo.AuthObjectVo;

import java.util.List;

/**
 * <p>
 * 数据权限表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-27
 */
public interface IDataAuthService extends MPJBaseService<DataAuth> {

    Boolean add(AddDataAuthDto dto);

    Boolean edit(UpdateDataAuthDto dto);

    List<AuthObjectVo> getAuthObjectsInfo(Long id);
}
