package com.xjrsoft.module.desktop.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: tzx
 * @Date: 2023/4/13 10:06
 */
@Data
public class AddDesktopSchemaDto {

    @ApiModelProperty("内容")
    private String jsonContent;

    @ApiModelProperty("编码")
    private String code;

    @ApiModelProperty("名称")
    private String name;

    @ApiModelProperty("菜单id")
    private String menuId;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("是否首页")
    private Integer isFirst;

    @ApiModelProperty("上级id")
    private String parentId = "0";

    @ApiModelProperty("是否菜单")
    private Integer isMenu;

    @ApiModelProperty("排序码")
    private Integer sortCode;

    @ApiModelProperty("图标")
    private String icon;

    @ApiModelProperty("子系统id")
    private Long systemId;


    @ApiModelProperty("翻译标记")
    private String lgMarkCode;

    @ApiModelProperty("背景图url")
    private String backgroundUrl;

    @ApiModelProperty("是否启用")
    private Integer enabledMark;
}
