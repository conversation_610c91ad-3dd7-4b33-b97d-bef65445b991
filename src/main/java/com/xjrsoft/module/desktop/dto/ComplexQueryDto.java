package com.xjrsoft.module.desktop.dto;

import com.xjrsoft.common.page.PageInput;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.util.Map;

/**
 * @Author: hnyyzy
 * @Date: 2023/9/19 10:06
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class ComplexQueryDto{

    @ApiModelProperty("表单id")
    @NotNull(message = "表单id")
    private Long formId;

    @ApiModelProperty("发布id")
    private Long releaseId;

}
