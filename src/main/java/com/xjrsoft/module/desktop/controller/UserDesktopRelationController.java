package com.xjrsoft.module.desktop.controller;

import cn.dev33.satoken.stp.StpUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.xjrsoft.common.constant.GlobalConstant;
import com.xjrsoft.common.model.result.R;
import com.xjrsoft.module.desktop.dto.UserDesktopRelationDto;
import com.xjrsoft.module.desktop.entity.UserDesktopRelation;
import com.xjrsoft.module.desktop.service.IUserDesktopRelationService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * <p>
 * 用户桌面设计首页关系表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-02
 */
@RestController
@RequestMapping(GlobalConstant.DESKTOP_MODULE_PREFIX + "/user-relation")
@Api(value = GlobalConstant.SYSTEM_MODULE_PREFIX + "/user-relation", tags = "用户和桌面设计关系模块接口")
@AllArgsConstructor
public class UserDesktopRelationController {

    private final IUserDesktopRelationService relationService;

    @PostMapping
    @ApiOperation(value = "设置用户首页")
    public R add(@Valid @RequestBody UserDesktopRelationDto dto) {
        String loginUserId = StpUtil.getLoginIdAsString();
        relationService.remove(Wrappers.lambdaQuery(UserDesktopRelation.class).eq(UserDesktopRelation::getUserId, loginUserId));
        UserDesktopRelation relation = new UserDesktopRelation();
        relation.setDesktopId(dto.getDesktopId());
        relation.setUserId(loginUserId);
        return R.ok(relationService.save(relation));
    }
}
