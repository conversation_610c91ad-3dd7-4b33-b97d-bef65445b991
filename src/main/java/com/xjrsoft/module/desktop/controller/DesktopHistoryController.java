package com.xjrsoft.module.desktop.controller;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.yulichang.toolkit.MPJWrappers;
import com.xjrsoft.common.constant.GlobalConstant;
import com.xjrsoft.common.model.result.R;
import com.xjrsoft.common.page.ConventPage;
import com.xjrsoft.common.page.PageOutput;
import com.xjrsoft.common.utils.VoToColumnUtil;
import com.xjrsoft.module.desktop.dto.DesktopHistoryPageDto;
import com.xjrsoft.module.desktop.dto.DesktopSchemaPageDto;
import com.xjrsoft.module.desktop.dto.SetActivityDto;
import com.xjrsoft.module.desktop.entity.DesktopHistory;
import com.xjrsoft.module.desktop.entity.DesktopSchema;
import com.xjrsoft.module.desktop.service.IDesktopHistoryService;
import com.xjrsoft.module.desktop.vo.DesktopHistoryPageVo;
import com.xjrsoft.module.desktop.vo.DesktopSchemaPageVo;
import com.xjrsoft.module.form.entity.FormTemplate;
import com.xjrsoft.module.form.vo.FormTemplatePageVo;
import com.xjrsoft.module.organization.entity.User;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;
import org.springframework.stereotype.Controller;

import javax.validation.Valid;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-13
 */
@RestController
@RequestMapping(GlobalConstant.DESKTOP_MODULE_PREFIX + "/history")
@Api(value = GlobalConstant.SYSTEM_MODULE_PREFIX + "/history", tags = "桌面设计历史记录接口")
@AllArgsConstructor
public class DesktopHistoryController {

    private final IDesktopHistoryService desktopHistoryService;

    @GetMapping(value = "/page")
    @ApiOperation(value = "桌面设计历史记录列表(分页)")
    public R page(@Valid DesktopHistoryPageDto dto) {

        IPage<DesktopHistoryPageVo> desktopHistoryPageVoIPage = desktopHistoryService.selectJoinListPage(ConventPage.getPage(dto), DesktopHistoryPageVo.class,
                MPJWrappers.<DesktopHistory>lambdaJoin().disableSubLogicDel()
                        .orderByDesc(DesktopHistory::getId)
                        .select(DesktopHistory::getId)
                        .selectAs(User::getName, DesktopHistoryPageVo::getCreateUserName)
                        .eq(ObjectUtil.isNotEmpty(dto.getSchemaId()), DesktopHistory::getSchemaId, dto.getSchemaId())
                        .eq(StrUtil.isNotBlank(dto.getKeyword()), DesktopHistory::getSchemaId, dto.getSchemaId())
                        .select(DesktopHistory.class, x -> VoToColumnUtil.fieldsToColumns(DesktopHistoryPageVo.class).contains(x.getProperty()))
                        .leftJoin(User.class, User::getId, DesktopHistory::getCreateUserId)
        );
        PageOutput<DesktopHistoryPageVo> pageOutput = ConventPage.getPageOutput(desktopHistoryPageVoIPage);
//        LambdaQueryWrapper<DesktopHistory> queryWrapper = new LambdaQueryWrapper<>();
//        queryWrapper
//                .eq(ObjectUtil.isNotEmpty(dto.getSchemaId()), DesktopHistory::getSchemaId, dto.getSchemaId())
//                .eq(StrUtil.isNotBlank(dto.getKeyword()), DesktopHistory::getSchemaId, dto.getSchemaId());
//        IPage<DesktopHistory> page = desktopHistoryService.page(ConventPage.getPage(dto), queryWrapper);
//        PageOutput<DesktopHistory> pageOutput = ConventPage.getPageOutput(page, DesktopHistory.class);

        return R.ok(pageOutput);
    }

    @GetMapping(value = "/info")
    @ApiOperation(value = "桌面设计历史记录详情")
    public R info(@RequestParam Long id) {
        return R.ok(desktopHistoryService.getById(id));
    }

    @PutMapping("/set-activity")
    @ApiOperation(value = "设置为活动版本")
    public R setActivity(@Valid @RequestBody SetActivityDto dto) {
        return R.ok(desktopHistoryService.setActivity(dto));
    }

}
