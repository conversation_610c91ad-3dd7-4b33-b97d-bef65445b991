package com.xjrsoft.module.desktop.service;

import com.xjrsoft.module.desktop.dto.*;
import com.xjrsoft.module.desktop.entity.DesktopSchema;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * <p>
 * 桌面设计历史记录 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-13
 */
public interface IDesktopSchemaService extends IService<DesktopSchema> {

    boolean add(AddDesktopSchemaDto dto);

    boolean update(UpdateDesktopSchemaDto dto);

    boolean delete(List<Long> ids);

    boolean copy(CopyDesktopSchemaDto dto);

    boolean setFirst(SetFirstDto dto);

    boolean importDesktopSchema(MultipartFile multipartFile);

}
