package com.xjrsoft.module.desktop.service.impl;

import com.github.yulichang.base.MPJBaseServiceImpl;
import com.xjrsoft.common.enums.YesOrNoEnum;
import com.xjrsoft.module.desktop.dto.SetActivityDto;
import com.xjrsoft.module.desktop.entity.DesktopHistory;
import com.xjrsoft.module.desktop.entity.DesktopSchema;
import com.xjrsoft.module.desktop.mapper.DesktopHistoryMapper;
import com.xjrsoft.module.desktop.mapper.DesktopSchemaMapper;
import com.xjrsoft.module.desktop.service.IDesktopHistoryService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-13
 */
@Service
@AllArgsConstructor
public class DesktopHistoryServiceImpl extends MPJBaseServiceImpl<DesktopHistoryMapper, DesktopHistory> implements IDesktopHistoryService {

    private final DesktopSchemaMapper desktopSchemaMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean setActivity(SetActivityDto dto) {
        DesktopHistory history = getById(dto.getId());
        history.setActivityFlag(YesOrNoEnum.YES.getCode());

        DesktopSchema desktopSchema = desktopSchemaMapper.selectById(history.getSchemaId());

        desktopSchema.setJsonContent(history.getJsonContent());

        updateById(history);

        desktopSchemaMapper.updateById(desktopSchema);
        return true;
    }
}
