package com.xjrsoft.module.desktop.vo;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: tzx
 * @Date: 2023/4/13 9:55
 */
@Data
public class DesktopSchemaPageVo {

    @ApiModelProperty("主键")
    private Long id;

    @ApiModelProperty("编码")
    private String code;

    @ApiModelProperty("名称")
    private String name;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("是否首页")
    private Integer isFirst;

    @ApiModelProperty("排序码")
    private Integer sortCode;

    @ApiModelProperty("背景图url")
    private String backgroundUrl;

    @ApiModelProperty("是否启用")
    private Integer enabledMark;
}
