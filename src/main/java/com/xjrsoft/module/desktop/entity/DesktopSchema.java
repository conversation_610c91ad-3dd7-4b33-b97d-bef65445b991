package com.xjrsoft.module.desktop.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.xjrsoft.common.model.base.AuditEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <p>
 * 桌面设计历史记录
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-13
 */

@TableName("xjr_desktop_schema")
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "DesktopSchema对象", description = "桌面设计模板表")
public class DesktopSchema extends AuditEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键")
    private Long id;

    @ApiModelProperty("内容")
    private String jsonContent;

    @ApiModelProperty("编码")
    private String code;

    @ApiModelProperty("名称")
    private String name;

    @ApiModelProperty("菜单id")
    private String menuId;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("是否首页")
    private Integer isFirst;

    @ApiModelProperty("是否菜单")
    private Integer isMenu;

    @ApiModelProperty("排序码")
    private Integer sortCode;

    @ApiModelProperty("子系统id")
    private Long systemId;

    @ApiModelProperty("翻译标记")
    private String lgMarkCode;

    @ApiModelProperty("背景图url")
    private String backgroundUrl;

    @ApiModelProperty("上级id")
    private String parentId;


}
