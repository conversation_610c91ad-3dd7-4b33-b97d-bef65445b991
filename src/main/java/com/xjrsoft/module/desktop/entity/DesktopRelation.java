package com.xjrsoft.module.desktop.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * 桌面设计授权表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-01
 */
@TableName("xjr_desktop_relation")
@ApiModel(value = "DesktopRelation对象", description = "桌面设计授权表")
@Data
public class DesktopRelation implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键")
    private Long id;

    @ApiModelProperty("角色主键")
    private Long roleId;

    @ApiModelProperty("桌面设计主键")
    private Long desktopId;
}
