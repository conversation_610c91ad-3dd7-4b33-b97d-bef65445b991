package com.xjrsoft.module.desktop.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * 用户桌面设计首页关系表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-02
 */
@TableName("xjr_user_desktop_relation")
@ApiModel(value = "UserDesktopRelation对象", description = "用户桌面设计首页关系表")
@Data
public class UserDesktopRelation implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键")
    private Long id;

    @ApiModelProperty("用户主键")
    private String userId;

    @ApiModelProperty("桌面设计主键")
    private Long desktopId;
}
