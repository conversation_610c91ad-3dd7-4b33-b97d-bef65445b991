package com.xjrsoft.module.desktop.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;

import com.xjrsoft.common.model.base.AuditEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-13
 */
@TableName("xjr_desktop_history")
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "DesktopHistory对象", description = "桌面设计历史记录表")
public class DesktopHistory extends AuditEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    @ApiModelProperty("桌面设计模板id")
    private Long schemaId;

    @ApiModelProperty("内容")
    private String jsonContent;

    @ApiModelProperty("是否当前版本")
    private Integer activityFlag;

}
