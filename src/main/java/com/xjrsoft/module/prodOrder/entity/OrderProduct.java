package com.xjrsoft.module.prodOrder.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-14
 */
@Data
@TableName("t_order_product")
@ApiModel(value = "OrderProduct对象", description = "")
public class OrderProduct implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId("ID")
    private String id;

    /**
     * 订单主键
     */
    @TableField("OrderID")
    private String orderID;

    /**
     * 商品主键
     */
    @TableField("ProductID")
    private String productID;

    /**
     * 商品数量
     */
    @TableField("ProductAmount")
    private BigDecimal productAmount;

    /**
     * 商品合计
     */
    @TableField("ProductPriceCount")
    private BigDecimal productPriceCount;

}
