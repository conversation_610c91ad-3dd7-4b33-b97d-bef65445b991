package com.xjrsoft.module.prodOrder.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-14
 */
@Data
@TableName("t_order")
@ApiModel(value = "Order对象", description = "")
public class Order implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 订单主键
     */
    @TableId("OrderID")
    private String orderID;

    /**
     *  订单编号
     */
    @TableField("OrderNum")
    private String orderNum;

    /**
     * 订单名称
     */
    @TableField("OrderName")
    private String orderName;

    /**
     * 订单负责人
     */
    @TableField("OrderManager")
    private String orderManager;

    /**
     * 添加时间
     */
    @TableField("OrderAddTime")
    private LocalDateTime orderAddTime;

    /**
     * 修改时间
     */
    @TableField("OrderModifyTime")
    private LocalDateTime orderModifyTime;

    /**
     *
     */
    @TableField("OrderPriceCount")
    private BigDecimal orderPriceCount;

}
