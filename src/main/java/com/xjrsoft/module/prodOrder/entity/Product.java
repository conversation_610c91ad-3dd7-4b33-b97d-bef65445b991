package com.xjrsoft.module.prodOrder.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-14
 */
@Data
@TableName("t_product")
@ApiModel(value = "Product对象", description = "")
public class Product implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 商品主键
     */
    @TableId("ProductID")
    private String productID;

    /**
     * 商品编号
     */
    @TableField("ProductNum")
    private String productNum;

    /**
     * 商品名称
     */
    @TableField("ProductName")
    private String productName;

    /**
     * 商品单价
     */
    @TableField("ProductUnitPrice")
    private BigDecimal productUnitPrice;

    /**
     *添加时间
     */
    @TableField("AddTime")
    private LocalDateTime addTime;

}
