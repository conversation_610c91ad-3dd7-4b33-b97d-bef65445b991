package com.xjrsoft.module.prodOrder.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class AddOrderDto {

    /**
     *  订单编号
     */
    @JsonProperty("OrderNum")
    private String orderNum;

    /**
     * 订单名称
     */
    @JsonProperty("OrderName")
    private String orderName;

    /**
     * 订单负责人
     */
    @JsonProperty("OrderManager")
    private String orderManager;

    /**
     *
     */
    @JsonProperty("OrderPriceCount")
    private BigDecimal orderPriceCount;

    /**
     * 订单商品
     */
    @JsonProperty("OrderProduct")
    private List<OrderProductDto> products;
}
