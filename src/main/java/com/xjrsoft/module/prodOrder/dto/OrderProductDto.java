package com.xjrsoft.module.prodOrder.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class OrderProductDto {

    /**
     * 订单主键
     */
    @JsonProperty("OrderID")
    private String orderID;

    /**
     * 商品主键
     */
    @JsonProperty("ProductID")
    private String productID;

    /**
     * 商品数量
     */
    @JsonProperty("ProductAmount")
    private BigDecimal productAmount;

    /**
     * 商品合计
     */
    @JsonProperty("ProductPriceCount")
    private BigDecimal productPriceCount;
}
