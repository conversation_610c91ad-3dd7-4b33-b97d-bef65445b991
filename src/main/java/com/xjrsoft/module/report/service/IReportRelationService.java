package com.xjrsoft.module.report.service;

import com.xjrsoft.common.page.PageOutput;
import com.xjrsoft.module.report.dto.AddReportReleaseDto;
import com.xjrsoft.module.report.dto.ReportReleasePageDto;
import com.xjrsoft.module.report.dto.UpdateReportReleaseDto;
import com.xjrsoft.module.report.entity.ReportRelation;
import com.baomidou.mybatisplus.extension.service.IService;
import com.xjrsoft.module.report.vo.ReportReleaseInfoVo;
import com.xjrsoft.module.report.vo.ReportReleasePageVo;

/**
 * <p>
 * 报表菜单关联关系表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-23
 */
public interface IReportRelationService extends IService<ReportRelation> {

    /**
     * 获取表单发布信息 列表 分页
     * @param dto
     * @return
     */
    PageOutput<ReportReleasePageVo> releasePage(ReportReleasePageDto dto);

    /**
     * 报表发布 新增
     * @param dto
     */
    Boolean add(AddReportReleaseDto dto);

    /**
     * 报表发布 修改
     * @param dto
     */
    Boolean update(UpdateReportReleaseDto dto);

    ReportReleaseInfoVo info(Long id);
}
