package com.xjrsoft.module.report.service.impl;

import com.xjrsoft.module.report.entity.ProfessionalReport;
import com.xjrsoft.module.report.mapper.ProfessionalReportMapper;
import com.xjrsoft.module.report.service.IProfessionalReportService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 专业报表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-01
 */
@Service
@AllArgsConstructor
public class ProfessionalReportServiceImpl extends ServiceImpl<ProfessionalReportMapper, ProfessionalReport> implements IProfessionalReportService {

}
