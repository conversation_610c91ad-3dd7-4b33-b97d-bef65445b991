package com.xjrsoft.module.report.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * @Author: tzx
 * @Date: 2022/5/23 16:40
 */
@Data
public class AddReportReleaseDto {
    /**
     * 菜单编码
     */
    @ApiModelProperty("菜单编码")
    @NotBlank(message = "菜单编码不能为空！")
    private String code;
    /**
     * 菜单名
     */
    @ApiModelProperty("菜单名")
    @NotBlank(message = "菜单名不能为空！")
    private String title;

    /**
     * 报表类型，0-ureport,1-葡萄城数据
     */
    @NotNull(message = "报表类型不能为空")
    private Integer dataType;
    /**
     * 父级
     */
    private Long parentId = 0L;
    /**
     * 绑定表单
     */
    private Long reportId;
    /**
     * 排序
     */
    private Integer sortCode;
    /**
     * 图标
     */
    private String icon;
    /**
     * 系统id
     */
    private String systemId;
    /**
     * 备注
     */
    private String remark;
}
