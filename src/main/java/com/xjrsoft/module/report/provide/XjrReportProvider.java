package com.xjrsoft.module.report.provide;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.bstek.ureport.console.UReportServlet;
import com.bstek.ureport.provider.report.ReportFile;
import com.bstek.ureport.provider.report.ReportProvider;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.xjrsoft.module.report.entity.Report;
import com.xjrsoft.module.report.service.IReportService;
import lombok.AllArgsConstructor;
import lombok.Setter;
import lombok.SneakyThrows;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.web.servlet.ServletRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;

import javax.servlet.Servlet;
import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * @Author: tzx
 * @Date: 2022/5/23 9:13
 */
@Setter
@Component
public class XjrReportProvider implements ReportProvider {

    private final String PREFIX = "xjr:";

    @JsonIgnore
    @Autowired
    private IReportService reportService;


    @Override
    public InputStream loadReport(String fileName) {
        Report report = reportService.getOne(Wrappers.<Report>lambdaQuery().eq(Report::getName, getRealName(fileName)));
        byte[] content = report.getContent();
        return new ByteArrayInputStream(content);
    }

    @Override
    public void deleteReport(String fileName) {
        reportService.remove(Wrappers.<Report>lambdaQuery().eq(Report::getName, getRealName(fileName)));
    }

    @Override
    public List<ReportFile> getReportFiles() {
        List<Report> list = reportService.list();
        List<ReportFile> reportList = new ArrayList<>();
        for (Report report : list) {
            reportList.add(new ReportFile(report.getName(), Date.from(report.getModifyDate().atZone(ZoneId.systemDefault()).toInstant())));
        }
        return reportList;
    }

    @Override
    public void saveReport(String fileName, String fileContent) {
        Report report = reportService.getOne(Wrappers.<Report>lambdaQuery().eq(Report::getName, getRealName(fileName)));
        if (report == null) {
            report = new Report();
            report.setName(getRealName(fileName));
            report.setContent(fileContent.getBytes(StandardCharsets.UTF_8));
            report.setModifyDate(LocalDateTime.now());

            reportService.save(report);
        }
        else {
            report.setName(getRealName(fileName));
            report.setContent(fileContent.getBytes(StandardCharsets.UTF_8));
            report.setModifyDate(LocalDateTime.now());
            reportService.updateById(report);
        }
    }

    @Override
    public String getName() {
        return "本地数据库";
    }

    @Override
    public boolean disabled() {
        return false;
    }

    @Override
    public String getPrefix() {
        return PREFIX;
    }


    /**
     * 获取真实名称  无前缀
     * @param name
     * @return
     */
    private String getRealName(String name) {
        if (name.startsWith(PREFIX)) {
            name = name.substring(PREFIX.length());
        }
        return name;
    }
}
