package com.xjrsoft.module.report.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/11/1 11:20
 */
@Data
public class ProfessionalReportDicTreeVo {

    /**
     * 报表类型id
     */
    @ApiModelProperty("报表类型id")
    private Long id;

    /**
     * 报表类型名称
     */
    @ApiModelProperty("报表类型名称")
    private String typeName;

    /**
     * 报表类型数据
     */
    @ApiModelProperty("报表类型数据")
    private List<ProfessionalReportVo> professionalReportVoList;


    public List<ProfessionalReportVo> get(){
        if (this.professionalReportVoList == null) {
            this.professionalReportVoList = new ArrayList();
        }
        return this.professionalReportVoList;
    }
}
