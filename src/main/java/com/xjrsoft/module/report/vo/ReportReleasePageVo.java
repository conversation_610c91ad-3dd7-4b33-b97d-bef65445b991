package com.xjrsoft.module.report.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: tzx
 * @Date: 2022/5/23 16:06
 */
@Data
public class ReportReleasePageVo {

    @ApiModelProperty("主键值")
    private Long id;

    @ApiModelProperty("菜单Id")
    private String menuId;

    @ApiModelProperty("菜单名")
    private String title;

    @ApiModelProperty("菜单编码")
    private String code;

    @ApiModelProperty("菜单上级Id")
    private String parentId;

    @ApiModelProperty("菜单上级名称")
    private String parentName;

    @ApiModelProperty("报表Id")
    private Long reportId;

    @ApiModelProperty("报表名")
    private String reportName;

    @ApiModelProperty("备足")
    private String remark;
}
