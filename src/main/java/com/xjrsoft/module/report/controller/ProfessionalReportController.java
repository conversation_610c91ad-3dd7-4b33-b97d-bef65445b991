package com.xjrsoft.module.report.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.xjrsoft.common.annotation.XjrLog;
import com.xjrsoft.common.constant.GlobalConstant;
import com.xjrsoft.common.model.result.R;
import com.xjrsoft.module.report.dto.AddProfessionalReportDto;
import com.xjrsoft.module.report.dto.ProfessionalReportPageDto;
import com.xjrsoft.module.report.dto.UpdateProfessionalReportDto;
import com.xjrsoft.module.report.entity.ProfessionalReport;
import com.xjrsoft.module.report.service.IProfessionalReportService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * <p>
 * 专业报表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-01
 */
@RestController
@RequestMapping(GlobalConstant.REPORT_MODULE_PREFIX + "/professionalReport")
@Api(value = GlobalConstant.REPORT_MODULE_PREFIX + "/professionalReport",tags = "专业报表模块")
@AllArgsConstructor
public class ProfessionalReportController {

    private final IProfessionalReportService professionalReportService;

    @GetMapping(value = "/page")
    @ApiOperation(value = "专业报表page接口")
    @XjrLog(value = "专业报表树")
    public R page(ProfessionalReportPageDto dto) {
        List<ProfessionalReport> list = professionalReportService.list(Wrappers.<ProfessionalReport>query().lambda()
                .like(StrUtil.isNotBlank(dto.getKeyword()),ProfessionalReport::getName,dto.getKeyword()).orderByAsc(ProfessionalReport::getSortCode));
        return R.ok(list);
    }

    @PostMapping
    @ApiOperation(value = "新增专业报表")
    public R add(@Valid @RequestBody AddProfessionalReportDto dto) {
        long count = professionalReportService.count(Wrappers.<ProfessionalReport>query().lambda().eq(ProfessionalReport::getName, dto.getName()));
        if (count > 0) {
            return R.error("此专业报表已经存在！");
        }
        ProfessionalReport professionalReport = BeanUtil.toBean(dto, ProfessionalReport.class);
        professionalReportService.save(professionalReport);
        return R.ok(true);
    }

    @PutMapping
    @ApiOperation(value = "修改专业报表")
    public R update(@Valid @RequestBody UpdateProfessionalReportDto dto) {
        ProfessionalReport professionalReport = BeanUtil.toBean(dto, ProfessionalReport.class);
        professionalReportService.updateById(professionalReport);
        return R.ok(true);
    }

    @GetMapping(value = "/info")
    @ApiOperation(value = "根据id查询专业报表信息")
    public R info(@RequestParam Long id) {
        ProfessionalReport professionalReport = professionalReportService.getById(id);
        if (professionalReport == null) {
            R.error("找不到此报表！");
        }
        return R.ok(professionalReport);
    }

    @DeleteMapping
    @ApiOperation(value = "删除专业报表")
    @XjrLog(value = "删除专业报表")
    public R delete(@Valid @RequestBody Long id) {
        return R.ok(professionalReportService.removeById(id));
    }
}
