package com.xjrsoft.module.report.controller;

import com.xjrsoft.common.constant.GlobalConstant;
import com.xjrsoft.common.model.result.R;
import com.xjrsoft.module.report.dto.AddReportReleaseDto;
import com.xjrsoft.module.report.dto.ReportReleasePageDto;
import com.xjrsoft.module.report.dto.UpdateReportReleaseDto;
import com.xjrsoft.module.report.service.IReportRelationService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * <p>
 * 报表菜单关联关系表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-23
 */
@RestController
@RequestMapping(GlobalConstant.REPORT_MODULE_PREFIX + "/release")
@Api(value = GlobalConstant.REPORT_MODULE_PREFIX + "/release",tags = "报表发布模块")
@AllArgsConstructor
public class ReportReleaseController {

    private final IReportRelationService reportRelationService;

    @GetMapping(value = "/page")
    @ApiOperation(value="报表发布列表(分页)")
    public R page(@Valid ReportReleasePageDto dto) {
        return R.ok(reportRelationService.releasePage(dto));
    }

    @GetMapping(value = "/info")
    @ApiOperation(value="报表发布详情")
    public R info(@RequestParam Long id) {
        return R.ok(reportRelationService.info(id));
    }

    @PostMapping
    @ApiOperation(value="新增(分页)")
    public R add(@Valid @RequestBody AddReportReleaseDto dto) {
        return R.ok(reportRelationService.add(dto));
    }

    @PutMapping
    @ApiOperation(value="修改(分页)")
    public R update(@Valid @RequestBody UpdateReportReleaseDto dto) {
        return R.ok(reportRelationService.update(dto));
    }

    @DeleteMapping
    @ApiOperation(value = "删除")
    public R delete(@Valid @RequestBody List<Long> ids){
        return R.ok(reportRelationService.removeBatchByIds(ids));
    }

}
