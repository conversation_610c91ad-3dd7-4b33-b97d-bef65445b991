package com.xjrsoft.module.report.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;

import com.xjrsoft.common.model.base.AuditEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 专业报表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-01
 */
@TableName("xjr_professional_report")
@ApiModel(value = "ProfessionalReport对象", description = "专业报表")
@Data
@EqualsAndHashCode(callSuper = false)
public class ProfessionalReport extends AuditEntity implements Serializable {

    private static final long serialVersionUID = 1L;

      @ApiModelProperty("主键值")
        private Long id;

      @ApiModelProperty("名称")
      private String name;

      @ApiModelProperty("编码")
      private String code;

      @ApiModelProperty("报表类型id")
      private Long typeId;

      @ApiModelProperty("报表类型值")
      private String typeValue;

      @ApiModelProperty("文件json")
      private String fileJson;

      @ApiModelProperty("备注")
      private String remark;

      @ApiModelProperty("排序")
      private Integer sortCode;
}
