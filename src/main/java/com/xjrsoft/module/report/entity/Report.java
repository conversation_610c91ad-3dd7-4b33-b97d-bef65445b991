package com.xjrsoft.module.report.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.sql.Blob;
import java.time.LocalDateTime;

import com.xjrsoft.common.model.base.AuditEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-23
 */
@TableName("xjr_report")
@ApiModel(value = "Report对象", description = "报表")
@Data
@EqualsAndHashCode(callSuper = false)
public class Report extends AuditEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键值")
    private Long id;

    @ApiModelProperty("名称")
    private String name;

    @ApiModelProperty("编码")
    private String code;

    @ApiModelProperty("内容")
    private byte[] content;

    @ApiModelProperty("排序码")
    private String remark;

    @ApiModelProperty("排序码")
    private Integer sortCode;


}
