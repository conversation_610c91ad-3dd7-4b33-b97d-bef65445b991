package com.xjrsoft.module.magicapi.vo;

import com.xjrsoft.common.model.tree.ITreeNode;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class MagicApiGroupTreeVo implements Serializable, ITreeNode<MagicApiGroupTreeVo,String> {

    /**
     * 上级Id
     */
    private String id;

    /**
     * 名称
     */
    private String name;
    /**
     * 上级Id
     */
    private String parentId;


    /**
     * 子集
     */
    private List<MagicApiGroupTreeVo> children;





}
