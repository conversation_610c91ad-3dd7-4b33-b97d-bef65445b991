package com.xjrsoft.module.magicapi.vo;

import com.xjrsoft.common.model.tree.ITreeNode;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class MagicApiTreeVo implements Serializable, ITreeNode<MagicApiTreeVo,String> {

    /**
     * 上级Id
     */
    private String id;

    /**
     * 名称
     */
    private String name;
    /**
     * 上级Id
     */
    private String parentId;

    /**
     * 类型
     */
    private String type;


    /**
     * 地址
     */
    private String path;


    /**
     * 请求方式
     */
    private String method;

    /**
     * 子集
     */
    private List<MagicApiTreeVo> children;




}
