package com.xjrsoft.module.magicapi.service;

import com.xjrsoft.common.pojo.CommonResult;
import com.xjrsoft.module.magicapi.vo.MagicApiGroupTreeVo;
import com.xjrsoft.module.magicapi.vo.MagicApiInfoVo;
import com.xjrsoft.module.magicapi.vo.MagicApiTreeVo;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

public interface IMagicApiService {

    /**
     * 分组以及api 树结构
     *
     * @return
     */
    List<MagicApiTreeVo> tree(String keyword);

    /**
     * 分组树
     *
     * @return
     */
    List<MagicApiGroupTreeVo> groupTree();

    /**
     * 根据 id 获取 接口信息
     *
     * @param id
     * @return
     */
    MagicApiInfoVo info(String id);

    /**
     * 根据分组查询所有接口
     *
     * @param groupId
     * @return
     */
    List<MagicApiInfoVo> list(String groupId);

    /**
     * 执行API
     *
     * @param id  接口id
     * @param <T>
     * @return
     */
    <T> T executeApi(String id);

    public CommonResult<List<MagicApiInfoVo>> getByIds(@RequestParam String ids);
}
