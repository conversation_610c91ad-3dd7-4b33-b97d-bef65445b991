package com.xjrsoft.module.magicapi.service.impl;

import com.xjrsoft.module.magicapi.entity.InterfaceAuth;
import com.xjrsoft.module.magicapi.mapper.InterfaceAuthMapper;
import com.xjrsoft.module.magicapi.service.IInterfaceAuthService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 接口权限表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-03
 */
@Service
public class InterfaceAuthServiceImpl extends ServiceImpl<InterfaceAuthMapper, InterfaceAuth> implements IInterfaceAuthService {

    @Override
    public List<String> loadAuthInterfaceIdsOfUser(Long userId) {
        return null;
    }
}
