package com.xjrsoft.module.magicapi.controller;


import cn.hutool.core.bean.BeanUtil;
import com.xjrsoft.common.annotation.XjrLog;
import com.xjrsoft.common.constant.GlobalConstant;
import com.xjrsoft.common.model.result.R;
import com.xjrsoft.common.pojo.CommonResult;
import com.xjrsoft.module.magicapi.service.IMagicApiService;
import com.xjrsoft.module.magicapi.vo.MagicApiInfoVo;
import com.xjrsoft.module.magicapi.vo.MagicApiTreeVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.ssssssss.magicapi.core.model.ApiInfo;
import org.ssssssss.magicapi.core.model.BaseDefinition;
import org.ssssssss.magicapi.core.model.MagicEntity;

import java.util.List;

/**
 * <p>
 * magic-api模块接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-30
 */
@RestController
@RequestMapping(GlobalConstant.MAGICAPI_MODULE_PREFIX)
@Api(value = GlobalConstant.MAGICAPI_MODULE_PREFIX, tags = "magic-api模块接口")
@AllArgsConstructor
public class MagicApiController {

    private final IMagicApiService magicApiService;

    @GetMapping(value = "/tree")
    @ApiOperation("查询所有分组以及api 树结构")
    @XjrLog(value = "查询所有分组以及api 树结构")
    public R tree(@RequestParam(required = false) String keyword ) {
        List<MagicApiTreeVo> tree = magicApiService.tree(keyword);
        return R.ok(tree);
    }

    @GetMapping(value = "/group/tree")
    @ApiOperation("查询所有树结构分组")
    @XjrLog(value = "查询所有树结构分组")
    public R groupTree(){
        return R.ok(magicApiService.groupTree());
    }

    @GetMapping(value = "/info")
    @ApiOperation("查询api详情")
    @XjrLog(value = "查询api详情")
    public R info(@RequestParam String id){
        return R.ok(magicApiService.info(id));
    }

    @GetMapping(value = "/list")
    @ApiOperation("根据分组id 查询所有api")
    @XjrLog(value = "根据分组id 查询所有api")
    public R list(@RequestParam String groupId){
        return R.ok(magicApiService.list(groupId));
    }

    @GetMapping(value = "/getByIds")
    @ApiOperation("根据id获取详细数据,多个逗号分割")
    public CommonResult<List<MagicApiInfoVo>> getByIds(@RequestParam String ids){
        return magicApiService.getByIds(ids);
    }

}
