package com.xjrsoft.module.magicapi.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * 接口权限表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-03
 */
@Data
@TableName("xjr_interface_auth")
@ApiModel(value = "InterfaceAuth对象", description = "接口权限表")
public class InterfaceAuth implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键")
    private Long id;

    @ApiModelProperty("角色主键")
    private Long roleId;

    @ApiModelProperty("接口主键")
    private String interfaceId;
}
