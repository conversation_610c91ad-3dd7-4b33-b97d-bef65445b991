package com.xjrsoft.module.zy.form.pojo.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.fhs.core.trans.vo.TransPojo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class ZyFormFormulaDO  implements TransPojo {


    @ApiModelProperty(value = "")
    private String id;

    @ApiModelProperty(value = "")
    private Date createDate;

    @ApiModelProperty(value = "")
    private String remarks;

    /**
     * 单据Id
     */
    @ApiModelProperty(value = "单据Id")
    private String schemeId;

    /**
     * 公式名称
     */
    @ApiModelProperty(value = "公式名称")
    private String name;

    /**
     * 公式类型
     */
    @ApiModelProperty(value = "公式类型")
    @Trans(type = TransType.DICTIONARY,key = "GS10000")
    private String type;

    /**
     * 处理类型
     */
    @ApiModelProperty(value = "处理类型")
    private String handle;

    /**
     * 说明
     */
    @ApiModelProperty(value = "说明")
    private String description;

    /**
     * 存储过程名字
     */
    @ApiModelProperty(value = "存储过程名字")
    private String procName;

    /**
     * 源表达式
     */
    @ApiModelProperty(value = "源表达式")
    private String expressionSource;

    /**
     * 编译后表达式
     */
    @ApiModelProperty(value = "编译后表达式")
    private String expressionCompile;

    /**
     * 提示信息
     */
    @ApiModelProperty(value = "提示信息")
    private String message;

    /**
     * 插入子表的名字
     */
    @ApiModelProperty(value = "插入子表的名字")
    private String subtable;

    /**
     * 数据源Id
     */
    @ApiModelProperty(value = "数据源Id")
    private String datasourceid;

    /**
     * 执行脚本
     */
    @ApiModelProperty(value = "执行脚本")
    private String script;

    /**
     * 扩展参数(json形式)
     */
    @ApiModelProperty(value = "扩展参数(json形式)")
    private String extParam;

    /**
     * 用户权限
     */
    @ApiModelProperty(value = "用户权限")
    private String users;

    /**
     * 单位权限
     */
    @ApiModelProperty(value = "单位权限")
    private String offices;

    /**
     * 角色权限
     */
    @ApiModelProperty(value = "角色权限")
    private String roles;

    /**
     * 1:PC 2:移动端
     */
    @ApiModelProperty(value = "1:PC 2:移动端")
    private String platform;

    @TableField(value = "SOURCE_ID")
    @ApiModelProperty(value = "来源id")
    private String sourceId;

    @TableField(value = "IS_SYS")
    @ApiModelProperty(value = "0自定义公式1系统公司")
    private String isSys;

    private String enable;

}
