package com.xjrsoft.module.zy.form.pojo.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel(description="XJRSOFT.ZY_FORM_BUTTON")
@Data
@TableName(value = "ZY_FORM_BUTTON")
public class ZyFormButtonDO {
    /**
     * 编号
     */
    @TableField(value = "ID")
    @ApiModelProperty(value="编号")
    private String id;

    /**
     * 表单编号
     */
    @TableField(value = "SCHEMEINFO_ID")
    @ApiModelProperty(value="表单编号")
    private String schemeinfoId;

    /**
     * 编号
     */
    @TableField(value = "NUM")
    @ApiModelProperty(value="编号")
    private String num;

    /**
     * 视图名称
     */
    @TableField(value = "\"NAME\"")
    @ApiModelProperty(value="视图名称")
    private String name;

    /**
     * 脚本函数
     */
    @TableField(value = "SCRIPT")
    @ApiModelProperty(value="脚本函数")
    private String script;

    /**
     * 存储过程
     */
    @TableField(value = "PRODUCER")
    @ApiModelProperty(value="存储过程")
    private String producer;

    /**
     * 图标
     */
    @TableField(value = "ICON")
    @ApiModelProperty(value="图标")
    private String icon;

    /**
     * 备注
     */
    @TableField(value = "REMARKS")
    @ApiModelProperty(value="备注")
    private String remarks;

    /**
     * 删除标记0未删除1已被删除
     */
    @TableField(value = "DEL_FLAG")
    @ApiModelProperty(value="删除标记0未删除1已被删除")
    private String delFlag;

    /**
     * 执行模式
     */
    @TableField(value = "MODEL")
    @ApiModelProperty(value="执行模式")
    private String model;

    /**
     * 错误处理模式
     */
    @TableField(value = "ERRORTYPE")
    @ApiModelProperty(value="错误处理模式")
    private String errortype;

    /**
     * 参数配置
     */
    @TableField(value = "BUTTON_PARAMS")
    @ApiModelProperty(value="参数配置")
    private String buttonParams;

    public static final String COL_ID = "ID";

    public static final String COL_SCHEMEINFO_ID = "SCHEMEINFO_ID";

    public static final String COL_NUM = "NUM";

    public static final String COL_NAME = "NAME";

    public static final String COL_SCRIPT = "SCRIPT";

    public static final String COL_PRODUCER = "PRODUCER";

    public static final String COL_ICON = "ICON";

    public static final String COL_CREATE_DATE = "CREATE_DATE";

    public static final String COL_CREATE_BY = "CREATE_BY";

    public static final String COL_UPDATE_DATE = "UPDATE_DATE";

    public static final String COL_UPDATE_BY = "UPDATE_BY";

    public static final String COL_REMARKS = "REMARKS";

    public static final String COL_DEL_FLAG = "DEL_FLAG";

    public static final String COL_MODEL = "MODEL";

    public static final String COL_ERRORTYPE = "ERRORTYPE";

    public static final String COL_BUTTON_PARAMS = "BUTTON_PARAMS";
}
