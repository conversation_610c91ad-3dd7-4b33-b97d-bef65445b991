package com.xjrsoft.module.zy.form.pojo.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel(description="XJRSOFT.ZY_FORM_V2_LIST")
@Data
@TableName(value = "ZY_FORM_V2_LIST")
public class ZyFormV2ListDO {
    @TableField(value = "ID")
    @ApiModelProperty(value="")
    private String id;

    @TableField(value = "\"NAME\"")
    @ApiModelProperty(value="")
    private String name;

    @TableField(value = "REMARKS")
    @ApiModelProperty(value="")
    private String remarks;

    @TableField(value = "DEL_FLAG")
    @ApiModelProperty(value="")
    private String delFlag;

    @TableField(value = "SCHEME")
    @ApiModelProperty(value="")
    private String scheme;

    @TableField(value = "PARAMS")
    @ApiModelProperty(value="")
    private String params;

    public static final String COL_ID = "ID";

    public static final String COL_NAME = "NAME";

    public static final String COL_CREATE_DATE = "CREATE_DATE";

    public static final String COL_CREATE_BY = "CREATE_BY";

    public static final String COL_UPDATE_DATE = "UPDATE_DATE";

    public static final String COL_UPDATE_BY = "UPDATE_BY";

    public static final String COL_REMARKS = "REMARKS";

    public static final String COL_DEL_FLAG = "DEL_FLAG";

    public static final String COL_SCHEME = "SCHEME";

    public static final String COL_PARAMS = "PARAMS";
}
