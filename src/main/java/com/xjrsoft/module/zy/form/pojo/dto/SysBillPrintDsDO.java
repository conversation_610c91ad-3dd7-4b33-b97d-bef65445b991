package com.xjrsoft.module.zy.form.pojo.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xjrsoft.common.model.base.ZyAuditEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 单据打印数据源表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-28
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("SYS_BILL_PRINT_DS")
@ApiModel(value = "SysBillPrintDsDO对象", description = " 单据打印数据源表")
public class SysBillPrintDsDO extends ZyAuditEntity {

    @ApiModelProperty("PAGEID唯一标志")
    @TableField("PAGE_ID")
    private String pageId;

    @ApiModelProperty("编号")
    @TableId(value = "ID", type = IdType.ASSIGN_UUID)
    private String id;

    @ApiModelProperty(" 数据源名称")
    @TableField("NAME")
    private String name;

    @ApiModelProperty("主表")
    @TableField("PRIMARY_TABLE")
    private String primaryTable;

    @ApiModelProperty("是否使用中(使用中的删除进行提示)  1 使用中  0 未使用")
    @TableField("IS_USE")
    private String isUse;

    @TableField("REMARKS")
    private String remarks;

    @ApiModelProperty("数据表别名")
    @TableField("ALIAS_NAME")
    private String aliasName;

    @ApiModelProperty("数据主表主键ID")
    @TableField("PRIMARY_FIELD")
    private String primaryField;

    @ApiModelProperty("过滤sql")
    @TableField("F_SQL")
    private String fSql;

    @ApiModelProperty("数据主表使用审核意见")
    @TableField("IS_USE_COMMENT")
    private String isUseComment;

    @ApiModelProperty("主表替换sql")
    @TableField("TABLE_SQL")
    private String tableSql;


}
