package com.xjrsoft.module.zy.form.pojo.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xjrsoft.common.model.base.ZyAuditEntity;
import java.util.Date;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-01
 */
@Getter
@Setter
  @Accessors(chain = true)
  @TableName("XJR_GL_BIZ_VIEW")
@ApiModel(value = "XjrGlBizViewDTO对象", description = "")
public class XjrGlBizViewDTO extends ZyAuditEntity {

      @ApiModelProperty("名称")
      @TableField("NAME")
    private String name;

      @ApiModelProperty("排序")
      @TableField("SORT_CODE")
    private Long sortCode;

      @ApiModelProperty("备注")
      @TableField("REMARK")
    private String remark;

      @ApiModelProperty("创建人id")
      @TableField("CREATE_USER_ID")
    private String createUserId;

      @ApiModelProperty("修改时间")
      @TableField("MODIFY_DATE")
    private Date modifyDate;

      @ApiModelProperty("修改人id")
      @TableField("MODIFY_USER_ID")
    private String modifyUserId;

      @ApiModelProperty("删除标记")
      @TableField("DELETE_MARK")
    private Long deleteMark;

      @ApiModelProperty("启用标记")
      @TableField("ENABLED_MARK")
    private Long enabledMark;

    @TableField("ID")
    private String id;

      @ApiModelProperty("业务id")
      @TableField("BIZ_ID")
    private String bizId;

      @ApiModelProperty("视图id")
      @TableField("VIEW_ID")
    private String viewId;


}
