package com.xjrsoft.module.language.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
public class AddOrUpdateLanguageDto {

    @ApiModelProperty("编码")
    private Long mapId;

    @ApiModelProperty("翻译内容")
    @NotBlank(message = "翻译内容不能为空！")
    private String languageContent;

    @ApiModelProperty("语言类型")
    @NotBlank(message = "语言类型不能为空！")
    private String languageType;

}
