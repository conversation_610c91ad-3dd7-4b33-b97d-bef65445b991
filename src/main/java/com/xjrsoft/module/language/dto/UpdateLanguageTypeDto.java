package com.xjrsoft.module.language.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotNull;

@Data
@ToString
public class UpdateLanguageTypeDto {


    /**
     * 主键ID
     */
    @ApiModelProperty("主键ID")
    private Long id;

    /**
     * 语言名称
     */
    @ApiModelProperty("语言名称")
    @NotNull(message = "语言编码不能为空!")
    @Length(min = 1, max = 200, message = "语言编码不能大于200个字符！")
    private String name;

    /**
     * 语言编码（不予许重复）
     */
    @ApiModelProperty("语言编码（不予许重复）")
    private String code;

    /**
     * 是否是主语言0不是1是
     */
    @ApiModelProperty("是否是主语言0不是1是")
    private Integer isMain;
}
