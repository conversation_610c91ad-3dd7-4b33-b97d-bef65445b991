package com.xjrsoft.module.language.service;

import com.xjrsoft.module.language.dto.AddOrUpdateLanguageDto;
import com.xjrsoft.module.language.entity.Language;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 语言翻译表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-09
 */
public interface ILanguageService extends IService<Language> {

    boolean add(List<AddOrUpdateLanguageDto> dto);

    boolean update(List<AddOrUpdateLanguageDto> dto);

    List<Map<String,Object>> page(String keywords);

    Map<String, Map<String,String>> main();

}
