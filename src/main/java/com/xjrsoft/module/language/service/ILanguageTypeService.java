package com.xjrsoft.module.language.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.xjrsoft.common.page.PageOutput;
import com.xjrsoft.module.language.dto.LanguageTypeListDto;
import com.xjrsoft.module.language.entity.LanguageType;
import com.xjrsoft.module.language.vo.LanguageTypeVo;

import java.util.List;

/**
 * <p>
 * 多语言语言类型表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-30
 */
public interface ILanguageTypeService extends IService<LanguageType> {

    boolean setMainLanguage(Long id);

    PageOutput<LanguageTypeVo> getPageData(LanguageTypeListDto dto);

    List<LanguageType> getDataList(String keyword);
}
