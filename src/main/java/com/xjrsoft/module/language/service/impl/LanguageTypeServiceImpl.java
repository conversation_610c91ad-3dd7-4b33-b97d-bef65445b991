package com.xjrsoft.module.language.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xjrsoft.common.page.ConventPage;
import com.xjrsoft.common.page.PageOutput;
import com.xjrsoft.module.language.dto.LanguageTypeListDto;
import com.xjrsoft.module.language.entity.LanguageType;
import com.xjrsoft.module.language.mapper.LanguageTypeMapper;
import com.xjrsoft.module.language.service.ILanguageTypeService;
import com.xjrsoft.module.language.vo.LanguageTypeVo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 多语言语言类型表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-30
 */
@Service
public class LanguageTypeServiceImpl extends ServiceImpl<LanguageTypeMapper, LanguageType> implements ILanguageTypeService {

    @Override
    public boolean setMainLanguage(Long id) {
        QueryWrapper<LanguageType> query = new QueryWrapper<>();
        query.lambda().eq(LanguageType::getIsMain, 1);
        LanguageType oldXjrLgType = baseMapper.selectOne(query);
        if (oldXjrLgType != null) {
            oldXjrLgType.setIsMain(0);
            // 更新
            retBool(baseMapper.updateById(oldXjrLgType));
        }
        LanguageType xjrLgType = baseMapper.selectById(id);
        if (xjrLgType != null) {
            xjrLgType.setIsMain(1);
            return retBool(baseMapper.updateById(xjrLgType));
        }
        return false;
    }

    @Override
    public PageOutput<LanguageTypeVo> getPageData(LanguageTypeListDto dto) {
        String keyword = dto.getKeyword();
        IPage<LanguageType> page = ConventPage.getPage(dto);
        this.page(page, Wrappers.<LanguageType>query().lambda()
                .like(!StringUtils.isEmpty(keyword), LanguageType::getName, keyword)
                .or().like(!StringUtils.isEmpty(keyword), LanguageType::getCode, keyword));
        return ConventPage.getPageOutput(page, LanguageTypeVo.class);
    }

    @Override
    public List<LanguageType> getDataList(String keyword) {
        return this.list(Wrappers.<LanguageType>query()
                .lambda().like(!StringUtils.isEmpty(keyword), LanguageType::getName, keyword)
                .like(!StringUtils.isEmpty(keyword), LanguageType::getName, keyword)
                .orderByAsc(LanguageType::getName));
    }
}
