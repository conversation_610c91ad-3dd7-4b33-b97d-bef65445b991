package com.xjrsoft.module.language.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xjrsoft.module.language.dto.AddOrUpdateLanguageDto;
import com.xjrsoft.module.language.entity.Language;
import com.xjrsoft.module.language.entity.LanguageType;
import com.xjrsoft.module.language.mapper.LanguageMapper;
import com.xjrsoft.module.language.mapper.LanguageTypeMapper;
import com.xjrsoft.module.language.service.ILanguageService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 语言翻译表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-09
 */
@Service
@AllArgsConstructor
public class LanguageServiceImpl extends ServiceImpl<LanguageMapper, Language> implements ILanguageService {


    private final LanguageTypeMapper languageTypeMapper;

    @Override
    public boolean add(List<AddOrUpdateLanguageDto> dto) {

        List<LanguageType> languageTypes = languageTypeMapper.selectList(null);
        List<Language> languages = BeanUtil.copyToList(dto, Language.class);

        long snowflakeNextId = IdUtil.getSnowflakeNextId();

        for (Language language : languages) {
            Optional<LanguageType> lgType = languageTypes.stream().filter(x -> x.getCode().equals(language.getLanguageType())).findFirst();
            lgType.ifPresent(t -> {
                language.setTypeId(t.getId());
            });
            language.setMapId(snowflakeNextId);
        }
        //新增新标记
        saveBatch(languages);

        return true;
    }

    @Override
    public boolean update(List<AddOrUpdateLanguageDto> dto) {

        List<Language> languages = BeanUtil.copyToList(dto, Language.class);

        List<LanguageType> languageTypes = languageTypeMapper.selectList(null);

        Language one = languages.get(0);

        //删除原有标记
        remove(Wrappers.lambdaQuery(Language.class).eq(Language::getMapId, one.getMapId()));

        for (Language language : languages) {
            Optional<LanguageType> lgType = languageTypes.stream().filter(x -> x.getCode().equals(language.getLanguageType())).findFirst();
            lgType.ifPresent(t -> {
                language.setTypeId(t.getId());
            });
        }

        //新增新标记
        saveBatch(languages);

        return true;
    }

    @Override
    public List<Map<String, Object>> page(String keyword) {

        LambdaQueryWrapper<Language> eq = Wrappers.lambdaQuery(Language.class).eq(StrUtil.isNotBlank(keyword), Language::getLanguageContent, keyword);

        List<Language> languages = list(eq);

        List<Map<String, Object>> result = new ArrayList<>();

        Map<Long, List<Language>> group = languages.stream().collect(Collectors.groupingBy(Language::getMapId));

        //根据语言类型分组后  遍历所有当前语言类型下的所有翻译
        for (Long key : group.keySet()) {
            Map<String, Object> map = new HashMap<>();
            //然后再根据mapId 映射id 分组  所有相同翻译的  分组到一起
            map.put("mapId", key);
            List<Language> lgs = group.get(key);
            for (Language lg : lgs) {
                map.put(lg.getLanguageType(), lg.getLanguageContent());
            }
            result.add(map);
        }

        return result;
    }

    @Override
    public Map<String, Map<String, String>> main() {

        Map<String, Map<String, String>> result = new HashMap<>();

//        List<Language> list = list();
//
//        Map<Long, List<Language>> group = list.stream().collect(Collectors.groupingBy(Language::getMapId));
//
//        List<LanguageType> languageTypes = languageTypeMapper.selectList(null);
//
//        Optional<LanguageType> mainLgOp = languageTypes.stream().filter(x -> x.getId().equals(GlobalConstant.SUPER_ADMIN_ROLE_ID)).findFirst();
//
//        if (!mainLgOp.isPresent()) {
//            throw new MyException("找不到中文语言，无法使用多语言， 请联系管理员！");
//        }
//
//        //找到除了中文以外的所有语言
////        List<LanguageType> otherLg = languageTypes.stream().collect(Collectors.toList());
//        for (LanguageType languageType : languageTypes) {
//            Map<String, String> map = new HashMap<>();
//            //遍历所有分组
//            for (Long key : group.keySet()) {
//                //同一翻译的数据
//                List<Language> languages = group.get(key);
//
//                Language chineseCode = languages.stream().filter(x -> x.getTypeId().equals(1L)).findFirst().orElse(new Language());
//                Language thisLgCode = languages.stream().filter(x -> languageType.getCode().equals(x.getLanguageType())).findFirst().orElse(new Language());
//
//                map.put(chineseCode.getLanguageContent(), thisLgCode.getLanguageContent());
//            }
//            result.put(languageType.getCode(), map);
//        }

        return result;
    }
}
