package com.xjrsoft.module.language.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * 多语言语言类型表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-30
 */
@TableName("xjr_language_type")
@ApiModel(value = "xjr_language_type对象", description = "多语言语言类型表")
@Data
public class LanguageType implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键ID")
    private String id;

    @ApiModelProperty("语言名称")
    private String name;

    @ApiModelProperty("语言编码（不予许重复）")
    private String code;

    @ApiModelProperty("是否是主语言0不是1是")
    private Integer isMain;
}
