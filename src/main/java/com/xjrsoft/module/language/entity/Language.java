package com.xjrsoft.module.language.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * 语言翻译表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-09
 */
@Data
@TableName("xjr_language")
@ApiModel(value = "Language对象", description = "语言翻译表")
public class Language implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("语言类型id")
    private String typeId;

    @ApiModelProperty("映射id 相同一个翻译标记 id相同")
    private Long mapId;

    @ApiModelProperty("翻译内容")
    private String languageContent;

    @ApiModelProperty("语言类型code")
    private String languageType;


}
