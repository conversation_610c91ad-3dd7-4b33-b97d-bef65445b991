package com.xjrsoft.module.language.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class LanguageTypeVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ApiModelProperty("主键ID")
    private String id;

    /**
     * 语言名称
     */
    @ApiModelProperty("语言名称")
    private String name;

    /**
     * 语言编码（不予许重复）
     */
    @ApiModelProperty("语言编码（不予许重复）")
    private String code;

    /**
     * 是否是主语言0不是1是
     */
    @ApiModelProperty("是否是主语言0不是1是")
    private Integer isMain;
}
