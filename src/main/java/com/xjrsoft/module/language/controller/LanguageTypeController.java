package com.xjrsoft.module.language.controller;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.xjrsoft.common.constant.GlobalConstant;
import com.xjrsoft.common.model.result.R;
import com.xjrsoft.module.language.dto.LanguageTypeListDto;
import com.xjrsoft.module.language.dto.UpdateLanguageTypeDto;
import com.xjrsoft.module.language.entity.LanguageType;
import com.xjrsoft.module.language.service.ILanguageTypeService;
import com.xjrsoft.module.language.vo.LanguageTypeVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>
 * 多语言语言类型表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-30
 */
@RestController
@RequestMapping(GlobalConstant.LANGUAGE_MODULE_PREFIX + "/type")
@Api(value = GlobalConstant.LANGUAGE_MODULE_PREFIX + "/type", tags = "翻译语言类型")
@AllArgsConstructor
public class LanguageTypeController {

    private final ILanguageTypeService lgTypeService;

    @GetMapping("/info")
    @ApiOperation(value = "获取详情")
    public R info(@RequestParam String id) {
        return R.ok(lgTypeService.getById(id));
    }

    @PutMapping
    @ApiOperation(value = "修改")
    public R updateLgType(@RequestBody UpdateLanguageTypeDto updateLgTypeDto) {
        LanguageType lgType = BeanUtil.toBean(updateLgTypeDto, LanguageType.class);
        return R.ok(lgTypeService.updateById(lgType));
    }

    @PutMapping("/main")
    @ApiOperation(value = "设置为主语言")
    public R setMainLanguage(@RequestParam Long id) {
        return R.ok(lgTypeService.setMainLanguage(id));
    }

    @GetMapping("/page")
    @ApiOperation(value = "获取列表数据")
    public R page(LanguageTypeListDto dto) {
        return R.ok(lgTypeService.getPageData(dto));
    }

    @GetMapping("/list")
    @ApiOperation(value = "获取列表数据")
    public R list(@RequestParam(required = false) String keyword) {
        List<LanguageType> dataList = lgTypeService.getDataList(keyword);
        return R.ok(BeanUtil.copyToList(dataList, LanguageTypeVo.class));
    }

    @PostMapping
    @ApiOperation(value = "新增")
    public R save(@RequestBody UpdateLanguageTypeDto updateLgTypeDto) {
        long count = lgTypeService.count(Wrappers.lambdaQuery(LanguageType.class).eq(LanguageType::getCode, updateLgTypeDto.getCode()));
        if(count > 0){
            return R.error("已经存在此语言编码！");
        }

        LanguageType lgType = BeanUtil.toBean(updateLgTypeDto, LanguageType.class);
        return R.ok(lgTypeService.save(lgType));
    }

    @DeleteMapping
    @ApiOperation(value = "删除")
    public R delete(@RequestBody List<Long> ids) {
        if(ids.contains(GlobalConstant.SUPER_ADMIN_ROLE_ID)){
            return R.error("不能删除中文！");
        }
        return R.ok(lgTypeService.removeByIds(ids));
    }

    @GetMapping("/main")
    @ApiOperation(value = "获取默认主语言")
    public R getMainLgType() {
        LanguageType lgType = lgTypeService.getOne(Wrappers.<LanguageType>query().lambda().eq(LanguageType::getIsMain, 1), false);
        if (lgType == null) {
            return R.error("当前系统没有设置主语言！");
        }
        return R.ok(BeanUtil.toBean(lgType, LanguageTypeVo.class));
    }
}
