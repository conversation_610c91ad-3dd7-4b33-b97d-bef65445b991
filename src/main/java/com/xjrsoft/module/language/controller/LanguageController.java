package com.xjrsoft.module.language.controller;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.xjrsoft.common.constant.GlobalConstant;
import com.xjrsoft.common.model.result.R;
import com.xjrsoft.module.language.dto.AddOrUpdateLanguageDto;
import com.xjrsoft.module.language.entity.Language;
import com.xjrsoft.module.language.service.ILanguageService;
import com.xjrsoft.module.language.vo.LanguageVo;
import com.xjrsoft.module.system.service.IFileService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.List;

/**
 * <p>
 * 语言翻译表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-09
 */

@RestController
@RequestMapping(GlobalConstant.LANGUAGE_MODULE_PREFIX)
@Api(value = GlobalConstant.LANGUAGE_MODULE_PREFIX, tags = "翻译")
@AllArgsConstructor
public class LanguageController {

    private ILanguageService languageService;

    @Autowired
    private Environment env;
    @Autowired
    private IFileService iFileService;


    @GetMapping("/info")
    @ApiOperation(value = "获取详情")
    public R info(@RequestParam String id) {

        Language language = languageService.getById(id);
        return R.ok(BeanUtil.toBean(language, LanguageVo.class));
    }

    @PutMapping
    @ApiOperation(value = "修改翻译")
    public R update(@Valid @RequestBody List<AddOrUpdateLanguageDto> dto) {
        return R.ok(languageService.update(dto));
    }

    @PostMapping
    @ApiOperation(value = "根据翻译标记新增翻译")
    public R add(@Valid @RequestBody List<AddOrUpdateLanguageDto> dto) {
        return R.ok(languageService.add(dto));
    }

    @GetMapping("/page")
    @ApiOperation(value = "获取列表数据，前端分页")
    public R page(String keyword) {

        return R.ok(languageService.page(keyword));
    }

    @GetMapping("/main-language-json")
    @ApiOperation(value = "获取中文一一对应默认语言")
    public R main(HttpServletRequest request) {

        return R.ok(languageService.main());
    }

    @DeleteMapping
    @ApiOperation(value = "删除翻译标记")
    public R delete(@RequestBody List<Long> ids) {

        languageService.remove(Wrappers.lambdaQuery(Language.class).in(Language::getMapId, ids));
        return R.ok(Boolean.TRUE);
    }
}
