package com.xjrsoft.module.organization.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>
 * 用户关联部门表
 * </p>
 */
@TableName("xjr_user_dept_relation")
@ApiModel(value = "UserDeptRelation对象", description = "用户关联部门表")
@Data
public class UserDeptRelation {
    private static final long serialVersionUID = 1L;

    private Long id;

    @ApiModelProperty("用户ID")
    private String userId;

    @ApiModelProperty("部门id")
    private String deptId;

    @ApiModelProperty("0部门1单位")
    private Integer lx;
}
