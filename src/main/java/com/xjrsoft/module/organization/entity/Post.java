package com.xjrsoft.module.organization.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.xjrsoft.common.model.base.AuditEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <p>
 * 岗位
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-02
 */
@TableName("xjr_post")
@ApiModel(value = "Post对象", description = "岗位")
@Data
@EqualsAndHashCode(callSuper = false)
public class Post extends AuditEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    private String id;


    @ApiModelProperty("名字")
    private String name;

    @ApiModelProperty("编码")
    private String code;

    @ApiModelProperty("父级")
    private String parentId;

    @ApiModelProperty("排序号")
    private Integer sortCode;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("岗位所属组织id")
    private String deptId;
}
