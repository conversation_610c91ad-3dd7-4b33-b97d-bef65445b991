package com.xjrsoft.module.organization.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xjrsoft.common.model.base.AuditEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 用户
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-02
 */
@TableName("xjr_user")
@ApiModel(value = "User对象", description = "用户")
@Data
@EqualsAndHashCode(callSuper = false)
public class User extends AuditEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    private String id;

    @ApiModelProperty("账户")
    private String userName;

    @ApiModelProperty("姓名")
    private String name;

    @ApiModelProperty("编号")
    private String code;

    @ApiModelProperty("昵称")
    private String nickName;

    @ApiModelProperty("密码")
    private String password;

    @ApiModelProperty("性别")
    private Integer gender;

    @ApiModelProperty("手机号")
    private String mobile;

    @ApiModelProperty("角色Id")
    @TableField(exist = false)
    private String postId;

    @ApiModelProperty("头像")
    private String avatar;

    @ApiModelProperty("邮箱")
    private String email;

    @ApiModelProperty("地址")
    private String address;

    @ApiModelProperty("经度")
    private Double longitude;

    @ApiModelProperty("纬度")
    private Double latitude;

    @ApiModelProperty("排序码")
    private Integer sortCode;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("部门")
    @TableField(exist = false)
    private String departmentId;

    @ApiModelProperty("微信号码")
    private String wechatNumber;

    @ApiModelProperty("qq号码")
    private String qqNumber;

    @ApiModelProperty("出生日期")
    private LocalDateTime birthDate;

    @ApiModelProperty("对应企业微信用户id")
    private String wechatUserId;

    @ApiModelProperty("对应钉钉用户id")
    private String dingtalkUserId;

    @ApiModelProperty("是否开启密码验证")
    private Integer passwordAuthentication;

    @ApiModelProperty("电话号码")
    private String phoneNumber;

    @ApiModelProperty("身份证号")
    private String identityCardNumber;

    @ApiModelProperty("政治面貌-数据字典id")
    private Long politicsStatus;

    @ApiModelProperty("行政职务-数据字典id")
    private Long administrativePost;

    @ApiModelProperty("行政职级-数据字典id")
    private Long administrativeRank;

    @ApiModelProperty("用户密级-数据字典id")
    private Long secretLevel;

    @ApiModelProperty("职称等级-数据字典id")
    private Long professionalTitleGrade;

    @ApiModelProperty("技术职务-数据字典id")
    private Long technicalPosition;

    @ApiModelProperty("管理职务-数据字典id")
    private Long managerialPosition;

    @ApiModelProperty("职业技能-数据字典id")
    private Long vocationalSkill;

    @ApiModelProperty("绑定ip")
    private String bindIp;

    @ApiModelProperty("单位")
    private String dw;

    @ApiModelProperty("部门")
    private String bm;

    private String useDepartmentIds;
}
