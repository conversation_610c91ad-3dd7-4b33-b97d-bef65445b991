package com.xjrsoft.module.organization.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * 用户负责部门表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-20
 */
@TableName("xjr_user_charge_dept")
@ApiModel(value = "UserChargeDept对象", description = "用户负责部门表")
@Data
public class UserChargeDept implements Serializable {

    private static final long serialVersionUID = 1L;
    private Long id;

    private String userId;

    private String deptId;
}
