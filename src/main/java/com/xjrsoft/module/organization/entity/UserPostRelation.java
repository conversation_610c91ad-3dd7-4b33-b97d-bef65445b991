package com.xjrsoft.module.organization.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * 用户关联岗位表
 * </p>
 */
@TableName("xjr_user_post_relation")
@ApiModel(value = "UserPostRelation对象", description = "用户关联岗位表")
@Data
public class UserPostRelation implements Serializable {
    private static final long serialVersionUID = 1L;

    private Long id;

    @ApiModelProperty("用户ID")
    private String userId;

    @ApiModelProperty("岗位id")
    private String postId;
}
