package com.xjrsoft.module.organization.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @title: PostPageVo
 * <AUTHOR>
 * @Date: 2022/4/4 17:56
 * @Version 1.0
 */
@Data
public class PostPageVo implements Serializable {

    private static final long serialVersionUID = 1L;

    private String id;

    @ApiModelProperty("名字")
    private String name;

    @ApiModelProperty("编码")
    private String code;

    @ApiModelProperty("父级id")
    private String parentId;

    @ApiModelProperty("排序号")
    private Integer sortCode;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("启用状态")
    private Integer enabledMark;

}
