package com.xjrsoft.module.organization.vo;

import com.xjrsoft.common.model.tree.ITreeNode;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @title: DepartmentTreeVo
 * <AUTHOR>
 * @Date: 2022/4/5 21:47
 * @Version 1.0
 */
@Data
public class DepartmentTreeVo implements ITreeNode<DepartmentTreeVo,String>, Serializable {

    private static final long serialVersionUID = 1L;

    private String id;

    private String name;

    private String parentId;

    @ApiModelProperty("编码")
    private String code;

    @ApiModelProperty("电话")
    private String mobile;

    @ApiModelProperty("邮箱")
    private String email;

    @ApiModelProperty("主页")
    private String website;

    @ApiModelProperty("地址")
    private String address;

    @ApiModelProperty("排序号")
    private Integer sortCode;

    @ApiModelProperty("备注")
    private String remark;

    private Integer enabledMark;

    private Boolean disabled;

    public Boolean getDisabled() {
        return this.enabledMark != null && this.enabledMark == 1;
    }

    private List<DepartmentTreeVo> children;


    @ApiModelProperty("组织类别，1：公司，0：部门")
    private Integer departmentType;

    @ApiModelProperty("对应企业微信部门id")
    private Long wechatDeptId;

    @ApiModelProperty("对应钉钉部门id")
    private Long dingtalkDeptId;

    private String dingAgentId;

    private String dingAppKey;

    private String dingAppSecret;

    private String useDepartmentIds;

    @ApiModelProperty("管理人-主要负责人")
    private String custodian;

    @ApiModelProperty("副负责人-用户id")
    private String administrativeLeader;

    @ApiModelProperty("分管局领导-用户id")
    private String partyCommitteeLeader;

    @ApiModelProperty("分管领导-ids")
    private String departmentLeaders;

    @ApiModelProperty("上级分管领导-ids")
    private String chargeOfLeaders;
}
