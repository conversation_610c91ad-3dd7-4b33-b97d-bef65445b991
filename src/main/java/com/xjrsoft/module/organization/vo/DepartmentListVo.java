package com.xjrsoft.module.organization.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @title: DepartmentListVo
 * <AUTHOR>
 * @Date: 2022/4/4 16:30
 * @Version 1.0
 */
@Data
public class DepartmentListVo implements Serializable {

    private static final long serialVersionUID = 1L;

    private String id;

    @ApiModelProperty("机构名称")
    private String name;

    @ApiModelProperty("机构名称")
    private String parentId;

    @ApiModelProperty("编码")
    private String code;

    @ApiModelProperty("电话")
    private String mobile;

    @ApiModelProperty("邮箱")
    private String email;

    @ApiModelProperty("主页")
    private String website;

    @ApiModelProperty("地址")
    private String address;

    @ApiModelProperty("排序号")
    private Integer sortCode;

    @ApiModelProperty("备注")
    private String remark;


    @ApiModelProperty("组织类别，1：公司，0：部门")
    private Integer departmentType;

    private String useDepartmentIds;
}
