package com.xjrsoft.module.organization.vo;

import com.xjrsoft.common.model.base.AuditEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * @title: DepartmentVo
 * <AUTHOR>
 * @Date: 2022/4/4 16:41
 * @Version 1.0
 */
@Data
public class DepartmentVo extends AuditEntity {

    private static final long serialVersionUID = 1L;

    private String id;

    @ApiModelProperty("机构名称")
    private String name;

    @ApiModelProperty("机构名称")
    private String parentId;

    @ApiModelProperty("编码")
    private String code;

    @ApiModelProperty("电话")
    private String mobile;

    @ApiModelProperty("邮箱")
    private String email;

    @ApiModelProperty("主页")
    private String website;

    @ApiModelProperty("地址")
    private String address;

    @ApiModelProperty("排序号")
    private Integer sortCode;

    @ApiModelProperty("备注")
    private String remark;


    @ApiModelProperty("组织类别，1：公司，0：部门")
    private Integer departmentType;


    @ApiModelProperty("对应企业微信部门id")
    private Long wechatDeptId;

    @ApiModelProperty("对应钉钉部门id")
    private Long dingtalkDeptId;

    private String dingAgentId;

    private String dingAppKey;

    private String dingAppSecret;

    @ApiModelProperty("简称")
    private String shortName;

    @ApiModelProperty("组织性质-数据字典id")
    private Long departmentNature;

    @ApiModelProperty("成立时间")
    private LocalDateTime establishedTime;

    @ApiModelProperty("管理人-用户id")
    private String custodian;

//    @ApiModelProperty("管理人-用户name")
//    private String custodianName;

    @ApiModelProperty("传真")
    private String facsimile;

    @ApiModelProperty("行政正职领导-用户id")
    private String administrativeLeader;

    @ApiModelProperty("党委正职领导-用户id")
    private String partyCommitteeLeader;

    @ApiModelProperty("部门领导-ids")
    private String departmentLeaders;

    @ApiModelProperty("上级分管领导-ids")
    private String chargeOfLeaders;

//    @ApiModelProperty("部门领导-names")
//    private String departmentLeaderNames;
//
//    @ApiModelProperty("上级分管领导-names")
//    private String chargeOfLeaderNames;

    @ApiModelProperty("部门标签-数据字典id")
    private String departmentLabel;

    @ApiModelProperty("分机号")
    private String extensionNumber;

    @ApiModelProperty("所属行业 - 数据字典id")
    private Long industry;

    @ApiModelProperty("公司法人")
    private String corporateLegalPerson;

    @ApiModelProperty("联系手机")
    private String phoneNumber;

    @ApiModelProperty("联系电话")
    private String contactNumber;

    @ApiModelProperty("开户银行")
    private String depositBank;

    @ApiModelProperty("银行账户")
    private String bankAccount;

    @ApiModelProperty("经营范围")
    private String businessScope;

    private String useDepartmentIds;
    private String gzUrl;

    @ApiModelProperty("现部门编码")
    private String xCode;
    @ApiModelProperty("部门全称")
    private String xName;
    @ApiModelProperty("现部门简称")
    private String xShortName;
    @ApiModelProperty("原部门编码")
    private String yCode;
    @ApiModelProperty("原部门全称")
    private String yName;
    @ApiModelProperty("原部门简称")
    private String yShortName;
}
