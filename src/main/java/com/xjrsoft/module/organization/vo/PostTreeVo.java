package com.xjrsoft.module.organization.vo;

import com.xjrsoft.common.model.tree.ITreeNode;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class PostTreeVo implements ITreeNode<PostTreeVo,String>, Serializable {
    private static final long serialVersionUID = 1L;

    private String id;

    @ApiModelProperty("名字")
    private String name;

    @ApiModelProperty("编码")
    private String code;

    @ApiModelProperty("父级id")
    private String parentId;

    @ApiModelProperty("排序号")
    private Integer sortCode;

    @ApiModelProperty("备注")
    private String remark;

    private Integer enabledMark;

    private List<PostTreeVo> children;
}
