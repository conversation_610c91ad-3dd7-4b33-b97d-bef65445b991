package com.xjrsoft.module.organization.vo;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * @title: RoleAuthVo
 * <AUTHOR>
 * @Date: 2022/4/10 16:25
 * @Version 1.0
 */
@Data
public class RoleAuthVo {

    private List<String> menuIds;

    private List<String> buttonIds;

    private List<String> columnIds;

    private List<String> formIds;

    public List<String> getMenuIds() {
        if (this.menuIds == null) {
            this.menuIds = new ArrayList<>();
        }
        return this.menuIds;
    }

    public List<String> getButtonIds() {
        if (this.buttonIds == null) {
            this.buttonIds = new ArrayList<>();
        }
        return this.buttonIds;
    }

    public List<String> getColumnIds() {
        if (this.columnIds == null) {
            this.columnIds = new ArrayList<>();
        }
        return this.columnIds;
    }

    public List<String> getFormIds() {
        if (this.formIds == null) {
            this.formIds = new ArrayList<>();
        }
        return this.formIds;
    }
}
