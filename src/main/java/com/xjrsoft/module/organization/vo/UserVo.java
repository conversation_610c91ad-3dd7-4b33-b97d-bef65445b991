package com.xjrsoft.module.organization.vo;

import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * @title: UserVo
 * <AUTHOR>
 * @Date: 2022/4/4 17:10
 * @Version 1.0
 */
public class UserVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 账户
     */
    private String userName;

    /**
     * 姓名
     */
    private String name;

    /**
     * 编号
     */
    private String code;

    /**
     * 昵称
     */
    private String nickName;

    /**
     * 密码
     */
    private String password;

    /**
     * 性别
     */
    private Integer gender;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 角色Id
     */
    private String postId;

    /**
     * 头像
     */
    private String avatar;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 地址
     */
    private String address;

    /**
     * 经度
     */
    private Double longitude;

    /**
     * 纬度
     */
    private Double latitude;

    /**
     * 排序码
     */
    private Integer sortCode;

    /**
     * 排序码
     */
    private String remark;
    private String dw;
    private String bm;

//    private String departmentId;

    private String departmentIds;

    private Integer enabledMark;

    private LocalDateTime createDate;

    private LocalDateTime modifyDate;

    @ApiModelProperty("微信号码")
    private String wechatNumber;

    @ApiModelProperty("qq号码")
    private String qqNumber;

    @ApiModelProperty("生日")
    private LocalDateTime birthDate;

    @ApiModelProperty("是否开启密码验证")
    private Integer passwordAuthentication;

    @ApiModelProperty("电话号码")
    private String phoneNumber;

    @ApiModelProperty("身份证号")
    private String identityCardNumber;

    @ApiModelProperty("政治面貌-数据字典id")
    private Long politicsStatus;

    @ApiModelProperty("行政职务-数据字典id")
    private Long administrativePost;

    @ApiModelProperty("行政职级-数据字典id")
    private Long administrativeRank;

    @ApiModelProperty("用户密级-数据字典id")
    private Long secretLevel;

    @ApiModelProperty("职称等级-数据字典id")
    private Long professionalTitleGrade;

    @ApiModelProperty("技术职务-数据字典id")
    private Long technicalPosition;

    @ApiModelProperty("管理职务-数据字典id")
    private Long managerialPosition;

    @ApiModelProperty("职业技能-数据字典id")
    private Long vocationalSkill;

    @ApiModelProperty("绑定ip")
    private String bindIp;

    private String useDepartmentIds;


    /**
     * 角色
     */
    private List<UserRoleVo> roles;

    /**
     * 所有岗位信息
     */
    private List<UserPostVo> posts;

    /**
     * 负责的部门信息
     */
    private List<UserDeptVo> chargeDepartments;

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getNickName() {
        return nickName;
    }

    public void setNickName(String nickName) {
        this.nickName = nickName;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public Integer getGender() {
        return gender;
    }

    public void setGender(Integer gender) {
        this.gender = gender;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getPostId() {
        return postId;
    }

    public void setPostId(String postId) {
        this.postId = postId;
    }

    public String getAvatar() {
        return avatar;
    }

    public void setAvatar(String avatar) {
        this.avatar = avatar;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public Double getLongitude() {
        return longitude;
    }

    public void setLongitude(Double longitude) {
        this.longitude = longitude;
    }

    public Double getLatitude() {
        return latitude;
    }

    public void setLatitude(Double latitude) {
        this.latitude = latitude;
    }

    public Integer getSortCode() {
        return sortCode;
    }

    public void setSortCode(Integer sortCode) {
        this.sortCode = sortCode;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getDw() {
        return dw;
    }

    public void setDw(String dw) {
        this.dw = dw;
    }

    public String getBm() {
        return bm;
    }

    public void setBm(String bm) {
        this.bm = bm;
        setDepartmentIds(bm);
    }

    public String getDepartmentIds() {
        if (StringUtils.isEmpty(departmentIds)) {
            return bm;
        }
        return departmentIds;
    }

    public void setDepartmentIds(String departmentIds) {
        if (StringUtils.isEmpty(departmentIds)) {
            return;
        }
        this.departmentIds = departmentIds;
    }

    public Integer getEnabledMark() {
        return enabledMark;
    }

    public void setEnabledMark(Integer enabledMark) {
        this.enabledMark = enabledMark;
    }

    public LocalDateTime getCreateDate() {
        return createDate;
    }

    public void setCreateDate(LocalDateTime createDate) {
        this.createDate = createDate;
    }

    public LocalDateTime getModifyDate() {
        return modifyDate;
    }

    public void setModifyDate(LocalDateTime modifyDate) {
        this.modifyDate = modifyDate;
    }

    public String getWechatNumber() {
        return wechatNumber;
    }

    public void setWechatNumber(String wechatNumber) {
        this.wechatNumber = wechatNumber;
    }

    public String getQqNumber() {
        return qqNumber;
    }

    public void setQqNumber(String qqNumber) {
        this.qqNumber = qqNumber;
    }

    public LocalDateTime getBirthDate() {
        return birthDate;
    }

    public void setBirthDate(LocalDateTime birthDate) {
        this.birthDate = birthDate;
    }

    public Integer getPasswordAuthentication() {
        return passwordAuthentication;
    }

    public void setPasswordAuthentication(Integer passwordAuthentication) {
        this.passwordAuthentication = passwordAuthentication;
    }

    public String getPhoneNumber() {
        return phoneNumber;
    }

    public void setPhoneNumber(String phoneNumber) {
        this.phoneNumber = phoneNumber;
    }

    public String getIdentityCardNumber() {
        return identityCardNumber;
    }

    public void setIdentityCardNumber(String identityCardNumber) {
        this.identityCardNumber = identityCardNumber;
    }

    public Long getPoliticsStatus() {
        return politicsStatus;
    }

    public void setPoliticsStatus(Long politicsStatus) {
        this.politicsStatus = politicsStatus;
    }

    public Long getAdministrativePost() {
        return administrativePost;
    }

    public void setAdministrativePost(Long administrativePost) {
        this.administrativePost = administrativePost;
    }

    public Long getAdministrativeRank() {
        return administrativeRank;
    }

    public void setAdministrativeRank(Long administrativeRank) {
        this.administrativeRank = administrativeRank;
    }

    public Long getSecretLevel() {
        return secretLevel;
    }

    public void setSecretLevel(Long secretLevel) {
        this.secretLevel = secretLevel;
    }

    public Long getProfessionalTitleGrade() {
        return professionalTitleGrade;
    }

    public void setProfessionalTitleGrade(Long professionalTitleGrade) {
        this.professionalTitleGrade = professionalTitleGrade;
    }

    public Long getTechnicalPosition() {
        return technicalPosition;
    }

    public void setTechnicalPosition(Long technicalPosition) {
        this.technicalPosition = technicalPosition;
    }

    public Long getManagerialPosition() {
        return managerialPosition;
    }

    public void setManagerialPosition(Long managerialPosition) {
        this.managerialPosition = managerialPosition;
    }

    public Long getVocationalSkill() {
        return vocationalSkill;
    }

    public void setVocationalSkill(Long vocationalSkill) {
        this.vocationalSkill = vocationalSkill;
    }

    public String getBindIp() {
        return bindIp;
    }

    public void setBindIp(String bindIp) {
        this.bindIp = bindIp;
    }

    public List<UserRoleVo> getRoles() {
        return roles;
    }

    public void setRoles(List<UserRoleVo> roles) {
        this.roles = roles;
    }

    public List<UserPostVo> getPosts() {
        return posts;
    }

    public void setPosts(List<UserPostVo> posts) {
        this.posts = posts;
    }

    public List<UserDeptVo> getChargeDepartments() {
        return chargeDepartments;
    }

    public void setChargeDepartments(List<UserDeptVo> chargeDepartments) {
        this.chargeDepartments = chargeDepartments;
    }

    public String getUseDepartmentIds() {
        return useDepartmentIds;
    }

    public void setUseDepartmentIds(String useDepartmentIds) {
        this.useDepartmentIds = useDepartmentIds;
    }
}

