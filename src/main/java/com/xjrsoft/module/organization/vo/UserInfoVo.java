package com.xjrsoft.module.organization.vo;

import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.xjrsoft.common.exception.MyException;
import com.xjrsoft.module.organization.entity.Department;
import com.xjrsoft.module.organization.service.IDepartmentService;
import lombok.EqualsAndHashCode;

import java.util.List;

@EqualsAndHashCode(callSuper = false)
public class UserInfoVo {
    private static final long serialVersionUID = 1L;

    /**
     * 账户
     */
    private String id;
    /**
     * 账户
     */
    private String userName;

    /**
     * 姓名
     */
    private String name;

    /**
     * 编号
     */
    private String code;

    /**
     * 昵称
     */
    private String nickName;

    /**
     * 地址
     */
    private String address;

    /**
     * 备注
     */
    private String remark;

    /**
     * 密码
     */
    private String password;

    /**
     * 性别
     */
    private Integer gender;

    /**
     * 手机号
     */
    private String mobile;


    /**
     * 角色名称
     */
    private String postName;

    /**
     * 头像
     */
    private String avatar;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 主页
     */
    private String homePath;


    /**
     * 所有部门信息
     */
    private List<UserDeptVo> departments;

    /**
     * 角色
     */
    private List<UserRoleVo> roles;

    /**
     * 所有岗位信息
     */
    private List<UserPostVo> posts;

    /**
     * 负责的部门信息
     */
    private List<UserDeptVo> chargeDepartments;

    private String useDepartmentIds;
    private String bm;
    private String dw;

    private String bmName;
    private String dwName;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getNickName() {
        return nickName;
    }

    public void setNickName(String nickName) {
        this.nickName = nickName;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public Integer getGender() {
        return gender;
    }

    public void setGender(Integer gender) {
        this.gender = gender;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getPostName() {
        return postName;
    }

    public void setPostName(String postName) {
        this.postName = postName;
    }

    public String getAvatar() {
        return avatar;
    }

    public void setAvatar(String avatar) {
        this.avatar = avatar;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getHomePath() {
        return homePath;
    }

    public void setHomePath(String homePath) {
        this.homePath = homePath;
    }

    public List<UserDeptVo> getDepartments() {
        return departments;
    }

    public void setDepartments(List<UserDeptVo> departments) {
        this.departments = departments;
    }

    public List<UserRoleVo> getRoles() {
        return roles;
    }

    public void setRoles(List<UserRoleVo> roles) {
        this.roles = roles;
    }

    public List<UserPostVo> getPosts() {
        return posts;
    }

    public void setPosts(List<UserPostVo> posts) {
        this.posts = posts;
    }

    public List<UserDeptVo> getChargeDepartments() {
        return chargeDepartments;
    }

    public void setChargeDepartments(List<UserDeptVo> chargeDepartments) {
        this.chargeDepartments = chargeDepartments;
    }

    public String getUseDepartmentIds() {
        return useDepartmentIds;
    }

    public void setUseDepartmentIds(String useDepartmentIds) {
        this.useDepartmentIds = useDepartmentIds;
    }

    public String getBm() {
        return bm;
    }

    public void setBm(String bm) {
        this.bm = bm;
        if (StrUtil.isNotBlank(bm)){
            IDepartmentService iDepartmentService = SpringUtil.getBean(IDepartmentService.class);
            Department department = iDepartmentService.getById(bm);
            if (department != null) {
                this.setBmName(iDepartmentService.getById(bm).getName());
            } else {
                throw new MyException(bm + "部门不存在");
            }
        }
    }

    public String getDw() {
        return dw;
    }

    public void setDw(String dw) {
        this.dw = dw;
        if (StrUtil.isNotBlank(dw)) {
            IDepartmentService iDepartmentService = SpringUtil.getBean(IDepartmentService.class);
            Department department = iDepartmentService.getById(dw);
            if (department != null) {
                this.setDwName(department.getName());
            } else {
                throw new MyException(bm + "单位不存在");
            }
        }

    }

    public String getBmName() {
        return bmName;
    }

    public void setBmName(String bmName) {
        this.bmName = bmName;
    }

    public String getDwName() {
        return dwName;
    }

    public void setDwName(String dwName) {
        this.dwName = dwName;
    }
}
