package com.xjrsoft.module.organization.vo;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@Data
@EqualsAndHashCode(callSuper = false)
public class UserOrganizationInfoVo {
    /**
     * 所有部门信息
     */
    private List<String> departmentNameList;

    /**
     * 角色
     */
    private List<String> roleNameList;

    /**
     * 所有部门信息
     */
    private List<String> postNameList;
}
