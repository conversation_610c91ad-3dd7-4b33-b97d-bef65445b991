package com.xjrsoft.module.organization.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xjrsoft.common.page.PageInput;
import com.xjrsoft.common.utils.ListUtils;
import com.xjrsoft.common.utils.TreeUtil;
import com.xjrsoft.common.utils.VoToColumnUtil;
import com.xjrsoft.module.organization.entity.Department;
import com.xjrsoft.module.organization.mapper.DepartmentMapper;
import com.xjrsoft.module.organization.service.IDepartmentService;
import com.xjrsoft.module.organization.vo.DepartmentTreeVo;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 机构 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-02
 */
@Service
@AllArgsConstructor
public class DepartmentServiceImpl extends ServiceImpl<DepartmentMapper, Department> implements IDepartmentService {

    private final DepartmentMapper departmentMapper;

    @Override
    public Object page(PageInput pageInput) {
        LambdaQueryWrapper<Department> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.like(StrUtil.isNotBlank(pageInput.getKeyword()),Department::getName, pageInput.getKeyword())
                .select(Department.class,x -> VoToColumnUtil.fieldsToColumns(DepartmentTreeVo.class).contains(x.getProperty()));
        List<Department> departmentList = departmentMapper.selectList(queryWrapper);
        List<DepartmentTreeVo> build = TreeUtil.build(BeanUtil.copyToList(departmentList, DepartmentTreeVo.class));
        return ListUtils.Pager(pageInput.getSize(), pageInput.getLimit(), build);
    }

    /**
     * 获取所有的子集id
     * @param deptIds
     * @param voList
     * @return
     */
    @Override
    public List<String> getAllChildIds(List<String> deptIds, List<DepartmentTreeVo> voList) {
        List<String> ids = new ArrayList<>();
        for (String deptId : deptIds) {
            if (voList.stream().anyMatch(x -> x.getParentId().equals(deptId))){
                List<DepartmentTreeVo> child = voList.stream().filter(x -> x.getParentId().equals(deptId)).collect(Collectors.toList());
                List<String> allChildId = child.stream().map(DepartmentTreeVo::getId).collect(Collectors.toList());
                List<String> allChildId1= getAllChildIds(allChildId, voList);
                ids.addAll(allChildId);
                ids.addAll(allChildId1);
            }
        }
        return ids;
    }
}
