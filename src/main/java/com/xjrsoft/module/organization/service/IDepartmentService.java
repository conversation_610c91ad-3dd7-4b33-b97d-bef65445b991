package com.xjrsoft.module.organization.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.xjrsoft.common.page.PageInput;
import com.xjrsoft.module.organization.entity.Department;
import com.xjrsoft.module.organization.vo.DepartmentTreeVo;

import java.util.List;

/**
 * <p>
 * 机构 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-02
 */
public interface IDepartmentService extends IService<Department> {

    Object page(PageInput pageInput);

    List<String> getAllChildIds(List<String> deptIds,List<DepartmentTreeVo> voList);
}
