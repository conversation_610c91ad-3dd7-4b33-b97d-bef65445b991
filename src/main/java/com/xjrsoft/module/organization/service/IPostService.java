package com.xjrsoft.module.organization.service;

import com.xjrsoft.module.organization.entity.Post;
import com.baomidou.mybatisplus.extension.service.IService;
import com.xjrsoft.module.organization.vo.SwitchPostVo;
import com.xjrsoft.module.system.dto.SwitchPostDto;

/**
 * <p>
 * 岗位 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-02
 */
public interface IPostService extends IService<Post> {
    SwitchPostVo switchPost(SwitchPostDto dto);
}
