package com.xjrsoft.module.organization.service;

import com.github.yulichang.base.MPJBaseService;
import com.xjrsoft.common.page.PageOutput;
import com.xjrsoft.module.organization.dto.AddUserDto;
import com.xjrsoft.module.organization.dto.UpdateUserDto;
import com.xjrsoft.module.organization.dto.UserPageDto;
import com.xjrsoft.module.organization.dto.WeChatPageDto;
import com.xjrsoft.module.organization.entity.User;
import com.xjrsoft.module.organization.vo.UserInfoVo;
import com.xjrsoft.module.organization.vo.UserPageVo;
import com.xjrsoft.module.organization.vo.UserVo;
import com.xjrsoft.module.organization.vo.WeChatPageVO;

import java.util.List;

/**
 * <p>
 * 用户 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-02
 */
public interface IUserService extends MPJBaseService<User> {

    boolean add(AddUserDto dto);

    boolean update(UpdateUserDto dto);

    boolean deleteBatch(List<String> ids);

    /**
     * 批量获取用户信息
     *
     * @param ids
     * @return 用户信息
     */
    List<UserInfoVo> getUsersInfo(String ids);

    /**
     * 获取微信分页
     * @param dto
     * @return
     */
    PageOutput<WeChatPageVO> getPage(WeChatPageDto dto);

    /**
     * 获取用户分页
     * @param dto
     * @return
     */
    PageOutput<UserPageVo> page(UserPageDto dto);

    UserVo getInfo(String id);

    UserInfoVo getCurrentInfo(User user);

}
