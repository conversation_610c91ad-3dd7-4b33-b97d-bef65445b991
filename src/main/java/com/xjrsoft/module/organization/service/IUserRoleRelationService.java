package com.xjrsoft.module.organization.service;

import com.xjrsoft.module.organization.entity.UserRoleRelation;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 用户关联角色表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-02
 */
public interface IUserRoleRelationService extends IService<UserRoleRelation> {

    boolean addRoleUser(String roleId, List<String> userIds);
}
