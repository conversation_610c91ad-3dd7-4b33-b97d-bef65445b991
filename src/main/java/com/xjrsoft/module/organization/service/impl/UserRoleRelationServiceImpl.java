package com.xjrsoft.module.organization.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xjrsoft.module.organization.entity.UserRoleRelation;
import com.xjrsoft.module.organization.mapper.UserRoleRelationMapper;
import com.xjrsoft.module.organization.service.IUserRoleRelationService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 用户关联角色表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-02
 */
@Service
public class UserRoleRelationServiceImpl extends ServiceImpl<UserRoleRelationMapper, UserRoleRelation> implements IUserRoleRelationService {

    @Override
    @Transactional
    public boolean addRoleUser(String roleId, List<String> userIds) {
        List<UserRoleRelation> userRoleRelationList = new ArrayList<>(userIds.size());
        for (String userId : userIds) {
            UserRoleRelation userRoleRelation = new UserRoleRelation();
            userRoleRelation.setRoleId(roleId);
            userRoleRelation.setUserId(userId);
            userRoleRelationList.add(userRoleRelation);
        }
        // 先删除角色下存在的人员
        this.remove(Wrappers.<UserRoleRelation>lambdaQuery().eq(UserRoleRelation::getRoleId, roleId));
        return this.saveBatch(userRoleRelationList);
    }
}
