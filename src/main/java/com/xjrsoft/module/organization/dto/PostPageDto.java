package com.xjrsoft.module.organization.dto;

import com.xjrsoft.common.page.PageInput;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.validator.constraints.Length;

/**
 * @title: PostPageDto
 * <AUTHOR>
 * @Date: 2022/4/4 17:56
 * @Version 1.0
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class PostPageDto extends PageInput{

    @Length(max = 20,message = "岗位名称不能大于10个字符！")
    private String name;

    @Length(max = 10,message = "岗位编码不能大于10个字符！")
    private String code;

    private Integer enabledMark;

    @ApiModelProperty("岗位所属组织id")
    private Long departmentId;
}
