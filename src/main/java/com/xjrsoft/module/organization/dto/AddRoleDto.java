package com.xjrsoft.module.organization.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * @title: AddRoleDto
 * <AUTHOR>
 * @Date: 2022/4/4 18:27
 * @Version 1.0
 */
@Data
public class AddRoleDto implements Serializable {

    private static final long serialVersionUID = 1L;

    @NotNull(message = "角色名不能为空")
    @ApiModelProperty("名字")
    private String name;

    @NotNull(message = "角色编码不能为空")
    @ApiModelProperty("编码")
    private String code;

    @ApiModelProperty("数据权限")
    private Integer dataAuthType;

    @ApiModelProperty("排序号")
    private Integer sortCode;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("启用状态")
    private Integer enabledMark;

    @ApiModelProperty("1所有数据2所在部门及以下数据3单位及以下数据4按明细设置")
    private String roleRule;
    @ApiModelProperty("角色级别")
    private String roleLevel;


}
