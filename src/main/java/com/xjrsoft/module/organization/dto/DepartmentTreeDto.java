package com.xjrsoft.module.organization.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

/**
 * @title: DepartmentTreeDto
 * <AUTHOR>
 * @Date: 2022/4/9 21:16
 * @Version 1.0
 */
@Data
public class DepartmentTreeDto {

    private String name;

    private String code;

    private Integer enabledMark;

    private Integer isOrg;
    @ApiModelProperty("组织类别，1：单位，0：部门")
    private Integer departmentType;
}
