package com.xjrsoft.module.organization.dto;

import com.xjrsoft.common.page.ListInput;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.validator.constraints.Length;
@Data
@EqualsAndHashCode(callSuper = true)
public class PostTreeDto extends ListInput {


    private String name;

    private String code;

    private Integer enabledMark;

    @ApiModelProperty("岗位所属组织id")
    private String deptId;
}
