package com.xjrsoft.module.organization.dto;

import cn.zhxu.bs.bean.DbField;
import cn.zhxu.bs.bean.DbIgnore;
import cn.zhxu.bs.bean.SearchBean;
import cn.zhxu.bs.operator.Contain;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xjrsoft.module.organization.entity.Department;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <p>
 * 用户
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-02
 */
@TableName("xjr_user")
@ApiModel(value = "User对象", description = "用户")
@Data
@EqualsAndHashCode(callSuper = false)
@SearchBean(tables ="xjr_user")
public class UserVO implements Serializable {

    private static final long serialVersionUID = 1L;

    private String id;

    @ApiModelProperty("账户")
    @DbField(onlyOn = Contain.class)
    private String userName;

    @ApiModelProperty("姓名")
    @DbField(onlyOn = Contain.class)
    private String name;

    @ApiModelProperty("警号")
    @DbField(onlyOn = Contain.class)
    private String code;

    @ApiModelProperty("昵称")
    @DbField(onlyOn = Contain.class)
    private String nickName;

    @ApiModelProperty("密码")
    private String password;

    @ApiModelProperty("性别")
    private String gender;

    @ApiModelProperty("手机号")
    @DbField(onlyOn = Contain.class)
    private String mobile;

    @ApiModelProperty("头像")
    private String avatar;

    @ApiModelProperty("邮箱")
    private String email;

    @ApiModelProperty("地址")
    private String address;

    @ApiModelProperty("排序码")
    private String sortCode;

    @ApiModelProperty("备注")
    private String remark;


    @ApiModelProperty("身份证号")
    @DbField(onlyOn = Contain.class)
    private String identityCardNumber;

    @ApiModelProperty("绑定ip")
    private String bindIp;

    @ApiModelProperty("单位")
    @DbField(onlyOn = Contain.class)
    private String dw;

    @ApiModelProperty("部门")
    @DbField(onlyOn = Contain.class)
    private String bm;

    @ApiModelProperty("page")
    @DbIgnore
    private Integer page;
    @DbIgnore
    @ApiModelProperty("size")
    private Integer size;


}
