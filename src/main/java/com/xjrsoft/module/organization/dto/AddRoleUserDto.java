package com.xjrsoft.module.organization.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

@Data
public class AddRoleUserDto {

    @NotNull(message = "角色ID不能为空")
    private String id;

    private List<String> userIds;

    @ApiModelProperty("类型：0：人员选择，1：按组织架构选择")
    @NotNull(message = "类型不能为空")
    private Integer type;

    private List<String> departmentIds;
}
