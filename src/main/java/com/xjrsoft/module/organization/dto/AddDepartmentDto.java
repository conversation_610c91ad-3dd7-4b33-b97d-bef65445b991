package com.xjrsoft.module.organization.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.Email;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.sql.Timestamp;

/**
 * @title: AddDepartmentDto
 * <AUTHOR>
 * @Date: 2022/4/4 16:48
 * @Version 1.0
 */
@Data
public class AddDepartmentDto  implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("组织名称")
    @NotNull(message = "组织名称不能为空！")
    private String name;

    @ApiModelProperty("组织名称")
    private String parentId;

    @ApiModelProperty("编码")
    @NotNull(message = "组织编码不能为空！")
    private String code;

    @ApiModelProperty("电话")
    @Length(max = 13,message = "电话最多13个字符！")
    private String mobile;

    @ApiModelProperty("邮箱")
    @Email(message = "邮箱格式不正确！")
    @Length(max = 50,message = "邮箱最多50个字符！")
    private String email;

    @ApiModelProperty("主页")
    @Length(max = 50,message = "主页最多50个字符！")
    private String website;

    @ApiModelProperty("地址")
    @Length(max = 250,message = "地址最多50个字符！")
    private String address;

    @ApiModelProperty("排序号")
    private Integer sortCode;

    @ApiModelProperty("备注")
    @Length(max = 250,message = "备注最多50个字符！")
    private String remark;

    @ApiModelProperty("是否启用")
    private Integer enabledMark;

    @ApiModelProperty("组织类别，1：公司，0：部门")
    private Integer departmentType;

    @ApiModelProperty("简称")
    private String shortName;

    @ApiModelProperty("组织性质-数据字典id")
    private Long departmentNature;

    @ApiModelProperty("成立时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Timestamp establishedTime;

    @ApiModelProperty("管理人-用户id")
    private String custodian;

    @ApiModelProperty("传真")
    private String facsimile;

    @ApiModelProperty("行政正职领导-用户id")
    private Long administrativeLeader;

    @ApiModelProperty("党委正职领导-用户id")
    private String partyCommitteeLeader;

    @ApiModelProperty("部门领导-ids")
    private String departmentLeaders;

    @ApiModelProperty("上级分管领导-ids")
    private String chargeOfLeaders;

    @ApiModelProperty("部门标签-数据字典ids")
    private String departmentLabel;

    @ApiModelProperty("分机号")
    private String extensionNumber;

    @ApiModelProperty("所属行业 - 数据字典id")
    private Long industry;

    @ApiModelProperty("公司法人")
    private String corporateLegalPerson;

    @ApiModelProperty("联系手机")
    private String phoneNumber;

    @ApiModelProperty("联系电话")
    private String contactNumber;

    @ApiModelProperty("开户银行")
    private String depositBank;

    @ApiModelProperty("银行账户")
    private String bankAccount;

    @ApiModelProperty("经营范围")
    private String businessScope;

    private String useDepartmentIds;

    @ApiModelProperty("公章url")
    private String gzUrl;

    @ApiModelProperty("现部门编码")
    private String xCode;
    @ApiModelProperty("部门全称")
    private String xName;
    @ApiModelProperty("现部门简称")
    private String xShortName;
    @ApiModelProperty("原部门编码")
    private String yCode;
    @ApiModelProperty("原部门全称")
    private String yName;
    @ApiModelProperty("原部门简称")
    private String yShortName;



}
