package com.xjrsoft.module.organization.dto;

import com.xjrsoft.common.page.PageInput;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.validator.constraints.Length;

/**
 * @title: DepartmentPageDto
 * <AUTHOR>
 * @Date: 2022/4/4 16:35
 * @Version 1.0
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class DepartmentPageDto extends PageInput {


    private String name;


    private String code;

    private Integer enabledMark;
}
