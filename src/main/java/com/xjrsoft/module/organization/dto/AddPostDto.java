package com.xjrsoft.module.organization.dto;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @title: AddPostDto
 * <AUTHOR>
 * @Date: 2022/4/4 17:59
 * @Version 1.0
 */
@Data
public class AddPostDto implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("岗位名称")
    @NotNull(message = "岗位名称不能为空！")
    @Length(min = 2,max = 20,message = "岗位名称不能少于2个字符，大于20个字符！")
    private String name;

    @ApiModelProperty("编码")
    @NotNull(message = "岗位编码不能为空！")
    @Length(max = 10,message = "岗位编码不能大于10个字符！")
    private String code;

    @ApiModelProperty("父级")
    private Long parentId = 0L;

    @ApiModelProperty("排序号")
    private Integer sortCode;

    @ApiModelProperty("备注")
    @Length(max = 255,message = "备注不能大于255个字符！")
    private String remark;

    @ApiModelProperty("有效标记")
    private Integer enabledMark;

    @ApiModelProperty("岗位所属组织id")
    private Long deptId = 0L;

}
