package com.xjrsoft.module.organization.dto;

import com.xjrsoft.common.page.PageInput;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.validator.constraints.Length;

/**
 * @title: UserPageDto
 * <AUTHOR>
 * @Date: 2022/4/4 17:06
 * @Version 1.0
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class UserPageDto extends PageInput {

    private String departmentId;

    @Length(max = 20, message = "用户名长度不能超过20")
    private String userName;

    @Length(max = 20, message = "姓名长度不能超过20")
    private String name;


    private String code;

    @Length(max = 20, message = "手机号长度不能超过20")
    private String mobile;

    private String dw;
    private String bm;
    private String useDepartmentIds;

}
