package com.xjrsoft.module.organization.dto;

import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.Email;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * @Author: tzx
 * @Date:2022/3/28 16:29
 */
@Data
public class UpdateInfoDto implements Serializable {

    private static final long serialVersionUID = 1L;

    @NotNull
    @Length(min = 2,max = 20,message = "姓名不能小于2个字符，不能大于20个字符！")
    private String name;

    @NotNull
    @Length(min = 2,max = 20,message = "编码不能小于2个字符，不能大于20个字符！")
    private String code;

    @NotNull
    @Length(min = 11,max = 11,message = "手机号码必须11位数字！")
    private String mobile;

    @Email
    private String email;

    @Length(max = 255,message = "备注不能超过255个字符！")
    private String remark;

    @Length(max = 200,message = "备注不能超过255个字符！")
    private String address;

    private String useDepartmentIds;



}
