package com.xjrsoft.module.organization.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.sql.Timestamp;
import java.util.List;

/**
 * @title: AddUserDto
 * <AUTHOR>
 * @Date: 2022/4/4 17:11
 * @Version 1.0
 */
@Data
public class AddUserDto implements Serializable {

    private static final long serialVersionUID = 1L;


    @ApiModelProperty("账户")
    @NotNull(message = "用户名不能为空")
    private String userName;

    @ApiModelProperty("姓名")
    @NotNull(message = "姓名不能为空")
    private String name;

    @ApiModelProperty("编号")
    private String code;

    @ApiModelProperty("昵称")
    private String nickName;

    @ApiModelProperty("密码")
    @NotNull(message = "密码不能为空")
    private String password;

    @ApiModelProperty("性别")
    @NotNull(message = "性别必须选择")
    @Range(min = -1, max = 2, message = "性别参数不正确！")
    private Integer gender = -1;

    @ApiModelProperty("手机号")
    private String mobile;

    //    @NotNull(message = "角色不能为空！")
    @ApiModelProperty("角色Id")
    private Long postId;

    @ApiModelProperty("头像")
    private String avatar;

    @ApiModelProperty("邮箱")
    private String email;

    @ApiModelProperty("地址")
    @Length(max = 200, message = "地址不能超过60字符！")
    private String address;

    @ApiModelProperty("经度")
    private Double longitude;

    @ApiModelProperty("纬度")
    private Double latitude;

    @ApiModelProperty("排序码")
    private Integer sortCode;

    @ApiModelProperty("备注")
    @Length(max = 255, message = "备注字符不能超过60字符！")
    private String remark;

    @NotNull(message = "部门不能为空！")
    @ApiModelProperty("部门id")
    private String departmentIds;

    @NotNull(message = "单位！")
    @ApiModelProperty("单位id")
    private String dw;

    @ApiModelProperty("微信号码")
    private String wechatNumber;

    @ApiModelProperty("qq号码")
    private String qqNumber;

    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty("生日")
    private Timestamp birthDate;

    @ApiModelProperty("是否开启密码验证")
    private Integer passwordAuthentication;

    @ApiModelProperty("电话号码")
    private String phoneNumber;

    @ApiModelProperty("身份证号")
    private String identityCardNumber;

    @ApiModelProperty("政治面貌-数据字典id")
    private Long politicsStatus;

    @ApiModelProperty("行政职务-数据字典id")
    private Long administrativePost;

    @ApiModelProperty("行政职级-数据字典id")
    private Long administrativeRank;

    @ApiModelProperty("用户密级-数据字典id")
    private Long secretLevel;

    @ApiModelProperty("职称等级-数据字典id")
    private Long professionalTitleGrade;

    @ApiModelProperty("技术职务-数据字典id")
    private Long technicalPosition;

    @ApiModelProperty("管理职务-数据字典id")
    private Long managerialPosition;

    @ApiModelProperty("职业技能-数据字典id")
    private Long vocationalSkill;

    @ApiModelProperty("所有角色信息")
    private List<String> roleIds;

    @ApiModelProperty("所有岗位信息")
    private List<String> postIds;

    @ApiModelProperty("负责的部门信息")
    private List<String> chargeDepartmentIds;

    @ApiModelProperty("绑定ip")
    private String bindIp;

    private String useDepartmentIds;

}
