package com.xjrsoft.module.organization.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.TypeReference;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.xjrsoft.common.annotation.XjrLog;
import com.xjrsoft.common.constant.GlobalConstant;
import com.xjrsoft.common.enums.YesOrNoEnum;
import com.xjrsoft.common.model.result.R;
import com.xjrsoft.common.page.ConventPage;
import com.xjrsoft.common.page.PageOutput;
import com.xjrsoft.common.utils.RedisUtil;
import com.xjrsoft.common.utils.TreeUtil;
import com.xjrsoft.common.utils.VoToColumnUtil;
import com.xjrsoft.module.organization.dto.*;
import com.xjrsoft.module.organization.entity.Department;
import com.xjrsoft.module.organization.entity.User;
import com.xjrsoft.module.organization.entity.UserDeptRelation;
import com.xjrsoft.module.organization.service.IDepartmentService;
import com.xjrsoft.module.organization.service.IUserDeptRelationService;
import com.xjrsoft.module.organization.service.IUserService;
import com.xjrsoft.module.organization.vo.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * <p>
 * 机构 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-02
 */
@RestController
@RequestMapping(GlobalConstant.ORGANIZATION_MODULE_PREFIX + "/department")
@Api(value = GlobalConstant.ORGANIZATION_MODULE_PREFIX + "/department", tags = "机构")
@AllArgsConstructor
public class DepartmentController {

    private final IDepartmentService departmentService;

    private final IUserService userService;

    private final IUserDeptRelationService userDeptRelationService;

    private final RedisUtil redisUtil;

    @GetMapping(value = "/list")
    @ApiOperation(value = "机构列表(不分页)")
    @XjrLog(value = "获取不分页机构列表")
    public R list() {
        List<Department> list = departmentService.list(Wrappers.lambdaQuery(Department.class)
                .orderByAsc(Department::getSortCode)
                .select(Department.class, x -> VoToColumnUtil.fieldsToColumns(DepartmentListVo.class).contains(x.getProperty())));

        List<DepartmentListVo> departmentListVos = BeanUtil.copyToList(list, DepartmentListVo.class);
        return R.ok(departmentListVos);
    }

    @GetMapping(value = "/page")
    @ApiOperation(value = "机构列表(分页)")
    @XjrLog(value = "获取分页机构列表")
    public R page(DepartmentPageDto dto) {

        LambdaQueryWrapper<Department> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.like(StrUtil.isNotBlank(dto.getKeyword()), Department::getName, dto.getKeyword())
                .like(StrUtil.isNotBlank(dto.getKeyword()), Department::getCode, dto.getKeyword())
                .like(StrUtil.isNotBlank(dto.getName()), Department::getName, dto.getName())
                .like(StrUtil.isNotBlank(dto.getCode()), Department::getCode, dto.getCode())
                .like(ObjectUtil.isNotEmpty(dto.getEnabledMark()), Department::getEnabledMark, dto.getEnabledMark())
                .orderByAsc(Department::getSortCode)
                .select(Department.class, x -> VoToColumnUtil.fieldsToColumns(DepartmentPageVo.class).contains(x.getProperty()));

        IPage<Department> page = departmentService.page(ConventPage.getPage(dto), queryWrapper);
        PageOutput<DepartmentPageVo> pageOutput = ConventPage.getPageOutput(page, DepartmentPageVo.class);
        return R.ok(pageOutput);
    }

    @GetMapping(value = "/enabled-tree")
    @ApiOperation(value = "机构树")
    @XjrLog(value = "部门展示 所有数据")
    public R tree(DepartmentTreeDto dto) {

        List<Department> list = departmentService.list(Wrappers.<Department>lambdaQuery()
                .like(StrUtil.isNotBlank(dto.getName()), Department::getName, dto.getName())
                .like(StrUtil.isNotBlank(dto.getCode()), Department::getCode, dto.getCode())
                .like(ObjectUtil.isNotEmpty(dto.getEnabledMark()), Department::getEnabledMark, dto.getEnabledMark())
                .orderByAsc(Department::getSortCode)
                .select(Department.class, x -> VoToColumnUtil.fieldsToColumns(DepartmentTreeVo.class).contains(x.getProperty())));

        List<DepartmentTreeVo> voList = BeanUtil.copyToList(list, DepartmentTreeVo.class);
        List<DepartmentTreeVo> treeVoList = TreeUtil.build(voList);
        return R.ok(treeVoList);
    }

    @GetMapping(value = "/tree")
    @ApiOperation(value = "部门展示 (忽略 未启用的数据)")
    @XjrLog(value = "部门展示 (忽略 未启用的数据)")
    public R treeEnabled(DepartmentTreeDto dto) {
        List<Department> list = departmentService.list(Wrappers.<Department>lambdaQuery()
                .like(StrUtil.isNotBlank(dto.getName()), Department::getName, dto.getName())
                .like(StrUtil.isNotBlank(dto.getCode()), Department::getCode, dto.getCode())
                .eq(dto.getDepartmentType() != null, Department::getDepartmentType, dto.getDepartmentType())
                .like(ObjectUtil.isNotEmpty(dto.getEnabledMark()), Department::getEnabledMark, dto.getEnabledMark())
                .orderByAsc(Department::getSortCode)
                .select(Department.class, x -> VoToColumnUtil.fieldsToColumns(DepartmentTreeVo.class).contains(x.getProperty())));

        List<DepartmentTreeVo> voList = BeanUtil.copyToList(list, DepartmentTreeVo.class);

        //所有需要过滤掉下级节点的deptIds
        List<String> deptIds = new ArrayList<>();
        for (DepartmentTreeVo departmentTreeVo : voList) {
            if (departmentTreeVo.getEnabledMark() == YesOrNoEnum.NO.getCode()) {
                //如果有下级，下级进行过滤，不显示到前端去
                deptIds.add(departmentTreeVo.getId());
                departmentTreeVo.setDisabled(true);
            } else {
                departmentTreeVo.setDisabled(false);
            }
        }

        //所有需要过滤的ids的集合
        List<String> allChildIds = departmentService.getAllChildIds(deptIds, voList);

        //不是组织管理模块请求过滤父级,如果是组织管理isOrg不为空,不用过滤
        if (ObjectUtil.isEmpty(dto.getIsOrg())) {
            //将父级节点也过滤掉
            allChildIds.addAll(deptIds);
        }

        List<DepartmentTreeVo> vo = voList.stream().filter(u -> !allChildIds.contains(u.getId())).collect(Collectors.toList());

        List<DepartmentTreeVo> treeVoList = TreeUtil.build(vo);
        for (DepartmentTreeVo departmentTreeVo : treeVoList) {
            if ("0".equals(departmentTreeVo.getParentId())) {
                departmentTreeVo.setParentId(null);
            }
        }
        return R.ok(treeVoList);
    }

    @GetMapping(value = "/info")
    @ApiOperation(value = "根据id查询机构信息")
    @XjrLog(value = "id查询获取机构信息")
    public R info(@RequestParam String id) {
        Department dept = departmentService.getById(id);
        if (dept == null) {
            R.error("找不到此机构！");
        }
        DepartmentVo departmentVo = BeanUtil.toBean(dept, DepartmentVo.class);
        return R.ok(departmentVo);
    }

    @PostMapping
    @ApiOperation(value = "新增机构")
    @XjrLog(value = "新增机构")
    public R add(@Valid @RequestBody AddDepartmentDto dto) {
        long count = departmentService.count(Wrappers.<Department>query().lambda().eq(Department::getCode, dto.getCode()));
        if (count > 0) {
            return R.error("组织编码已经存在！");
        }

        Department department = BeanUtil.toBean(dto, Department.class);
        if (ObjectUtil.isEmpty(dto.getParentId())) {
            department.setParentId("0");
        }
        department.setId(department.getCode());
        departmentService.save(department);
        //设置层级
        if (ObjectUtil.isNotEmpty(dto.getParentId())) {
            List<Department> list = redisUtil.get(GlobalConstant.DEP_CACHE_KEY, new TypeReference<List<Department>>() {
            });
            //获取父级的层级
            String hierarchy = list.stream().filter(x -> x.getId().equals(dto.getParentId())).findFirst().orElse(new Department()).getHierarchy();
            //在父级的基础上添加层级
            department.setHierarchy(hierarchy + StringPool.DASH + department.getId());
        } else {
            department.setHierarchy(department.getId().toString());
        }
        departmentService.updateById(department);
        CompletableFuture.runAsync(() -> {
            List<Department> list = departmentService.list();
            redisUtil.set(GlobalConstant.DEP_CACHE_KEY, list);
        });
        return R.ok(true);
    }

    @PutMapping
    @ApiOperation(value = "修改机构")
    @XjrLog(value = "修改机构")
    public R update(@Valid @RequestBody UpdateDepartmentDto dto) {

        long count = departmentService.count(Wrappers.<Department>query().lambda()
                .eq(Department::getCode, dto.getCode())
                .ne(Department::getId, dto.getId()));

        if (count > 0) {
            return R.error("组织编码已经存在！");
        }

        Department department = BeanUtil.toBean(dto, Department.class);
        if (department.getParentId() == null) {
            department.setParentId("0");
        }
        departmentService.updateById(department);

        CompletableFuture.runAsync(() -> {
            List<Department> list = departmentService.list();
            redisUtil.set(GlobalConstant.DEP_CACHE_KEY, list);
        });

        return R.ok(true);
    }

    @DeleteMapping
    @ApiOperation(value = "删除")
    @XjrLog(value = "删除机构")
    public R delete(@RequestBody List<String> ids) {
        //删除岗位时，需要判断，此机构下是不是存在人员，存在人员就不能删除
        List<UserDeptRelation> userDeptRelationList = redisUtil.get(GlobalConstant.USER_DEPT_RELATION_CACHE_KEY, new TypeReference<List<UserDeptRelation>>() {
        });
        //拿ids进行过滤，如果存在，就不能删除
        List<UserDeptRelation> users = userDeptRelationList.stream().filter(u -> ids.contains(u.getDeptId())).collect(Collectors.toList());
        if (users.size() > 0) {
            return R.error("此机构下存在用户！");
        }
        departmentService.removeBatchByIds(ids);
        CompletableFuture.runAsync(() -> {
            List<Department> list = departmentService.list();
            redisUtil.set(GlobalConstant.DEP_CACHE_KEY, list);
        });
        return R.ok(true);
    }

    @PostMapping("add-dept-user")
    @ApiOperation(value = "添加人员（组织）")
    @Transactional(rollbackFor = Exception.class)
    public R addDeptUser(@Valid @RequestBody AddDepartmentUserDto dto) {
        //先删除再新增
        userDeptRelationService.remove(Wrappers.<UserDeptRelation>query().lambda().eq(UserDeptRelation::getDeptId, dto.getId()));
        List<UserDeptRelation> userDeptRelationList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(dto.getUserIds())) {
            for (String userId : dto.getUserIds()) {
                //将用户所选部门保存到关联表中
                UserDeptRelation userDeptRelation = new UserDeptRelation();
                userDeptRelation.setUserId(userId);
                userDeptRelation.setDeptId(dto.getId());
                userDeptRelationList.add(userDeptRelation);
            }
        }
        userDeptRelationService.saveBatch(userDeptRelationList);
        //更新缓存
        CompletableFuture.runAsync(() -> {
            List<UserDeptRelation> deptRelationList = userDeptRelationService.list(Wrappers.lambdaQuery(UserDeptRelation.class));
            redisUtil.set(GlobalConstant.USER_DEPT_RELATION_CACHE_KEY, deptRelationList);
        });
        return R.ok(true);
    }

    @GetMapping(value = "/dept-user-info")
    @ApiOperation(value = "根据组织id查询组织下的人员")
    public R DeptUserInfo(@RequestParam String id) {
        List<String> userIdList = userDeptRelationService.list(Wrappers.<UserDeptRelation>query().lambda().eq(UserDeptRelation::getDeptId, id)).stream().map(UserDeptRelation::getUserId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(userIdList)) {
            return R.ok(new ArrayList<>());
        }

        final int BATCH_SIZE = 1000;
        List<User> users = new ArrayList<>();
        // 分割大列表
        List<List<String>> chunks = IntStream.range(0, (userIdList.size() + BATCH_SIZE - 1) / BATCH_SIZE)
                .mapToObj(i -> userIdList.stream()
                        .skip(i * BATCH_SIZE)
                        .limit(BATCH_SIZE)
                        .collect(Collectors.toList()))
                .collect(Collectors.toList());
        chunks.forEach(chunk -> {
            List<User> user = userService.list(Wrappers.<User>query().lambda().in(User::getId, chunks).select(User.class, x -> VoToColumnUtil.fieldsToColumns(UserRoleVo.class).contains(x.getProperty())));
            users.addAll(user);
        });

        List<UserRoleVo> userRoleVos = BeanUtil.copyToList(users, UserRoleVo.class);
        return R.ok(userRoleVos);
    }

    @GetMapping(value = "/idEqCode")
    @ApiOperation(value = "根据组织id查询组织下的人员")
    public R idEqCode() {

        List<Department> list = departmentService.list();
        list.forEach(x -> {
            if (StrUtil.isNotBlank(x.getParentId())) {
                Department department = departmentService.getById(x.getParentId());
                if (department != null) {
                    departmentService.update(new LambdaUpdateWrapper<Department>()
                            .eq(Department::getId, x.getId())
                            .set(Department::getParentId, department.getCode())
                            .set(Department::getId, x.getCode()));
                }else {
                    departmentService.update(new LambdaUpdateWrapper<Department>()
                            .eq(Department::getId, x.getId())
                            .set(Department::getId, x.getCode()));
                }
            }
        });
        return R.ok(true);
    }


}
