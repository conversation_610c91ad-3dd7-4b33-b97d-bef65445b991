package com.xjrsoft.module.organization.utils;

import com.xjrsoft.module.organization.entity.Department;
import org.apache.commons.lang.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

public class OrganizationUtil {

    /**
     * 获取当前组织下所有下级组织Id
     * @param deptIds 组织ids
     * @param list 组织列表
     * @return
     */
    public static List<String> getDeptChild(List<String> deptIds, List<Department> list){
        List<String> deptChildIds = new ArrayList<>();
        for (String deptId : deptIds) {
            if (list.stream().anyMatch(x -> x.getParentId().equals(deptId))){
                List<Department> child = list.stream().filter(x -> x.getParentId().equals(deptId)).collect(Collectors.toList());
                List<String> allChildId = child.stream().map(Department::getId).collect(Collectors.toList());
                List<String> allChildId1= getDeptChild(allChildId, list);
                deptChildIds.addAll(allChildId);
                deptChildIds.addAll(allChildId1);
            }
        }
        return deptChildIds;
    }

    public static List<Department> getChildDepartment(String departmentId, List<Department> departmentList){

        List<Department> result = new ArrayList<>();
        List<Department> currentAndChildDepartment = departmentList.stream().filter(x -> x.getId().equals(departmentId) || x.getParentId().equals(departmentId)).collect(Collectors.toList());
        Optional<Department> currentDepartmentOp = currentAndChildDepartment.stream().filter(x -> x.getId().equals(departmentId)).findFirst();
        currentDepartmentOp.ifPresent(result::add);

        List<Department> childDepartment = currentAndChildDepartment.stream().filter(x -> x.getParentId().equals(departmentId)).collect(Collectors.toList());
        result.addAll(childDepartment);
        for (Department department : childDepartment) {
            result.addAll(getChildDepartment(department.getId(),departmentList));
        }
        return result;
    }

    /**
     * 过滤禁用的节点，以及下级节点
     * @param list 组织集合
     * @return
     */
    public static List<Department> getUnEnabledMarkList(List<Department> list){
        //所有需要过滤掉下级节点的deptIds
        List<String> deptIds = new ArrayList<>();
        for (Department department : list) {
            if(department.getEnabledMark()==0){
                //如果有下级，下级进行过滤，不显示到前端去
                deptIds.add(department.getId());
            }
        }
        //所有需要过滤的ids的集合
        List<String> allChildIds = getDeptChild(deptIds, list);
        //将父级节点也过滤掉
        allChildIds.addAll(deptIds);
        return list.stream().filter(u -> !allChildIds.contains(u.getId())).collect(Collectors.toList());
    }

    /**
     * 验证密码，密码必须包含大写字母、小写字母、数字和特殊字符，长度8~16位
     * @param password
     * @return
     */
    public static boolean validatePassword(String password) {
        if (StringUtils.isEmpty(password)) {
            return false;
        }
        // 验证密码
        return Pattern.compile("^(?=.*[0-9]).{8,16}$").matcher(password).matches()
                && Pattern.compile("(?=.*[a-z]).{8,16}").matcher(password).matches()
                && Pattern.compile("(?=.*[A-Z]).{8,16}").matcher(password).matches()
                && Pattern.compile("(?=.*[^a-zA-Z0-9]).{8,16}").matcher(password).matches();
    }
}
