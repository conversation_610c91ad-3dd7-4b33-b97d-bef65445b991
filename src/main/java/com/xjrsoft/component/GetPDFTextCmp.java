package com.xjrsoft.component;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.io.FileUtil;
import com.xjrsoft.module.fp.entity.SbfpxxDTO;
import com.xjrsoft.module.fp.extractor.Invoice;
import com.xjrsoft.module.fp.extractor.NewInvoice;
import com.xjrsoft.module.fp.extractor.NewPdfInvoiceExtractor;
import com.xjrsoft.module.fp.extractor.PdfInvoiceExtractor;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import com.yomahub.liteflow.core.NodeComponent;
import lombok.SneakyThrows;

import java.io.IOException;

/**
 * @ClassName GetPDFTextCmp
 * @Description 获取pdf 字符串
 * <AUTHOR>
 * @Date 2024/7/26 14:02
 * @Version 1.0
 */
@LiteflowComponent("GetPDFTextCmp")
public class GetPDFTextCmp extends NodeComponent {
    @SneakyThrows
    @Override
    public void process() {
        SbfpxxDTO sbfpxxDTO = this.getContextBean(SbfpxxDTO.class);
        Invoice extract = null;
        try {
            extract = PdfInvoiceExtractor.extract(FileUtil.readBytes(sbfpxxDTO.getFilePath()));
        } catch (IOException e) {

        }
        NewInvoice newInvoice = null;
        try {
            newInvoice = NewPdfInvoiceExtractor.extract(FileUtil.readBytes(sbfpxxDTO.getFilePath()));
        } catch (IOException e) {

        }
        Invoice invoice = new Invoice();
        CopyOptions copyOptions = CopyOptions.create();
        copyOptions.setIgnoreNullValue(true);
        if (extract != null) {
            BeanUtil.copyProperties(extract, invoice, copyOptions);
        }
        if (newInvoice != null) {
            BeanUtil.copyProperties(newInvoice, invoice, copyOptions);
        }
        sbfpxxDTO.setInvoice(invoice);
    }
}
