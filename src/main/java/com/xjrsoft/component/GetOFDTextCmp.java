package com.xjrsoft.component;

import cn.hutool.core.util.IdUtil;
import com.xjrsoft.module.fp.entity.SbfpxxDTO;
import com.xjrsoft.module.fp.extractor.Invoice;
import com.xjrsoft.module.fp.extractor.OfdInvoiceExtractor;
import com.xjrsoft.module.fp.extractor.PdfInvoiceExtractor;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import com.yomahub.liteflow.core.NodeComponent;
import lombok.SneakyThrows;

import java.io.File;

/**
 * @ClassName GetPDFTextCmp
 * @Description 获取pdf 字符串
 * <AUTHOR>
 * @Date 2024/7/26 14:02
 * @Version 1.0
 */
@LiteflowComponent("GetOFDTextCmp")
public class GetOFDTextCmp extends NodeComponent {

    @SneakyThrows
    @Override
    public void process() {
        SbfpxxDTO sbfpxxDTO = this.getContextBean(SbfpxxDTO.class);
        Invoice extract = OfdInvoiceExtractor.extract(new File(sbfpxxDTO.getFilePath()));
        sbfpxxDTO.setInvoice(extract);
    }
}
