package com.xjrsoft.component;

import cn.hutool.core.io.FileTypeUtil;
import com.xjrsoft.module.fp.entity.SbfpxxDTO;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import com.yomahub.liteflow.core.NodeSwitchComponent;

import java.io.File;

/**
 * @ClassName FPFileTypeSWCmp
 * @Description 根据不同类型的文件处理发票
 * <AUTHOR>
 * @Date 2024/7/26 18:20
 * @Version 1.0
 */
@LiteflowComponent("FPFileTypeSWCmp")
public class FPFileTypeSWCmp extends NodeSwitchComponent {
    @Override
    public String processSwitch() throws Exception {
        SbfpxxDTO sbfpxxDTO = this.getContextBean(SbfpxxDTO.class);
        String type = FileTypeUtil.getType(new File(sbfpxxDTO.getFilePath()), false);
        sbfpxxDTO.setType(type);
        if ("zip".equals(type)) {
            type = "ofd";
        }
        return "tag:"+type;
    }
}
