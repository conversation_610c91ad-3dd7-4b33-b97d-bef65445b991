package com.xjrsoft;

import cn.hutool.extra.spring.SpringUtil;
import com.xjrsoft.common.annotation.UniqueNameGenerator;
import com.xjrsoft.module.system.entity.LoginConfig;
import com.xjrsoft.module.system.service.ILoginConfigService;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.EnableAspectJAutoProxy;

import java.util.List;

/**
 * <AUTHOR>
 */
@SpringBootApplication
@EnableAspectJAutoProxy(exposeProxy = true)
@ComponentScan(nameGenerator = UniqueNameGenerator.class)
public class XjrSoftApplication {
    public static boolean isHttps = false;

    public static void main(String[] args) {
        SpringApplication.run(XjrSoftApplication.class, args);
        ILoginConfigService loginConfigService = SpringUtil.getBean(ILoginConfigService.class);
        List<LoginConfig> list = loginConfigService.list();
        for (LoginConfig loginConfig : list) {
            if ("1".equals(loginConfig.getHttps())) {
                isHttps = true;
            }
        }
    }

}
