package com.leiden.model;

public class LeidenRfidModel {

    private static int VALUE_DEFAULT = -1;

    int x = VALUE_DEFAULT; // 0 - 4800
    int y = VALUE_DEFAULT; // 0 – 支持标签最大高度
    int l = 0; // 文本行数
    int len = 0; // RFID数据长度
    int md = 1;// 数据类型 1 – ASCII字符串  2 – 16进制字符串
    int wd = VALUE_DEFAULT; // 宽 16 - 4800
    int ht = VALUE_DEFAULT;// 16 -支持标签最大高度
    int bx = VALUE_DEFAULT;// 文本外框 0：无效 1：有效
    int ta = VALUE_DEFAULT;// 0x01：粗体 0x02：斜体 0x08：反显 0x20：划线
    int ba = VALUE_DEFAULT; // 片区类型 0-保留区 1-EPC区域 2-TID区域 3-用户区域
    String aps = "00000000"; // 访问密码
    String content = null;

    public void setLen(int len) {
        this.len = len;
    }

    public void setContent(String content) {
        this.content = content;
    }
    public void setBx(int bx) {
        this.bx = bx;
    }
    public void setHt(int ht) {
        this.ht = ht;
    }
    public void setMd(int md) {
        this.md = md;
    }
    public void setTa(int ta) {
        this.ta = ta;
    }
    public void setL(int textRowNumber) {
        this.l = textRowNumber;
    }
    public void setWd(int w) {
        this.wd = w;
    }
    public void setX(int x) {
        this.x = x;
    }
    public void setY(int y) {
        this.y = y;
    }
    public void setAps(String aps) {
        this.aps = aps;
    }

    public void setBa(int ba) {
        this.ba = ba;
    }
    public boolean isAvailable() {
        if (content == null || l <= 0 || !(md != 1 || md != 2) || !(wd == VALUE_DEFAULT || (wd > 16 && wd < 4800))
            || !(ht == VALUE_DEFAULT || ht > 16) || !(bx == VALUE_DEFAULT || bx == 0 || bx == 1)
            || !(ta == VALUE_DEFAULT || ta == 0x00 || ta == 0x02 || ta == 0x08 || ta == 0x20)
            || !((ba == VALUE_DEFAULT || (ba >= 0 && ba <= 3)))) {
            return false;
        }
        return true;
    }

    public String getCmd() {

        StringBuilder cmd = new StringBuilder();
        cmd.append("RFID ");
        if (x > 0 && x < 4800) {
            cmd.append("X=").append(x).append(",");
        }
        if (y > 0) {
            cmd.append("Y=").append(y).append(",");
        }
        if (l > 0) {
            cmd.append("L=").append(l).append(",");
        }
        if (md == 1 || md == 2) {
            cmd.append("MD=").append(md).append(",");
        }
        if (wd > 16 && wd < 4800) {
            cmd.append("WD=").append(wd).append(",");
        }
        if (ht > 16) {
            cmd.append("HT=").append(ht).append(",");
        }
        if (bx == 0 || bx == 1) {
            cmd.append("BX=").append(bx).append(",");
        }
        if (ta == 0x00 || ta == 0x02 || ta == 0x08 || ta == 0x20) {
            cmd.append("TA=").append(ta).append(",");
        }

        if (md == 2) {
            cmd.append("LEN=").append((content.length() + 1) / 2).append(",");
        } else {
            cmd.append("LEN=").append(content.length()).append(",");
        }

        if (ba >= 0 && ba <= 3) {
            cmd.append("BA=").append(ba).append(",");
        }

        cmd.append("APS=").append(aps);

        cmd.append("\r\n");
        cmd.append(content).append("\r\n");
        return cmd.toString();
    }

}
