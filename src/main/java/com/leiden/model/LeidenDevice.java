package com.leiden.model;


public class LeidenDevice {

    String mac;
    String name;

    // 方便扩展 采用组合的形式
    WifiDevice wifiDevice;

    public WifiDevice getWifiDevice() {
        return wifiDevice;
    }

    public void setWifiDevice(WifiDevice wifiDevice) {
        this.wifiDevice = wifiDevice;
        mac = wifiDevice.getIp();
        name = String.valueOf(wifiDevice.getPort());
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getMac() {
        return mac;
    }

    public void setMac(String mac) {
        this.mac = mac;
    }
}
