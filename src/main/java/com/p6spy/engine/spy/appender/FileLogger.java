/**
 * P6Spy
 *
 * Copyright (C) 2002 - 2020 P6Spy
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.p6spy.engine.spy.appender;

import cn.hutool.core.date.DateUtil;
import com.p6spy.engine.spy.P6SpyOptions;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.PrintStream;
import java.util.Date;

/**
 * Appender which writes log messages to a file.  This is the default appended for P6Spy.
 */
public class FileLogger extends StdoutLogger {

    private String fileName = null;
    private PrintStream printStream = null;
    private String logDate = "";

    private void init() {
        if (fileName == null) {
            throw new IllegalStateException("setLogfile() must be called before init()");
        }
        logDate = DateUtil.format(new Date(),"yyyyMMdd");
        String logsFile = fileName+ File.separator+"p6spy"+ DateUtil.format(new Date(),"yyyyMMdd")+".log";
        try {
            printStream = new PrintStream(new FileOutputStream(logsFile, P6SpyOptions.getActiveInstance().getAppend()));
        } catch (IOException e) {
            throw new IllegalStateException("couldn't create PrintStream for " + logsFile, e);
        }
    }

    @Override
    protected PrintStream getStream() {
        // Lazy init to allow for the appender to be changed at Runtime without creating an empty log file (assuming
        // that no log message has been written yet)
        String logDateTemp = DateUtil.format(new Date(),"yyyyMMdd");
        try {
            if (!logDateTemp.equals(logDate)) {
                printStream.flush();
                printStream.close();
                printStream = null;
            }
        } catch (Exception e) {

        }

        if (printStream == null) {
            synchronized (this) {
                if (printStream == null) {
                    init();
                }
            }
        }
        return printStream;
    }

    public void setLogfile(String fileName) {
        this.fileName = fileName;
    }
}

