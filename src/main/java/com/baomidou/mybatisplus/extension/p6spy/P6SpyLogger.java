/*
 * Copyright (c) 2011-2022, b<PERSON><PERSON><PERSON><PERSON> (<EMAIL>).
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.baomidou.mybatisplus.extension.p6spy;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.p6spy.engine.spy.appender.MessageFormattingStrategy;
import com.xjrsoft.common.constant.GlobalConstant;
import com.xjrsoft.common.utils.RedisUtil;
import com.xjrsoft.common.utils.RequestUtil;
import com.xjrsoft.common.utils.ZyQqLogHolder;
import com.xjrsoft.module.organization.entity.User;
import com.xjrsoft.module.organization.service.IUserService;
import com.xjrsoft.module.zy.form.pojo.entity.ZyQqLog;
import org.apache.skywalking.apm.toolkit.trace.TraceContext;

/**
 * P6spy SQL 打印策略
 *
 * <AUTHOR>
 * @since 2019-02-20
 */
public class P6SpyLogger implements MessageFormattingStrategy {

    public RedisUtil redisUtil;
    private IUserService iUserService;

    @Override
    public String formatMessage(int connectionId, String now, long elapsed, String category,
                                String prepared, String sql, String url) {
        if (redisUtil == null) {
            redisUtil = SpringUtil.getBean(RedisUtil.class);
            iUserService = SpringUtil.getBean(IUserService.class);
        }
        String tokenValue = "";
        try {
            tokenValue = StpUtil.getTokenValue();
        } catch (Exception e) {

        }
        String loginIdAsString = null;
        try {
            User user = (User) StpUtil.getTokenSession().get(GlobalConstant.LOGIN_USER_INFO_KEY);
            loginIdAsString = JSON.toJSONString(user);
        } catch (Exception e) {

        }

        String requestUrl = "";
        try {
            requestUrl = RequestUtil.getCurrentRequest().getRequestURI();
        } catch (Exception e) {

        }
        String traceId = "";
        try {
            traceId = TraceContext.traceId();
        } catch (Exception e) {

        }
        String body = "";
        String param = "";
        String queryString = "";
        try {
            ZyQqLog context = ZyQqLogHolder.getContext();
            body = context.getBody();
            param = context.getParam();
            queryString = context.getQueryString();
        } catch (Exception e) {

        }


        return StringUtils.isNotBlank(sql) ? " Consume Time：" + elapsed + " ms " + now + "  " +
                "\n 用户信息：" + loginIdAsString +
                "\n tokenValue：" + tokenValue +
                "\n 请求路径：" + requestUrl +
                "\n body：" + body +
                "\n param：" + param +
                "\n queryString：" + queryString +
                "\n traceId：" + traceId +
                "\n 执行的sql：" + sql.replaceAll("[\\s]+", " ") + "\n" : "";
    }
}
