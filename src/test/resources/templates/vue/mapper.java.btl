package ${package.Mapper};

import ${package.Entity}.${entity};
import ${superMapperClassPackage};
<% if(mapperAnnotation){ %>
import org.apache.ibatis.annotations.Mapper;
<% } %>

/**
 * <p>
 * ${table.comment!} Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since ${date}
 */
<% if(mapperAnnotation){ %>
@Mapper
<% } %>
<% if(kotlin){ %>
interface ${table.mapperName} : ${superMapperClass}<${entity}>
<% }else{ %>
public interface ${table.mapperName} extends ${superMapperClass}<${entity}> {

}
<% } %>
