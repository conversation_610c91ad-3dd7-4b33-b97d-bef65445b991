package com.xjrsoft.module.fp.service;

import com.xjrsoft.module.fp.entity.BizFp;
import com.xjrsoft.module.fp.entity.InvoiceSerialCheckResult;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit.jupiter.SpringJUnitConfig;

import javax.annotation.Resource;
import java.util.Date;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 发票连号校验测试
 */
@SpringBootTest
@SpringJUnitConfig
public class BizFpServiceTest {

    @Resource
    private BizFpService bizFpService;

    @Test
    public void testCheckInvoiceSerial_InvalidLength() {
        // 测试无效长度的发票号码
        InvoiceSerialCheckResult result = bizFpService.checkInvoiceSerial("123456");
        assertFalse(result.isHasSerial());
        assertTrue(result.getMessage().contains("长度不符合要求"));
    }

    @Test
    public void testCheckInvoiceSerial_NonNumeric() {
        // 测试非数字发票号码
        InvoiceSerialCheckResult result = bizFpService.checkInvoiceSerial("1234567A");
        assertFalse(result.isHasSerial());
        assertTrue(result.getMessage().contains("必须为纯数字"));
    }

    @Test
    public void testCheckInvoiceSerial_EmptyNumber() {
        // 测试空发票号码
        InvoiceSerialCheckResult result = bizFpService.checkInvoiceSerial("");
        assertFalse(result.isHasSerial());
        assertTrue(result.getMessage().contains("不能为空"));
    }

    @Test
    public void testCheckInvoiceSerial_ValidNumber() {
        // 测试有效的8位发票号码
        InvoiceSerialCheckResult result = bizFpService.checkInvoiceSerial("12345678");
        assertNotNull(result);
        assertNotNull(result.getSerialInvoices());
    }

    @Test
    public void testCheckInvoiceSerial_SerialRange() {
        // 测试连号范围：后三位001-999都算连号
        // 这个测试需要实际的数据库数据支持
        InvoiceSerialCheckResult result = bizFpService.checkInvoiceSerial("12345001");
        assertNotNull(result);
        // 如果数据库中存在12345002-12345999的发票，都应该被识别为连号
    }

    /**
     * 创建测试数据的辅助方法
     */
    private BizFp createTestInvoice(String invoiceNumber, Date kprq) {
        BizFp invoice = new BizFp();
        invoice.setInvoiceNumber(invoiceNumber);
        invoice.setInvoiceCode("123456789012");
        invoice.setKprq(kprq);
        invoice.setBuyName("测试购方");
        invoice.setSalesName("测试销方");
        invoice.setMoney("100.00");
        return invoice;
    }
}
