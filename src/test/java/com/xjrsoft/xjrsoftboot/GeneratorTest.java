package com.xjrsoft.xjrsoftboot;


import cn.hutool.core.bean.BeanUtil;
import com.xjrsoft.XjrSoftApplication;
import com.xjrsoft.common.model.generator.FormConfig;
import com.xjrsoft.module.form.entity.FormTemplate;
import com.xjrsoft.module.form.service.IFormTemplateService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * 代码生成器 测试
 *
 * @Author: tzx
 * @Date: 2022/10/20 10:25
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = XjrSoftApplication.class)
public class GeneratorTest {

    @Autowired
    private IFormTemplateService formTemplateService;


    /**
     * 测试代码生成器json 转  自定义表单功能
     */
    @Test
    public void testGeneratorToForm(){
        FormTemplate formTemplate = formTemplateService.getById(1564536125215977474L);
    }


}
