package com.xjrsoft.xjrsoftboot;

/**
 * @ClassName JSQLParserUtilTest
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/4/23 15:35
 * @Version 1.0
 */

import com.xjrsoft.common.utils.JSQLParserUtil;
import org.junit.Assert;
import org.junit.Test;

import java.util.List;

public class JSQLParserUtilTest {

    @Test
    public void testGetSelectColumn_Normal() {
        String sql = "SELECT id, name, age FROM users";
        List<String> selectedFields = JSQLParserUtil.getSelectColumn(sql);
        Assert.assertEquals(3, selectedFields.size());
        Assert.assertEquals("id", selectedFields.get(0));
        Assert.assertEquals("name", selectedFields.get(1));
        Assert.assertEquals("age", selectedFields.get(2));
    }

    @Test
    public void testGetSelectColumn_WithTableAlias() {
        String sql = "SELECT a.id, b.name, c.age FROM users a JOIN orders b ON a.id=b.user_id LEFT OUTER JOIN details c ON a.id=c.user_id";
        List<String> selectedFields = JSQLParserUtil.getSelectColumn(sql);
        Assert.assertEquals(3, selectedFields.size());
        Assert.assertEquals("a.id", selectedFields.get(0));
        Assert.assertEquals("b.name", selectedFields.get(1));
        Assert.assertEquals("c.age", selectedFields.get(2));
    }

    @Test
    public void testGetSelectColumn_WithExpression() {
        String sql = "SELECT CONCAT(first_name, ' ', last_name) AS full_name, salary + bonus AS total_pay FROM employees";
        List<String> selectedFields = JSQLParserUtil.getSelectColumn(sql);
        Assert.assertEquals(2, selectedFields.size());
        Assert.assertEquals("full_name", selectedFields.get(0));
        Assert.assertEquals("total_pay", selectedFields.get(1));
    }

    @Test
    public void testGetSelectColumn_WithFunction() {
        String sql = "SELECT COUNT(id) AS count, AVG(salary) AS avg_salary FROM employees";
        List<String> selectedFields = JSQLParserUtil.getSelectColumn(sql);
        Assert.assertEquals(2, selectedFields.size());
        Assert.assertEquals("count", selectedFields.get(0));
        Assert.assertEquals("avg_salary", selectedFields.get(1));
    }

    @Test
    public void testGetSelectColumn_AllColumns() {
        String sql = "SELECT * FROM employees";
        List<String> selectedFields = JSQLParserUtil.getSelectColumn(sql);
        Assert.assertEquals(1, selectedFields.size());
        Assert.assertEquals("*", selectedFields.get(0));
    }
    @Test
    public void testGetSelectColumn_AllColumns2() {
        String sql = "SELECT t1.a  FROM employees";
        List<String> selectedFields = JSQLParserUtil.getSelectColumn(sql);
        Assert.assertEquals(1, selectedFields.size());
        Assert.assertEquals("*", selectedFields.get(0));
    }

    @Test
    public void testGetSelectColumn_Subquery() {
        String sql = "SELECT name, (SELECT COUNT(*) FROM orders WHERE employee_id = e.id) AS order_count FROM employees e";
        List<String> selectedFields = JSQLParserUtil.getSelectColumn(sql);
        Assert.assertEquals(2, selectedFields.size());
        Assert.assertEquals("name", selectedFields.get(0));
        Assert.assertEquals("order_count", selectedFields.get(1));
    }
}
