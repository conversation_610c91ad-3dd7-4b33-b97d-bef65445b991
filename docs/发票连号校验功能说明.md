# 发票连号校验功能说明

## 功能概述

发票连号校验功能用于检测指定发票号码是否存在连号发票，帮助识别可能的重复或连续开票情况。

## 校验规则

1. **发票号码长度限制**：只校对8位、10位、20位数的发票号码
2. **连号范围**：发票尾数三位在001-999范围内都算是连号（排除000）
3. **前缀匹配**：发票号码前面位数一样且尾号后三位不一样则开始校对是否连号
4. **日期范围**：发票日期前后各7天内来判断发票是否连号

## API接口

### 发票连号校验

**接口地址**：`GET /form/fp/checkInvoiceSerial`

**请求参数**：
- `invoiceNumber`：发票号码（必填）

**响应示例**：
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "hasSerial": true,
    "message": "发现2张连号发票",
    "serialInvoices": [
      {
        "id": "1234567890",
        "invoiceCode": "123456789012",
        "invoiceNumber": "12345679",
        "kprq": "2024-01-15T00:00:00.000+00:00",
        "buyName": "购方名称",
        "salesName": "销方名称",
        "money": "100.00",
        "serialType": "AFTER",
        "numberDiff": 1
      },
      {
        "id": "1234567891",
        "invoiceCode": "123456789012",
        "invoiceNumber": "12345677",
        "kprq": "2024-01-14T00:00:00.000+00:00",
        "buyName": "购方名称",
        "salesName": "销方名称",
        "money": "200.00",
        "serialType": "BEFORE",
        "numberDiff": -1
      }
    ]
  }
}
```

## 字段说明

### InvoiceSerialCheckResult（连号校验结果）

| 字段名 | 类型 | 说明 |
|--------|------|------|
| hasSerial | boolean | 是否存在连号 |
| message | String | 校验消息 |
| serialInvoices | List | 连号发票列表 |

### SerialInvoiceInfo（连号发票信息）

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | String | 发票ID |
| invoiceCode | String | 发票代码 |
| invoiceNumber | String | 发票号码 |
| kprq | Date | 开票日期 |
| buyName | String | 购方名称 |
| salesName | String | 销方名称 |
| money | String | 金额 |
| serialType | String | 连号类型：BEFORE-前连号，AFTER-后连号 |
| numberDiff | int | 与目标发票的号码差值 |

## 使用示例

### 1. 检查8位发票号码连号

```bash
curl -X GET "http://localhost:8080/form/fp/checkInvoiceSerial?invoiceNumber=12345678"
```

### 2. 检查10位发票号码连号

```bash
curl -X GET "http://localhost:8080/form/fp/checkInvoiceSerial?invoiceNumber=1234567890"
```

### 3. 检查20位发票号码连号

```bash
curl -X GET "http://localhost:8080/form/fp/checkInvoiceSerial?invoiceNumber=12345678901234567890"
```

## 错误处理

系统会对以下情况进行校验并返回相应错误信息：

1. **发票号码为空**：返回"发票号码不能为空"
2. **发票号码长度不符**：返回"发票号码长度不符合要求，只支持8位、10位、20位发票号码"
3. **发票号码非数字**：返回"发票号码必须为纯数字"
4. **发票不存在**：返回"未找到该发票或发票开票日期为空"
5. **系统异常**：返回"连号校验异常：[具体错误信息]"

## 注意事项

1. 发票必须已存在于系统中且有开票日期，否则无法进行连号校验
2. 连号校验基于发票号码的最后3位数字进行比较
3. 只有相同长度的发票号码才会进行连号比较
4. 日期范围限制为目标发票开票日期前后各7天
5. 连号范围为发票号码最后3位数字在001-999范围内（排除000）
