{"code": "200", "info": "success", "data": {"pageNo": 1, "pageSize": 100, "count": 9, "list": [{"id": "2b853aaee81749d0b3f352d98f482f96", "isNewRecord": false, "condition": "and y.TYPE = 'XCCSQ'  and y.COMPANY_CODE = @svCoCode@ and EXISTS (select 1 from act_ru_task task  where  task.ASSIGNEE_ = @svUserId@ and  task.PROC_INST_ID_ = PROC_INST_ID)", "createBy": {"id": "1", "isNewRecord": false, "delFlag": "0", "loginFlag": "1", "roleList": [], "ownRoles": [], "loginFailTimes": 0, "officeIdList": [], "roleIdList": [], "roleNames": "", "ownRoleIdList": [], "admin": true, "keywordCondition": 0}, "createDate": "2020-04-10 09:28:39", "updateBy": {"id": "1", "isNewRecord": false, "delFlag": "0", "loginFlag": "1", "roleList": [], "ownRoles": [], "loginFailTimes": 0, "officeIdList": [], "roleIdList": [], "roleNames": "", "ownRoleIdList": [], "admin": true, "keywordCondition": 0}, "updateDate": "2023-08-28 01:35:06", "delFlag": "0", "schemeinfoId": "561fc1911ee743159fa393ef093c7854", "num": "1", "name": "待办", "pageSize": "100", "sort": "CREATE_DATE , ROW_ID asc", "show": "1", "dataSourceId": "860c0220983a4124aed6dc3d321d597d", "dbclick": "4", "btnNum": 5, "datasource": {"isNewRecord": false, "delFlag": "0", "keywordCondition": 0, "F_Id": "860c0220983a4124aed6dc3d321d597d", "F_Code": "XCCSQ", "F_Name": "新出差申请", "F_Sql": "select y.*,(case when  instr(TRAFFIC_MODEL, '飞机') > 0  then '是' else '否' end) IS_ATTR1 from (\nSELECT\n  (select TO_CHAR(MAX(AHA.END_TIME_),'YYYY-MM-DD') FROM ACT_HI_ACTINST AHA WHERE AHA.PROC_INST_ID_=A.PROC_INST_ID AND AHA.END_TIME_ IS NOT NULL) AS audit_time,\n  a.ROW_ID,\n  a.CO_CODE,\n  A.BILL_NO,\n  A.company_code,\n  A.wf_status,\n  a.type,\n  a.create_by,\n  k.SUBJECT,\n  o.name OFFICENAME,\n  --NVL(LENGTHB(TRANSLATE(PARTICIPANT,','||PARTICIPANT,',')),0)+1 as PERSON_NUM ,\n  k.START_DATE,\n  k.END_DATE,\n  a.c_attr1 as DEPARTURE_CITY,\n  a.c_attr2 as ARRIVE_CITY,\n  (select listagg(b.label, ',') within group(order by b.value)\n          from sys_dict b\n         where type='cc_traffic_model'  and instr(',' || k.cxfs || ',', ',' || b.value || ',') > 0) TRAFFIC_MODEL,\n  (select NAME from MA_PERSON where a.APPLICANT=ROW_ID) APPLICANT,\n  a.CREATE_DATE,\n  k.c_attr1 c_attr6,\n\tk.fjc_attr1 fjc_attr6,\n  A.IS_ATTR8,\n  A.IS_SUM_BILL,\n  A.SUM_BILL_NO,\n  c.bill_id,\n  (SELECT LABEL from SYS_DICT where type='LC_CCBX' and DEL_FLAG='0' and value= a.wf_status) wf_status1,\n  DECODE(SUBSTR(temp_ahc.MESSAGE_, 1, 4), '[驳回]', '驳回', '[追回]', '追回', '待办') as prev_operation,\n  a.IS_TEMPORARY,\n  A.PROC_INST_ID\nFROM\n  ZY_AR_APPLY a \n      LEFT JOIN (SELECT MESSAGE_, PROC_INST_ID_\n  FROM\n    ( SELECT AHC.MESSAGE_, AHC.PROC_INST_ID_, row_number () over \n    ( partition BY AHC.PROC_INST_ID_ ORDER BY AHC.TIME_ DESC ) px FROM ACT_HI_COMMENT AHC ) \n  WHERE\n\nPX = 1 \n  ) temp_ahc ON a.PROC_INST_ID = temp_ahc.PROC_INST_ID_ \nLEFT JOIN SYS_OFFICE o on a.co_code = o.code LEFT JOIN sys_user u on u.id = a.create_by left join zy_ar_apply_cxxx k on a.row_id = k.apply_id  left join zy_ar_bill c on c.apply_id=a.row_id\nWHERE\n  1 = 1-- and a.year = @svNd@ \n @dsf('o','u','2')@\n) y where 1=1 "}, "zyFormListButtonList": [], "zyFormListFieldList": [], "zyFormListQueryList": [], "params": "[{\"code\":\"CUSTOM_TITLE_CSS\",\"name\":\"弹出编辑页标题CSS\",\"value\":\"font-size:24px;text-align:center\",\"val\":\"文字过多,请编辑查看\"},{\"code\":\"GRID_BORDER\",\"name\":\"表格边框\",\"value\":\"true\",\"enable\":\"1\",\"val\":\"true\"},{\"code\":\"CUSTOM_TITLE\",\"name\":\"自定义弹出编辑页标题\",\"value\":\"出差申请表\",\"enable\":\"1\"}]", "keywordCondition": 0}, {"id": "cf81e012b8a94e59ad46fc3e032c1e45", "isNewRecord": false, "condition": "and y.TYPE = 'XCCSQ' and y.PROC_INST_ID is null  and y.COMPANY_CODE = @svCoCode@ and y.wf_status ='0' and y.create_by =@svUserRealId@", "createBy": {"id": "1", "isNewRecord": false, "delFlag": "0", "loginFlag": "1", "roleList": [], "ownRoles": [], "loginFailTimes": 0, "officeIdList": [], "roleIdList": [], "roleNames": "", "ownRoleIdList": [], "admin": true, "keywordCondition": 0}, "createDate": "2020-04-09 15:54:40", "updateBy": {"id": "1", "isNewRecord": false, "delFlag": "0", "loginFlag": "1", "roleList": [], "ownRoles": [], "loginFailTimes": 0, "officeIdList": [], "roleIdList": [], "roleNames": "", "ownRoleIdList": [], "admin": true, "keywordCondition": 0}, "updateDate": "2023-08-25 07:04:04", "delFlag": "0", "schemeinfoId": "561fc1911ee743159fa393ef093c7854", "num": "2", "name": "新建", "pageSize": "30", "sort": "CREATE_DATE asc", "show": "1", "dataSourceId": "860c0220983a4124aed6dc3d321d597d", "dbclick": "1", "btnNum": 5, "datasource": {"isNewRecord": false, "delFlag": "0", "keywordCondition": 0, "F_Id": "860c0220983a4124aed6dc3d321d597d", "F_Code": "XCCSQ", "F_Name": "新出差申请", "F_Sql": "select y.*,(case when  instr(TRAFFIC_MODEL, '飞机') > 0  then '是' else '否' end) IS_ATTR1 from (\nSELECT\n  (select TO_CHAR(MAX(AHA.END_TIME_),'YYYY-MM-DD') FROM ACT_HI_ACTINST AHA WHERE AHA.PROC_INST_ID_=A.PROC_INST_ID AND AHA.END_TIME_ IS NOT NULL) AS audit_time,\n  a.ROW_ID,\n  a.CO_CODE,\n  A.BILL_NO,\n  A.company_code,\n  A.wf_status,\n  a.type,\n  a.create_by,\n  k.SUBJECT,\n  o.name OFFICENAME,\n  --NVL(LENGTHB(TRANSLATE(PARTICIPANT,','||PARTICIPANT,',')),0)+1 as PERSON_NUM ,\n  k.START_DATE,\n  k.END_DATE,\n  a.c_attr1 as DEPARTURE_CITY,\n  a.c_attr2 as ARRIVE_CITY,\n  (select listagg(b.label, ',') within group(order by b.value)\n          from sys_dict b\n         where type='cc_traffic_model'  and instr(',' || k.cxfs || ',', ',' || b.value || ',') > 0) TRAFFIC_MODEL,\n  (select NAME from MA_PERSON where a.APPLICANT=ROW_ID) APPLICANT,\n  a.CREATE_DATE,\n  k.c_attr1 c_attr6,\n\tk.fjc_attr1 fjc_attr6,\n  A.IS_ATTR8,\n  A.IS_SUM_BILL,\n  A.SUM_BILL_NO,\n  c.bill_id,\n  (SELECT LABEL from SYS_DICT where type='LC_CCBX' and DEL_FLAG='0' and value= a.wf_status) wf_status1,\n  DECODE(SUBSTR(temp_ahc.MESSAGE_, 1, 4), '[驳回]', '驳回', '[追回]', '追回', '待办') as prev_operation,\n  a.IS_TEMPORARY,\n  A.PROC_INST_ID\nFROM\n  ZY_AR_APPLY a \n      LEFT JOIN (SELECT MESSAGE_, PROC_INST_ID_\n  FROM\n    ( SELECT AHC.MESSAGE_, AHC.PROC_INST_ID_, row_number () over \n    ( partition BY AHC.PROC_INST_ID_ ORDER BY AHC.TIME_ DESC ) px FROM ACT_HI_COMMENT AHC ) \n  WHERE\n\nPX = 1 \n  ) temp_ahc ON a.PROC_INST_ID = temp_ahc.PROC_INST_ID_ \nLEFT JOIN SYS_OFFICE o on a.co_code = o.code LEFT JOIN sys_user u on u.id = a.create_by left join zy_ar_apply_cxxx k on a.row_id = k.apply_id  left join zy_ar_bill c on c.apply_id=a.row_id\nWHERE\n  1 = 1-- and a.year = @svNd@ \n @dsf('o','u','2')@\n) y where 1=1 "}, "zyFormListButtonList": [], "zyFormListFieldList": [], "zyFormListQueryList": [], "params": "[{\"code\":\"CUSTOM_TITLE_CSS\",\"name\":\"弹出编辑页标题CSS\",\"value\":\"font-size:24px;text-align:center\",\"val\":\"文字过多,请编辑查看\"},{\"code\":\"GRID_BORDER\",\"name\":\"表格边框\",\"value\":\"true\",\"enable\":\"1\",\"val\":\"true\"},{\"code\":\"CUSTOM_TITLE\",\"name\":\"自定义弹出编辑页标题\",\"value\":\"出差申请表\",\"enable\":\"1\"}]", "keywordCondition": 0}, {"id": "eda7cbb809df48e88805b209ce1ee0e9", "isNewRecord": false, "condition": "and y.TYPE = 'XCCSQ' and y.COMPANY_CODE = @svCoCode@ and wf_status not in ('999') and exists (select 1 from act_hi_actinst h where  ASSIGNEE_ = @svUserId@ and  h.PROC_INST_ID_ = PROC_INST_ID and not exists (select 1 from act_ru_task r where h.assignee_ = r.assignee_ and  h.proc_inst_id_ = r.proc_inst_id_ and h.act_id_ = r.task_def_key_))", "createBy": {"id": "1", "isNewRecord": false, "delFlag": "0", "loginFlag": "1", "roleList": [], "ownRoles": [], "loginFailTimes": 0, "officeIdList": [], "roleIdList": [], "roleNames": "", "ownRoleIdList": [], "admin": true, "keywordCondition": 0}, "createDate": "2020-04-10 09:30:01", "updateBy": {"id": "1", "isNewRecord": false, "delFlag": "0", "loginFlag": "1", "roleList": [], "ownRoles": [], "loginFailTimes": 0, "officeIdList": [], "roleIdList": [], "roleNames": "", "ownRoleIdList": [], "admin": true, "keywordCondition": 0}, "updateDate": "2023-08-28 01:35:17", "delFlag": "0", "schemeinfoId": "561fc1911ee743159fa393ef093c7854", "num": "3", "name": "在途", "pageSize": "30", "sort": "CREATE_DATE  , ROW_ID asc", "show": "1", "dataSourceId": "860c0220983a4124aed6dc3d321d597d", "dbclick": "2", "btnNum": 5, "datasource": {"isNewRecord": false, "delFlag": "0", "keywordCondition": 0, "F_Id": "860c0220983a4124aed6dc3d321d597d", "F_Code": "XCCSQ", "F_Name": "新出差申请", "F_Sql": "select y.*,(case when  instr(TRAFFIC_MODEL, '飞机') > 0  then '是' else '否' end) IS_ATTR1 from (\nSELECT\n  (select TO_CHAR(MAX(AHA.END_TIME_),'YYYY-MM-DD') FROM ACT_HI_ACTINST AHA WHERE AHA.PROC_INST_ID_=A.PROC_INST_ID AND AHA.END_TIME_ IS NOT NULL) AS audit_time,\n  a.ROW_ID,\n  a.CO_CODE,\n  A.BILL_NO,\n  A.company_code,\n  A.wf_status,\n  a.type,\n  a.create_by,\n  k.SUBJECT,\n  o.name OFFICENAME,\n  --NVL(LENGTHB(TRANSLATE(PARTICIPANT,','||PARTICIPANT,',')),0)+1 as PERSON_NUM ,\n  k.START_DATE,\n  k.END_DATE,\n  a.c_attr1 as DEPARTURE_CITY,\n  a.c_attr2 as ARRIVE_CITY,\n  (select listagg(b.label, ',') within group(order by b.value)\n          from sys_dict b\n         where type='cc_traffic_model'  and instr(',' || k.cxfs || ',', ',' || b.value || ',') > 0) TRAFFIC_MODEL,\n  (select NAME from MA_PERSON where a.APPLICANT=ROW_ID) APPLICANT,\n  a.CREATE_DATE,\n  k.c_attr1 c_attr6,\n\tk.fjc_attr1 fjc_attr6,\n  A.IS_ATTR8,\n  A.IS_SUM_BILL,\n  A.SUM_BILL_NO,\n  c.bill_id,\n  (SELECT LABEL from SYS_DICT where type='LC_CCBX' and DEL_FLAG='0' and value= a.wf_status) wf_status1,\n  DECODE(SUBSTR(temp_ahc.MESSAGE_, 1, 4), '[驳回]', '驳回', '[追回]', '追回', '待办') as prev_operation,\n  a.IS_TEMPORARY,\n  A.PROC_INST_ID\nFROM\n  ZY_AR_APPLY a \n      LEFT JOIN (SELECT MESSAGE_, PROC_INST_ID_\n  FROM\n    ( SELECT AHC.MESSAGE_, AHC.PROC_INST_ID_, row_number () over \n    ( partition BY AHC.PROC_INST_ID_ ORDER BY AHC.TIME_ DESC ) px FROM ACT_HI_COMMENT AHC ) \n  WHERE\n\nPX = 1 \n  ) temp_ahc ON a.PROC_INST_ID = temp_ahc.PROC_INST_ID_ \nLEFT JOIN SYS_OFFICE o on a.co_code = o.code LEFT JOIN sys_user u on u.id = a.create_by left join zy_ar_apply_cxxx k on a.row_id = k.apply_id  left join zy_ar_bill c on c.apply_id=a.row_id\nWHERE\n  1 = 1-- and a.year = @svNd@ \n @dsf('o','u','2')@\n) y where 1=1 "}, "zyFormListButtonList": [], "zyFormListFieldList": [], "zyFormListQueryList": [], "params": "[{\"code\":\"CUSTOM_TITLE_CSS\",\"name\":\"弹出编辑页标题CSS\",\"value\":\"font-size:24px;text-align:center\",\"val\":\"文字过多,请编辑查看\"},{\"code\":\"GRID_BORDER\",\"name\":\"表格边框\",\"value\":\"true\",\"enable\":\"1\",\"val\":\"true\"},{\"code\":\"CUSTOM_TITLE\",\"name\":\"自定义弹出编辑页标题\",\"value\":\"出差申请表\",\"enable\":\"1\"}]", "keywordCondition": 0}, {"id": "c64a8958a9ef4d899afc10cd9fc7e56e", "isNewRecord": false, "condition": "and y.TYPE = 'XCCSQ' and y.COMPANY_CODE = @svCoCode@ and y.wf_status ='999' and exists (select 1 from act_hi_actinst h where  ASSIGNEE_ = @svUserId@ and  h.PROC_INST_ID_ = PROC_INST_ID and not exists (select 1 from act_ru_task r where h.assignee_ = r.assignee_ and  h.proc_inst_id_ = r.proc_inst_id_ and h.act_id_ = r.task_def_key_))", "createBy": {"id": "1", "isNewRecord": false, "delFlag": "0", "loginFlag": "1", "roleList": [], "ownRoles": [], "loginFailTimes": 0, "officeIdList": [], "roleIdList": [], "roleNames": "", "ownRoleIdList": [], "admin": true, "keywordCondition": 0}, "createDate": "2020-10-29 17:17:43", "updateBy": {"id": "1", "isNewRecord": false, "delFlag": "0", "loginFlag": "1", "roleList": [], "ownRoles": [], "loginFailTimes": 0, "officeIdList": [], "roleIdList": [], "roleNames": "", "ownRoleIdList": [], "admin": true, "keywordCondition": 0}, "updateDate": "2023-08-28 01:35:29", "delFlag": "0", "schemeinfoId": "561fc1911ee743159fa393ef093c7854", "num": "5", "name": "完结", "pageSize": "30", "sort": "CREATE_DATE  , ROW_ID asc", "show": "1", "dataSourceId": "860c0220983a4124aed6dc3d321d597d", "dbclick": "2", "btnNum": 5, "datasource": {"isNewRecord": false, "delFlag": "0", "keywordCondition": 0, "F_Id": "860c0220983a4124aed6dc3d321d597d", "F_Code": "XCCSQ", "F_Name": "新出差申请", "F_Sql": "select y.*,(case when  instr(TRAFFIC_MODEL, '飞机') > 0  then '是' else '否' end) IS_ATTR1 from (\nSELECT\n  (select TO_CHAR(MAX(AHA.END_TIME_),'YYYY-MM-DD') FROM ACT_HI_ACTINST AHA WHERE AHA.PROC_INST_ID_=A.PROC_INST_ID AND AHA.END_TIME_ IS NOT NULL) AS audit_time,\n  a.ROW_ID,\n  a.CO_CODE,\n  A.BILL_NO,\n  A.company_code,\n  A.wf_status,\n  a.type,\n  a.create_by,\n  k.SUBJECT,\n  o.name OFFICENAME,\n  --NVL(LENGTHB(TRANSLATE(PARTICIPANT,','||PARTICIPANT,',')),0)+1 as PERSON_NUM ,\n  k.START_DATE,\n  k.END_DATE,\n  a.c_attr1 as DEPARTURE_CITY,\n  a.c_attr2 as ARRIVE_CITY,\n  (select listagg(b.label, ',') within group(order by b.value)\n          from sys_dict b\n         where type='cc_traffic_model'  and instr(',' || k.cxfs || ',', ',' || b.value || ',') > 0) TRAFFIC_MODEL,\n  (select NAME from MA_PERSON where a.APPLICANT=ROW_ID) APPLICANT,\n  a.CREATE_DATE,\n  k.c_attr1 c_attr6,\n\tk.fjc_attr1 fjc_attr6,\n  A.IS_ATTR8,\n  A.IS_SUM_BILL,\n  A.SUM_BILL_NO,\n  c.bill_id,\n  (SELECT LABEL from SYS_DICT where type='LC_CCBX' and DEL_FLAG='0' and value= a.wf_status) wf_status1,\n  DECODE(SUBSTR(temp_ahc.MESSAGE_, 1, 4), '[驳回]', '驳回', '[追回]', '追回', '待办') as prev_operation,\n  a.IS_TEMPORARY,\n  A.PROC_INST_ID\nFROM\n  ZY_AR_APPLY a \n      LEFT JOIN (SELECT MESSAGE_, PROC_INST_ID_\n  FROM\n    ( SELECT AHC.MESSAGE_, AHC.PROC_INST_ID_, row_number () over \n    ( partition BY AHC.PROC_INST_ID_ ORDER BY AHC.TIME_ DESC ) px FROM ACT_HI_COMMENT AHC ) \n  WHERE\n\nPX = 1 \n  ) temp_ahc ON a.PROC_INST_ID = temp_ahc.PROC_INST_ID_ \nLEFT JOIN SYS_OFFICE o on a.co_code = o.code LEFT JOIN sys_user u on u.id = a.create_by left join zy_ar_apply_cxxx k on a.row_id = k.apply_id  left join zy_ar_bill c on c.apply_id=a.row_id\nWHERE\n  1 = 1-- and a.year = @svNd@ \n @dsf('o','u','2')@\n) y where 1=1 "}, "zyFormListButtonList": [], "zyFormListFieldList": [], "zyFormListQueryList": [], "params": "[{\"code\":\"CUSTOM_TITLE_CSS\",\"name\":\"弹出编辑页标题CSS\",\"value\":\"font-size:24px;text-align:center\",\"val\":\"文字过多,请编辑查看\"},{\"code\":\"GRID_BORDER\",\"name\":\"表格边框\",\"value\":\"true\",\"enable\":\"1\",\"val\":\"true\"},{\"code\":\"CUSTOM_TITLE\",\"name\":\"自定义弹出编辑页标题\",\"value\":\"出差申请表\",\"enable\":\"1\"}]", "keywordCondition": 0}, {"id": "61aac3142410409e91f43b1db383845d", "isNewRecord": false, "condition": "and y.TYPE in ('XCCSQ') and y.COMPANY_CODE = @svCoCode@  and ('xg1'='xg1' and wf_status !='998' and wf_status !='-2' and wf_status !='0')", "createBy": {"id": "1", "isNewRecord": false, "delFlag": "0", "loginFlag": "1", "roleList": [], "ownRoles": [], "loginFailTimes": 0, "officeIdList": [], "roleIdList": [], "roleNames": "", "ownRoleIdList": [], "admin": true, "keywordCondition": 0}, "createDate": "2020-04-23 11:05:11", "updateBy": {"id": "1", "isNewRecord": false, "delFlag": "0", "loginFlag": "1", "roleList": [], "ownRoles": [], "loginFailTimes": 0, "officeIdList": [], "roleIdList": [], "roleNames": "", "ownRoleIdList": [], "admin": true, "keywordCondition": 0}, "updateDate": "2023-08-28 01:35:42", "delFlag": "0", "schemeinfoId": "561fc1911ee743159fa393ef093c7854", "num": "6", "name": "全部", "pageSize": "100", "sort": "CREATE_DATE  , ROW_ID asc", "show": "1", "dataSourceId": "1d9051989daa487c86b60c929ae9b050", "dbclick": "2", "btnNum": 5, "datasource": {"isNewRecord": false, "delFlag": "0", "keywordCondition": 0, "F_Id": "1d9051989daa487c86b60c929ae9b050", "F_Code": "XCCSQQB", "F_Name": "新出差申请（全部）", "F_Sql": "select y.*\n  from (SELECT a.ROW_ID,\n               a.CO_CODE,\n               A.<PERSON>_<PERSON>,\n               A.company_code,\n               A.wf_status,\n               a.type,\n               a.create_by,\n               k.SUBJECT,\n               o.name OFFICENAME,\n               --NVL(LENGTHB(TRANSLATE(PARTICIPANT,','||PARTICIPANT,',')),0)+1 as PERSON_NUM ,\n               k.START_DATE,\n               k.END_DATE,\n\t\t\t\t         a.c_attr1,\n\t\t\t\t         a.c_attr2,\n               (select listagg(b.label, ',') within group(order by b.value)\n                  from sys_dict b\n                 where type = 'cc_traffic_model'\n                   and instr(',' || k.cxfs || ',', ',' || b.value || ',') > 0) TRAFFIC_MODEL,\n               (select NAME from MA_PERSON where a.APPLICANT = ROW_ID) APPLICANT,\n               a.CREATE_DATE,\n               k.c_attr1 c_attr6,\n\t\t\t\t          k.fjc_attr1 fjc_attr6,\n               <PERSON><PERSON><PERSON>_ATTR8,\n               <PERSON><PERSON><PERSON><PERSON>_<PERSON>,\n               <PERSON><PERSON><PERSON><PERSON>_<PERSON>ILL_<PERSON>,\n               c.bill_id,\n               k.c_attr6 as sjchm,\n               k.c_attr7 as wdwhm,\n               k.c_attr8 as gwchm,\n               (SELECT LABEL\n                  from SYS_DICT\n                 where type = 'LC_CCBX'\n                   and DEL_FLAG = '0'\n                   and value = a.wf_status) wf_status1,\n               a.IS_TEMPORARY,\n               A.PROC_INST_ID\n          FROM ZY_AR_APPLY a\n          LEFT JOIN SYS_OFFICE o\n            on a.co_code = o.code\n          LEFT JOIN sys_user u\n            on u.id = a.create_by\n          left join zy_ar_apply_cxxx k\n            on a.row_id = k.apply_id\n          left join zy_ar_bill c\n            on c.apply_id = a.row_id\n         WHERE 1 = 1\n          /*and a.year = @svNd@*/\n\t\t\t\t@dsf('o', 'u', '2') @ ) y\n where 1 = 1\n"}, "zyFormListButtonList": [], "zyFormListFieldList": [], "zyFormListQueryList": [], "params": "[{\"code\":\"CUSTOM_TITLE_CSS\",\"name\":\"弹出编辑页标题CSS\",\"value\":\"font-size:24px;text-align:center\",\"val\":\"文字过多,请编辑查看\"},{\"code\":\"GRID_BORDER\",\"name\":\"表格边框\",\"value\":\"true\",\"enable\":\"1\",\"val\":\"true\"},{\"code\":\"CUSTOM_TITLE\",\"name\":\"自定义弹出编辑页标题\",\"value\":\"出差申请表\",\"enable\":\"1\"}]", "keywordCondition": 0}, {"id": "41930a3258e8441e99b0e38da60ca648", "isNewRecord": false, "condition": "and y.TYPE = 'XCCSQ' and y.COMPANY_CODE = @svCoCode@ and y.wf_status ='998' and y.create_by =@svUserRealId@", "createBy": {"id": "1", "isNewRecord": false, "delFlag": "0", "loginFlag": "1", "roleList": [], "ownRoles": [], "loginFailTimes": 0, "officeIdList": [], "roleIdList": [], "roleNames": "", "ownRoleIdList": [], "admin": true, "keywordCondition": 0}, "createDate": "2020-04-28 14:44:44", "updateBy": {"id": "1", "isNewRecord": false, "delFlag": "0", "loginFlag": "1", "roleList": [], "ownRoles": [], "loginFailTimes": 0, "officeIdList": [], "roleIdList": [], "roleNames": "", "ownRoleIdList": [], "admin": true, "keywordCondition": 0}, "updateDate": "2023-08-28 01:35:52", "delFlag": "0", "schemeinfoId": "561fc1911ee743159fa393ef093c7854", "num": "20", "name": "作废", "pageSize": "30", "sort": "CREATE_DATE asc", "show": "1", "dataSourceId": "860c0220983a4124aed6dc3d321d597d", "dbclick": "2", "btnNum": 5, "datasource": {"isNewRecord": false, "delFlag": "0", "keywordCondition": 0, "F_Id": "860c0220983a4124aed6dc3d321d597d", "F_Code": "XCCSQ", "F_Name": "新出差申请", "F_Sql": "select y.*,(case when  instr(TRAFFIC_MODEL, '飞机') > 0  then '是' else '否' end) IS_ATTR1 from (\nSELECT\n  (select TO_CHAR(MAX(AHA.END_TIME_),'YYYY-MM-DD') FROM ACT_HI_ACTINST AHA WHERE AHA.PROC_INST_ID_=A.PROC_INST_ID AND AHA.END_TIME_ IS NOT NULL) AS audit_time,\n  a.ROW_ID,\n  a.CO_CODE,\n  A.BILL_NO,\n  A.company_code,\n  A.wf_status,\n  a.type,\n  a.create_by,\n  k.SUBJECT,\n  o.name OFFICENAME,\n  --NVL(LENGTHB(TRANSLATE(PARTICIPANT,','||PARTICIPANT,',')),0)+1 as PERSON_NUM ,\n  k.START_DATE,\n  k.END_DATE,\n  a.c_attr1 as DEPARTURE_CITY,\n  a.c_attr2 as ARRIVE_CITY,\n  (select listagg(b.label, ',') within group(order by b.value)\n          from sys_dict b\n         where type='cc_traffic_model'  and instr(',' || k.cxfs || ',', ',' || b.value || ',') > 0) TRAFFIC_MODEL,\n  (select NAME from MA_PERSON where a.APPLICANT=ROW_ID) APPLICANT,\n  a.CREATE_DATE,\n  k.c_attr1 c_attr6,\n\tk.fjc_attr1 fjc_attr6,\n  A.IS_ATTR8,\n  A.IS_SUM_BILL,\n  A.SUM_BILL_NO,\n  c.bill_id,\n  (SELECT LABEL from SYS_DICT where type='LC_CCBX' and DEL_FLAG='0' and value= a.wf_status) wf_status1,\n  DECODE(SUBSTR(temp_ahc.MESSAGE_, 1, 4), '[驳回]', '驳回', '[追回]', '追回', '待办') as prev_operation,\n  a.IS_TEMPORARY,\n  A.PROC_INST_ID\nFROM\n  ZY_AR_APPLY a \n      LEFT JOIN (SELECT MESSAGE_, PROC_INST_ID_\n  FROM\n    ( SELECT AHC.MESSAGE_, AHC.PROC_INST_ID_, row_number () over \n    ( partition BY AHC.PROC_INST_ID_ ORDER BY AHC.TIME_ DESC ) px FROM ACT_HI_COMMENT AHC ) \n  WHERE\n\nPX = 1 \n  ) temp_ahc ON a.PROC_INST_ID = temp_ahc.PROC_INST_ID_ \nLEFT JOIN SYS_OFFICE o on a.co_code = o.code LEFT JOIN sys_user u on u.id = a.create_by left join zy_ar_apply_cxxx k on a.row_id = k.apply_id  left join zy_ar_bill c on c.apply_id=a.row_id\nWHERE\n  1 = 1-- and a.year = @svNd@ \n @dsf('o','u','2')@\n) y where 1=1 "}, "zyFormListButtonList": [], "zyFormListFieldList": [], "zyFormListQueryList": [], "params": "[{\"code\":\"CUSTOM_TITLE_CSS\",\"name\":\"弹出编辑页标题CSS\",\"value\":\"font-size:24px;text-align:center\",\"val\":\"文字过多,请编辑查看\"},{\"code\":\"GRID_BORDER\",\"name\":\"表格边框\",\"value\":\"true\",\"enable\":\"1\",\"val\":\"true\"},{\"code\":\"CUSTOM_TITLE\",\"name\":\"自定义弹出编辑页标题\",\"value\":\"出差申请表\",\"enable\":\"1\"}]", "keywordCondition": 0}, {"id": "feee71c8be934ddda2bae947fde04269", "isNewRecord": false, "condition": "and y.TYPE = 'XCCSQ' and y.CO_CODE = @svOrgCode@  and  WF_STATUS=999 and IS_SUM_BILL=0 and (SUM_BILL_NO is null or SUM_BILL_NO='') and not exists(select * from zy_ar_bill b where y.row_id = b.apply_id and b.wf_status!=998)", "remarks": "and y.TYPE = 'XCCSQ' and y.CO_CODE = @svOrgCode@  and  WF_STATUS=999 and IS_SUM_BILL=0 and (SUM_BILL_NO is null or SUM_BILL_NO='') and not exists(select * from zy_ar_bill b where y.row_id = b.apply_id and b.wf_status!=998)", "createBy": {"id": "1", "isNewRecord": false, "delFlag": "0", "loginFlag": "1", "roleList": [], "ownRoles": [], "loginFailTimes": 0, "officeIdList": [], "roleIdList": [], "roleNames": "", "ownRoleIdList": [], "admin": true, "keywordCondition": 0}, "createDate": "2021-10-12 17:00:31", "updateBy": {"id": "1", "isNewRecord": false, "delFlag": "0", "loginFlag": "1", "roleList": [], "ownRoles": [], "loginFailTimes": 0, "officeIdList": [], "roleIdList": [], "roleNames": "", "ownRoleIdList": [], "admin": true, "keywordCondition": 0}, "updateDate": "2023-08-28 01:36:03", "delFlag": "0", "schemeinfoId": "561fc1911ee743159fa393ef093c7854", "num": "100", "name": "未合并", "pageSize": "30", "sort": "CREATE_DATE  , ROW_ID asc", "show": "1", "dataSourceId": "1d9051989daa487c86b60c929ae9b050", "dbclick": "2", "btnNum": 5, "datasource": {"isNewRecord": false, "delFlag": "0", "keywordCondition": 0, "F_Id": "1d9051989daa487c86b60c929ae9b050", "F_Code": "XCCSQQB", "F_Name": "新出差申请（全部）", "F_Sql": "select y.*\n  from (SELECT a.ROW_ID,\n               a.CO_CODE,\n               A.<PERSON>_<PERSON>,\n               A.company_code,\n               A.wf_status,\n               a.type,\n               a.create_by,\n               k.SUBJECT,\n               o.name OFFICENAME,\n               --NVL(LENGTHB(TRANSLATE(PARTICIPANT,','||PARTICIPANT,',')),0)+1 as PERSON_NUM ,\n               k.START_DATE,\n               k.END_DATE,\n\t\t\t\t         a.c_attr1,\n\t\t\t\t         a.c_attr2,\n               (select listagg(b.label, ',') within group(order by b.value)\n                  from sys_dict b\n                 where type = 'cc_traffic_model'\n                   and instr(',' || k.cxfs || ',', ',' || b.value || ',') > 0) TRAFFIC_MODEL,\n               (select NAME from MA_PERSON where a.APPLICANT = ROW_ID) APPLICANT,\n               a.CREATE_DATE,\n               k.c_attr1 c_attr6,\n\t\t\t\t          k.fjc_attr1 fjc_attr6,\n               <PERSON><PERSON><PERSON>_ATTR8,\n               <PERSON><PERSON><PERSON><PERSON>_<PERSON>,\n               <PERSON><PERSON><PERSON><PERSON>_<PERSON>ILL_<PERSON>,\n               c.bill_id,\n               k.c_attr6 as sjchm,\n               k.c_attr7 as wdwhm,\n               k.c_attr8 as gwchm,\n               (SELECT LABEL\n                  from SYS_DICT\n                 where type = 'LC_CCBX'\n                   and DEL_FLAG = '0'\n                   and value = a.wf_status) wf_status1,\n               a.IS_TEMPORARY,\n               A.PROC_INST_ID\n          FROM ZY_AR_APPLY a\n          LEFT JOIN SYS_OFFICE o\n            on a.co_code = o.code\n          LEFT JOIN sys_user u\n            on u.id = a.create_by\n          left join zy_ar_apply_cxxx k\n            on a.row_id = k.apply_id\n          left join zy_ar_bill c\n            on c.apply_id = a.row_id\n         WHERE 1 = 1\n          /*and a.year = @svNd@*/\n\t\t\t\t@dsf('o', 'u', '2') @ ) y\n where 1 = 1\n"}, "zyFormListButtonList": [], "zyFormListFieldList": [], "zyFormListQueryList": [], "params": "[{\"code\":\"CUSTOM_TITLE_CSS\",\"name\":\"弹出编辑页标题CSS\",\"value\":\"font-size:24px;text-align:center\",\"val\":\"文字过多,请编辑查看\"},{\"code\":\"GRID_BORDER\",\"name\":\"表格边框\",\"value\":\"true\",\"enable\":\"1\",\"val\":\"true\"},{\"code\":\"CUSTOM_TITLE\",\"name\":\"自定义弹出编辑页标题\",\"value\":\"出差申请表\",\"enable\":\"1\"}]", "keywordCondition": 0}, {"id": "6fe0e10395bf433487ba901ce00edacd", "isNewRecord": false, "condition": "and y.TYPE = 'XCCSQ'  and sum_bill_no is not null", "createBy": {"id": "1", "isNewRecord": false, "delFlag": "0", "loginFlag": "1", "roleList": [], "ownRoles": [], "loginFailTimes": 0, "officeIdList": [], "roleIdList": [], "roleNames": "", "ownRoleIdList": [], "admin": true, "keywordCondition": 0}, "createDate": "2021-10-12 17:00:59", "updateBy": {"id": "1", "isNewRecord": false, "delFlag": "0", "loginFlag": "1", "roleList": [], "ownRoles": [], "loginFailTimes": 0, "officeIdList": [], "roleIdList": [], "roleNames": "", "ownRoleIdList": [], "admin": true, "keywordCondition": 0}, "updateDate": "2023-08-28 01:36:13", "delFlag": "0", "schemeinfoId": "561fc1911ee743159fa393ef093c7854", "num": "120", "name": "已合并", "pageSize": "30", "sort": "CREATE_DATE  , ROW_ID asc", "show": "1", "dataSourceId": "1d9051989daa487c86b60c929ae9b050", "dbclick": "2", "btnNum": 5, "datasource": {"isNewRecord": false, "delFlag": "0", "keywordCondition": 0, "F_Id": "1d9051989daa487c86b60c929ae9b050", "F_Code": "XCCSQQB", "F_Name": "新出差申请（全部）", "F_Sql": "select y.*\n  from (SELECT a.ROW_ID,\n               a.CO_CODE,\n               A.<PERSON>_<PERSON>,\n               A.company_code,\n               A.wf_status,\n               a.type,\n               a.create_by,\n               k.SUBJECT,\n               o.name OFFICENAME,\n               --NVL(LENGTHB(TRANSLATE(PARTICIPANT,','||PARTICIPANT,',')),0)+1 as PERSON_NUM ,\n               k.START_DATE,\n               k.END_DATE,\n\t\t\t\t         a.c_attr1,\n\t\t\t\t         a.c_attr2,\n               (select listagg(b.label, ',') within group(order by b.value)\n                  from sys_dict b\n                 where type = 'cc_traffic_model'\n                   and instr(',' || k.cxfs || ',', ',' || b.value || ',') > 0) TRAFFIC_MODEL,\n               (select NAME from MA_PERSON where a.APPLICANT = ROW_ID) APPLICANT,\n               a.CREATE_DATE,\n               k.c_attr1 c_attr6,\n\t\t\t\t          k.fjc_attr1 fjc_attr6,\n               <PERSON><PERSON><PERSON>_ATTR8,\n               <PERSON><PERSON><PERSON><PERSON>_<PERSON>,\n               <PERSON><PERSON><PERSON><PERSON>_<PERSON>ILL_<PERSON>,\n               c.bill_id,\n               k.c_attr6 as sjchm,\n               k.c_attr7 as wdwhm,\n               k.c_attr8 as gwchm,\n               (SELECT LABEL\n                  from SYS_DICT\n                 where type = 'LC_CCBX'\n                   and DEL_FLAG = '0'\n                   and value = a.wf_status) wf_status1,\n               a.IS_TEMPORARY,\n               A.PROC_INST_ID\n          FROM ZY_AR_APPLY a\n          LEFT JOIN SYS_OFFICE o\n            on a.co_code = o.code\n          LEFT JOIN sys_user u\n            on u.id = a.create_by\n          left join zy_ar_apply_cxxx k\n            on a.row_id = k.apply_id\n          left join zy_ar_bill c\n            on c.apply_id = a.row_id\n         WHERE 1 = 1\n          /*and a.year = @svNd@*/\n\t\t\t\t@dsf('o', 'u', '2') @ ) y\n where 1 = 1\n"}, "zyFormListButtonList": [], "zyFormListFieldList": [], "zyFormListQueryList": [], "params": "[{\"code\":\"CUSTOM_TITLE_CSS\",\"name\":\"弹出编辑页标题CSS\",\"value\":\"font-size:24px;text-align:center\",\"val\":\"文字过多,请编辑查看\"},{\"code\":\"GRID_BORDER\",\"name\":\"表格边框\",\"value\":\"true\",\"enable\":\"1\",\"val\":\"true\"},{\"code\":\"CUSTOM_TITLE\",\"name\":\"自定义弹出编辑页标题\",\"value\":\"出差申请表\",\"enable\":\"1\"}]", "keywordCondition": 0}, {"id": "42d96e257e874bf28ad8da9b4cd37215", "isNewRecord": false, "condition": "and y.TYPE = 'XCCSQ'  and  is_sum_bill='1'", "createBy": {"id": "1", "isNewRecord": false, "delFlag": "0", "loginFlag": "1", "roleList": [], "ownRoles": [], "loginFailTimes": 0, "officeIdList": [], "roleIdList": [], "roleNames": "", "ownRoleIdList": [], "admin": true, "keywordCondition": 0}, "createDate": "2022-01-26 10:07:25", "updateBy": {"id": "1", "isNewRecord": false, "delFlag": "0", "loginFlag": "1", "roleList": [], "ownRoles": [], "loginFailTimes": 0, "officeIdList": [], "roleIdList": [], "roleNames": "", "ownRoleIdList": [], "admin": true, "keywordCondition": 0}, "updateDate": "2023-08-28 01:36:22", "delFlag": "0", "schemeinfoId": "561fc1911ee743159fa393ef093c7854", "num": "130", "name": "合并单据查询", "pageSize": "30", "sort": "BILL_NO, ROW_ID asc", "show": "1", "dataSourceId": "cffa8c8e5a6e4fbe95266a70fcc37cb6", "dbclick": "0", "btnNum": 5, "datasource": {"isNewRecord": false, "delFlag": "0", "keywordCondition": 0, "F_Id": "cffa8c8e5a6e4fbe95266a70fcc37cb6", "F_Code": "HBCX", "F_Name": "合并单据查询", "F_Sql": "select y.* from (\nSELECT\n  a.ROW_ID,\n  a.CO_CODE,\n  A.BILL_NO,\n  a.type,\n  o.name OFFICENAME,\n  (select NAME from MA_PERSON where a.APPLICANT=ROW_ID) APPLICANT,\n  A.IS_SUM_BILL,\n  A.SUM_BILL_NO,\n  c.bill_id,\n  A.PROC_INST_ID\nFROM\n  ZY_AR_APPLY a \nLEFT JOIN SYS_OFFICE o on a.co_code = o.code  left join zy_ar_bill c on c.apply_id=a.row_id\nWHERE\n  1 = 1 --and a.year = @svNd@ \n  @dsf('o','u','2')@\n) y where 1=1 "}, "zyFormListButtonList": [], "zyFormListFieldList": [], "zyFormListQueryList": [], "params": "[{\"code\":\"CUSTOM_TITLE_CSS\",\"name\":\"弹出编辑页标题CSS\",\"value\":\"font-size:24px;text-align:center\",\"val\":\"文字过多,请编辑查看\"},{\"code\":\"GRID_BORDER\",\"name\":\"表格边框\",\"value\":\"true\",\"enable\":\"1\",\"val\":\"true\"},{\"code\":\"CUSTOM_TITLE\",\"name\":\"自定义弹出编辑页标题\",\"value\":\"出差申请表\",\"enable\":\"1\"}]", "keywordCondition": 0}], "totalPage": 1, "html": "<ul>\n<li class=\"disabled\"><a href=\"javascript:\">« 上一页</a></li>\n<li class=\"active\"><a href=\"javascript:\">1</a></li>\n<li class=\"disabled\"><a href=\"javascript:\">下一页 »</a></li>\n<li class=\"disabled controls\"><a href=\"javascript:\">当前 <input type=\"text\" value=\"1\" onkeypress=\"var e=window.event||event;var c=e.keyCode||e.which;if(c==13)page(this.value,100,'');\" onclick=\"this.select();\"/> / <input type=\"text\" value=\"100\" onkeypress=\"var e=window.event||event;var c=e.keyCode||e.which;if(c==13)page(1,this.value,'');\" onclick=\"this.select();\"/> 条，共 9 条</a></li>\n</ul>\n<div style=\"clear:both;\"></div>", "maxResults": 100, "firstResult": 0}}