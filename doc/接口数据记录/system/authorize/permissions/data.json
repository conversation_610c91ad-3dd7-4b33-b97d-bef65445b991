{"msg": "", "code": 0, "data": {"postId": "1419276794949275648", "postName": "董事长", "departmentId": "1419276791701966848", "departmentName": "总部", "menuAuthList": [{"buttonAuthCode": [], "columnAuthCode": [], "formAuthCode": [], "menuId": "1419273699992498188"}, {"buttonAuthCode": ["dic:addItems", "dic:delete", "dic:edit", "dic:addDictionary"], "columnAuthCode": ["item_code", "item_action", "item_remark", "code", "name", "item_name", "remark", "value"], "formAuthCode": ["Item_name", "item_code", "item_sortCode", "item_remark", "name", "code", "value", "sortCode", "remark"], "menuId": "1419276797351038933"}, {"buttonAuthCode": ["user:add", "user:resetPassword", "user:unlock", "menu:edit", "menu:delete"], "columnAuthCode": ["code", "name", "mobile", "action", "remark", "userName", "email"], "formAuthCode": ["userName", "password", "code", "departmentId", "name", "mobile", "email", "remark"], "menuId": "1419276797568356352"}, {"buttonAuthCode": ["department:edit", "department:delete", "department:add"], "columnAuthCode": ["website", "code", "address", "enabledMark", "name", "action", "remark", "email"], "formAuthCode": ["name", "code", "enabledMark", "parentId", "email", "website", "address", "remark"], "menuId": "1419276797720137728"}, {"buttonAuthCode": ["role:view", "role:home<PERSON><PERSON>", "role:edit", "role:add<PERSON><PERSON><PERSON>", "role:delete", "role:functional<PERSON><PERSON>", "role:<PERSON><PERSON><PERSON>", "role:add"], "columnAuthCode": ["code", "enabledMark", "name", "action", "remark"], "formAuthCode": ["name", "code", "enabledMark", "remark"], "menuId": "1419276797849243648"}, {"buttonAuthCode": ["post:add", "post:<PERSON><PERSON><PERSON><PERSON>", "post:edit", "post:delete"], "columnAuthCode": ["code", "enabledMark", "name", "action", "remark"], "formAuthCode": ["name", "code", "enabledMark", "parentId", "remark"], "menuId": "1419276797978742784"}, {"buttonAuthCode": ["menu:add", "menu:edit", "menu:delete"], "columnAuthCode": ["component", "code", "enabledMark", "name", "icon", "action", "remark", "title"], "formAuthCode": ["title", "name", "enabledMark", "code", "sortCode", "parentId", "systemId", "path", "outLink", "icon", "remark", "menuType", "display", "iframeSrc", "component", "componentType"], "menuId": "1419276798102605824"}, {"buttonAuthCode": [], "columnAuthCode": [], "formAuthCode": [], "menuId": "1534704535292010497"}, {"buttonAuthCode": ["design:exportFlow", "design:classifyMgt", "design:edit", "design:add", "design:enable", "design:disable", "design:preview", "design:importFlow", "design:delete", "design:history"], "columnAuthCode": ["code", "enabledMark", "name", "action", "remark", "categoryName"], "formAuthCode": ["keyword"], "menuId": "1539078176788135937"}, {"buttonAuthCode": ["areaManager:delete", "areaManager:add", "areaManager:edit"], "columnAuthCode": ["code", "quickQuery", "name", "simpleSpelling", "action", "remark", "layer"], "formAuthCode": ["code", "name", "simpleSpelling", "quickQuery", "sortCode", "remark"], "menuId": "1541229473335279618"}, {"buttonAuthCode": ["codeRule:edit", "codeRule:add", "codeRule:delete"], "columnAuthCode": ["code", "name", "description", "action", "createUserName", "currentNumber", "createDate"], "formAuthCode": ["code", "name", "description", "sortCode"], "menuId": "1541241284113661954"}, {"buttonAuthCode": ["databaselink:add", "databaselink:delete", "databaselink:edit"], "columnAuthCode": ["dbVersion", "dbN<PERSON>", "host", "dbType", "action"], "formAuthCode": ["dbN<PERSON>", "dbType", "dbVersion", "host", "username", "password"], "menuId": "1550034198272872450"}, {"buttonAuthCode": ["form-design:previewForm", "form-design:delete", "form-design:batchDelete", "form-design:classifyMGT", "form-design:edit", "form-design:add", "form-design:generatedCode"], "columnAuthCode": ["enabledMark", "name", "action", "createUserName", "remark", "categoryName", "createDate"], "formAuthCode": ["name", "category", "remark", "databaseId", "isDataAuth", "dataAuthList"], "menuId": "1564170833587494914"}, {"buttonAuthCode": ["form-release:add", "form-release:edit", "form-release:delete"], "columnAuthCode": ["formName", "action", "menuName", "remark"], "formAuthCode": ["code", "name", "parentId", "formId", "sortCode", "icon", "remark"], "menuId": "1564809725089058817"}, {"buttonAuthCode": [], "columnAuthCode": [], "formAuthCode": [], "menuId": "1586631264210206721"}, {"buttonAuthCode": [], "columnAuthCode": [], "formAuthCode": [], "menuId": "1589823150509592578"}, {"buttonAuthCode": ["processtasks:delete", "processtasks:withdraw", "processtasks:view", "processtasks:approve", "processtasks:edit", "processtasks:batchApproval", "processtasks:relaunch"], "columnAuthCode": ["currentProgress", "serialNumber", "startUserName", "action", "taskName", "startTime", "originator", "schemaName"], "formAuthCode": [], "menuId": "1605102917408903169"}, {"buttonAuthCode": [], "columnAuthCode": [], "formAuthCode": [], "menuId": "1607666932241141762"}, {"buttonAuthCode": ["monitor:pending", "monitor:<PERSON><PERSON><PERSON><PERSON>", "monitor:delete", "monitor:complete", "monitor:active", "monitor:internalTermination", "monitor:view"], "columnAuthCode": ["currentProgress", "serialNumber", "currentTaskName", "originator", "schemaName", "status", "createDate"], "formAuthCode": ["keyword", "searchDate"], "menuId": "1621059005749190657"}, {"buttonAuthCode": ["report-design:edit", "report-design:delete", "report-design:add"], "columnAuthCode": ["modifyDate", "name", "action", "createDate"], "formAuthCode": [], "menuId": "1623841648974790657"}, {"buttonAuthCode": ["report-release:add", "report-release:edit", "report-release:delete"], "columnAuthCode": ["parentName", "code", "reportName", "action", "remark", "title"], "formAuthCode": ["keyword", "code", "title", "parentId", "reportId", "sortCode", "icon", "remark"], "menuId": "1625307788997627905"}, {"buttonAuthCode": ["delegate:add", "delegate:delete", "delegate:edit"], "columnAuthCode": ["delegator", "delegate<PERSON>ser<PERSON><PERSON>s", "action", "startTime", "remark", "endTime", "createDate"], "formAuthCode": ["keyword"], "menuId": "1625315693209653249"}, {"buttonAuthCode": ["PublicStamp:disable", "PublicStamp:delete", "PublicStamp:addPeople", "PublicStamp:enable", "PublicStamp:add", "PublicStamp:designMaintainPersonnel", "PublicStamp:edit", "PublicStamp:defaultStamp"], "columnAuthCode": ["stampCategoryName", "enabledMark", "name", "action", "fileUrl", "remark"], "formAuthCode": ["keyword"], "menuId": "1626497178952368129"}, {"buttonAuthCode": ["PublicStamp:disable", "PublicStamp:delete", "PublicStamp:enable", "PublicStamp:add", "PublicStamp:designMaintainPersonnel", "PublicStamp:edit", "PublicStamp:defaultStamp"], "columnAuthCode": ["stampCategoryName", "isDefault", "enabledMark", "name", "action", "fileUrl", "remark"], "formAuthCode": [], "menuId": "1626497785792659458"}, {"buttonAuthCode": ["index:edit", "index:add", "index:delete", "index:view"], "columnAuthCode": ["modifyUserName", "code", "modifyDate", "name", "action", "createUserName", "authType", "objectId", "createDate"], "formAuthCode": ["keyword", "code", "name", "authType", "objectIdList", "authMethod", "authScope", "remark"], "menuId": "1635086300032897025"}, {"buttonAuthCode": ["languageType:edit", "languageType:add", "languageType:delete"], "columnAuthCode": ["code", "name", "action"], "formAuthCode": ["code", "name"], "menuId": "1636187258410352641"}, {"buttonAuthCode": ["translation:add", "translation:edit", "translation:delete"], "columnAuthCode": ["action"], "formAuthCode": ["keyword"], "menuId": "1636188006506414081"}, {"buttonAuthCode": ["log:batchDelete", "log:empty", "log:export"], "columnAuthCode": ["method", "createTime", "ip", "time", "operation", "username"], "formAuthCode": ["keyword", "createTime"], "menuId": "1642777176880279554"}, {"buttonAuthCode": ["print:edit", "print:disable", "print:delete", "print:classifyMgt", "print:add", "print:enable"], "columnAuthCode": ["code", "enabledMark", "name", "action", "remark"], "formAuthCode": ["name", "code", "enabledMark"], "menuId": "1656114604324503553"}, {"buttonAuthCode": ["subSystem:add", "subSystem:edit", "subSystem:delete"], "columnAuthCode": ["code", "name", "description", "action", "sortCode"], "formAuthCode": ["name", "code", "icon", "description", "sortCode"], "menuId": "1658652295024971778"}, {"buttonAuthCode": ["LiteFlow:add", "LiteFlow:delete", "LiteFlow:edit"], "columnAuthCode": ["chainName", "createTime", "action", "chainDesc", "applicationName"], "formAuthCode": ["applicationName", "chainName", "elData", "chainDesc"], "menuId": "1658725213171929090"}, {"buttonAuthCode": [], "columnAuthCode": [], "formAuthCode": [], "menuId": "1665542245175566337"}, {"buttonAuthCode": [], "columnAuthCode": [], "formAuthCode": [], "menuId": "1665543080395710465"}, {"buttonAuthCode": [], "columnAuthCode": [], "formAuthCode": [], "menuId": "1665543831385841666"}, {"buttonAuthCode": ["desktop:add"], "columnAuthCode": [], "formAuthCode": [], "menuId": "1669548082198065153"}, {"buttonAuthCode": ["desktop:add"], "columnAuthCode": [], "formAuthCode": [], "menuId": "1681187391346905090"}, {"buttonAuthCode": [], "columnAuthCode": [], "formAuthCode": [], "menuId": "1697150517762379778"}, {"buttonAuthCode": ["oaNews:status", "oaNews:view", "oaNews:add", "oaNews:edit", "oaNews:delete"], "columnAuthCode": ["newsContent", "releaseTime", "compileName", "enabledMark", "briefHead", "<PERSON><PERSON><PERSON>", "tagWord", "keyword", "category", "categoryName"], "formAuthCode": ["briefHead", "category", "releaseTime", "<PERSON><PERSON><PERSON>", "compileName", "tagWord", "keyword", "newsContent", "categoryId"], "menuId": "1718795794098167810"}, {"buttonAuthCode": ["oaNotices:edit", "oaNotices:delete", "oaNotices:view", "oaNotices:status", "oaNotices:add"], "columnAuthCode": ["newsContent", "releaseTime", "sourceAddress", "enabledMark", "briefHead", "sourceName", "categoryName"], "formAuthCode": ["briefHead", "category", "releaseTime", "sourceName", "sourceAddress", "newsContent"], "menuId": "1718795794098167811"}, {"buttonAuthCode": ["preview", "report-design:edit", "report-design:delete", "report-design:add"], "columnAuthCode": [], "formAuthCode": ["code", "filePath", "name", "remark", "sortCode", "typeId", "typeValue"], "menuId": "1719596668533944322"}, {"buttonAuthCode": [], "columnAuthCode": [], "formAuthCode": [], "menuId": "1726445152380727297"}, {"buttonAuthCode": [], "columnAuthCode": [], "formAuthCode": [], "menuId": "1726510946900856834"}, {"buttonAuthCode": ["organization:sync"], "columnAuthCode": ["code", "enabledMark", "name"], "formAuthCode": [], "menuId": "1727490076026982401"}, {"buttonAuthCode": ["member:sync"], "columnAuthCode": ["departmentName", "gender", "name", "mobile", "userName"], "formAuthCode": [], "menuId": "1727496563487002626"}, {"buttonAuthCode": ["configuration:setting"], "columnAuthCode": ["code", "name", "departmentType"], "formAuthCode": ["name", "code", "dingAppKey", "DingAppSecret", "dingAgentId"], "menuId": "1727497234319790082"}, {"buttonAuthCode": ["organization:sync"], "columnAuthCode": ["code", "enabledMark", "name", "dingtalkUserId"], "formAuthCode": [], "menuId": "1727497697131876353"}, {"buttonAuthCode": ["user:syncSystem", "user:syncDing"], "columnAuthCode": ["departmentName", "gender", "name", "mobile", "dingtalkUserId", "userName"], "formAuthCode": [], "menuId": "1727498098770038785"}, {"buttonAuthCode": ["oaNews:status", "oaNews:view", "oaNews:add", "oaNews:edit", "oaNews:delete"], "columnAuthCode": ["newsContent", "releaseTime", "compileName", "enabledMark", "briefHead", "<PERSON><PERSON><PERSON>", "tagWord", "keyword", "category", "categoryName"], "formAuthCode": ["briefHead", "category", "releaseTime", "<PERSON><PERSON><PERSON>", "compileName", "tagWord", "keyword", "newsContent", "categoryId"], "menuId": "1729745835096690690"}, {"buttonAuthCode": ["online:logoutSingle", "online:logout"], "columnAuthCode": [], "formAuthCode": [], "menuId": "1731571645817143298"}, {"buttonAuthCode": ["form-design:previewForm", "form-design:delete", "form-design:batchDelete", "form-design:classifyMGT", "form-design:edit", "form-design:add", "form-design:generatedCode"], "columnAuthCode": ["enabledMark", "name", "action", "createUserName", "remark", "categoryName", "createDate"], "formAuthCode": ["name", "category", "remark", "databaseId", "isDataAuth", "dataAuthList"], "menuId": "1768220168192606209"}, {"buttonAuthCode": ["testfunc:add", "testfunc:refresh", "testfunc:view", "testfunc:edit", "testfunc:delete"], "columnAuthCode": ["typeName", "id"], "formAuthCode": [{"caseErpMaterial": ["classesId", "name"]}, "id", "typeName"], "menuId": "1771047773320249346"}, {"buttonAuthCode": ["test:add", "test:startwork", "test:delete", "test:edit", "test:flowRecord", "test:view"], "columnAuthCode": ["secretLevel", "users"], "formAuthCode": ["users", "files", "secretLevel"], "menuId": "1772188260454465538"}, {"buttonAuthCode": ["测试2:delete", "测试2:startwork", "测试2:flowRecord", "测试2:edit", "测试2:refresh", "测试2:view", "测试2:add"], "columnAuthCode": ["shi_jian_fan_wei2789", "dan_xing_wen_ben4467", "shi_jian_fan_wei3757"], "formAuthCode": ["dan_xing_wen_ben4467", "shi_jian_fan_wei2789,shi_jian_fan_wei3757"], "menuId": "1772192917442576385"}, {"buttonAuthCode": ["testdemo1:add", "testdemo1:edit", "testdemo1:view", "testdemo1:delete", "testdemo1:refresh"], "columnAuthCode": ["test2", "test3", "test1"], "formAuthCode": ["test1", "test2", "test3"], "menuId": "1772198942407254018"}, {"buttonAuthCode": ["gsgl:batchdelete", "gsgl:refresh", "gsgl:export", "gsgl:import", "gsgl:view", "gsgl:delete", "gsgl:add", "gsgl:edit", "gsgl:copyData"], "columnAuthCode": ["ming<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "formAuthCode": ["ming<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "menuId": "1772813330298183681"}], "desktopSchema": null}, "success": true}