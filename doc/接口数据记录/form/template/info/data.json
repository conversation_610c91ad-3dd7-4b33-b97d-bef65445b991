{"databaseId": "master", "tableStructureConfigs": [{"operator": 4, "tableName": "gl_zj_gs", "tableComment": null, "isMain": true, "pkField": null, "tableFieldConfigs": [{"fieldName": "gs_name", "fieldType": null, "fieldLength": 50, "fieldComment": "公式名称"}, {"fieldName": "gs_id", "fieldType": null, "fieldLength": 50, "fieldComment": "公式id"}, {"fieldName": "zj_name", "fieldType": null, "fieldLength": 50, "fieldComment": "组件名称"}, {"fieldName": "zj_code", "fieldType": null, "fieldLength": 50, "fieldComment": "组件编码"}]}], "formEventConfig": {"0": [{"type": "circle", "color": "#2774ff", "text": "开始节点", "icon": "#icon-kaishi", "bgcColor": "#D8E5FF", "isUserDefined": false, "isClick": false}, {"color": "#F6AB01", "icon": "#icon-ch<PERSON><PERSON><PERSON>", "text": "初始化表单", "bgcColor": "#f9f5ea", "isUserDefined": false, "nodeInfo": {"processEvent": []}, "isClick": false}, {"color": "#2774FF", "icon": "#icon-yonghu-xianxing", "text": "设置默认值", "bgcColor": "#F5F8FA", "isUserDefined": true, "nodeInfo": {"processEvent": [{"operateType": "js", "operateConfig": "debugger\nif(getBindData)\n{\n  let data = getBindData();\n  formModel.zj_name = data.name;\n  formModel.zj_code = data.uuidCode;\n}", "showValue": "已配置"}]}, "isClick": true}], "1": [{"color": "#B36EDB", "icon": "#icon-shu<PERSON>fen<PERSON>", "text": "获取表单数据", "detail": "(新增无此操作)", "bgcColor": "#F8F2FC", "isUserDefined": false, "nodeInfo": {"processEvent": []}, "isClick": false}], "2": [{"color": "#F8625C", "icon": "#icon-ji<PERSON>ai", "text": "加载表单", "bgcColor": "#FFF1F1", "isUserDefined": false, "nodeInfo": {"processEvent": []}, "isClick": false}], "3": [{"color": "#6C6AE0", "icon": "#icon-j<PERSON><PERSON><PERSON><PERSON>", "text": "提交表单", "bgcColor": "#F5F4FF", "isUserDefined": false, "nodeInfo": {"processEvent": []}, "isClick": false}], "4": [{"type": "circle", "color": "#F8625C", "text": "结束节点", "icon": "#icon-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bgcColor": "#FFD6D6", "isLast": true, "isUserDefined": false, "isClick": false}]}, "tableConfigs": [{"tableName": "gl_zj_gs", "isMain": true, "pkField": "id", "pkType": "<PERSON>", "relationField": null, "relationTableField": null}], "formJson": {"list": [{"label": "公式名称", "type": "associate-popup", "key": "c0b39de871884e3692756c798dcb3ad3", "bindTable": "gl_zj_gs", "bindField": "gs_name", "rules": [], "children": null, "layout": null, "options": {"popupType": "associate", "width": "100%", "span": "", "placeholder": "选择请公式名称", "showLabel": true, "disabled": false, "datasourceType": "api", "labelField": "label", "valueField": "value", "apiConfig": {"path": "/hzhl/gs", "method": "GET", "apiId": "d09f7f0502b148d7a01c7fab2d300920", "apiParams": [{"key": "1", "title": "Query Params", "tableInfo": [{"name": "zjId", "value": "{\"bindField\":\"zj_code\",\"fieldKey\":\"f8cdb51c9db1496e965002b4567bf4b3\"}", "description": null, "required": false, "dataType": null, "type": null, "defaultValue": null, "validateType": null, "error": null, "expression": null, "children": null, "bindType": "data"}]}, {"key": "2", "title": "Header", "tableInfo": []}, {"key": "3", "title": "Body"}], "script": "return db.select('select id as \"value\", name as \"label\" from config_gsgl_data')", "outputParams": [{"name": "value", "tableTitle": "公式id", "bindField": "gs_id", "show": true, "width": 150, "component": "bb0833f5d16043b3a2d3bbd8c828a74d"}, {"name": "label", "tableTitle": "公式名称", "bindField": "", "show": true, "width": 150}]}, "dicOptions": [], "required": true, "rules": [], "events": {}, "isShow": true}, "bindStartTime": null, "bindEndTime": null, "value": null, "code": null, "isSubFormChild": false, "height": null, "position": null, "width": null, "colspan": null, "rowspan": null, "class": null}, {"label": "公式id", "type": "input", "key": "bb0833f5d16043b3a2d3bbd8c828a74d", "bindTable": "gl_zj_gs", "bindField": "gs_id", "rules": [], "children": null, "layout": null, "options": {"width": "100%", "span": "", "defaultValue": "", "placeholder": "请输入公式id", "maxlength": null, "prefix": "", "suffix": "", "addonBefore": "", "addonAfter": "", "disabled": true, "allowClear": false, "showLabel": true, "required": false, "rules": [], "events": {}, "isSave": false, "isShow": true, "scan": false}, "bindStartTime": null, "bindEndTime": null, "value": null, "code": null, "isSubFormChild": false, "height": null, "position": null, "width": null, "colspan": null, "rowspan": null, "class": null}, {"label": "组件名称", "type": "input", "key": "710d0d863e784b1a9f9de39b3072a22a", "bindTable": "gl_zj_gs", "bindField": "zj_name", "rules": [], "children": null, "layout": null, "options": {"width": "100%", "span": "", "defaultValue": "", "placeholder": "请输入组件名称", "maxlength": null, "prefix": "", "suffix": "", "addonBefore": "", "addonAfter": "", "disabled": true, "allowClear": false, "showLabel": true, "required": false, "rules": [], "events": {}, "isSave": false, "isShow": true, "scan": false}, "bindStartTime": null, "bindEndTime": null, "value": null, "code": null, "isSubFormChild": false, "height": null, "position": null, "width": null, "colspan": null, "rowspan": null, "class": null}, {"label": "组件编码", "type": "input", "key": "f8cdb51c9db1496e965002b4567bf4b3", "bindTable": "gl_zj_gs", "bindField": "zj_code", "rules": [], "children": null, "layout": null, "options": {"width": "100%", "span": "", "defaultValue": "", "placeholder": "请输入组件编码", "maxlength": null, "prefix": "", "suffix": "", "addonBefore": "", "addonAfter": "", "disabled": true, "allowClear": false, "showLabel": true, "required": false, "rules": [], "events": {}, "isSave": false, "isShow": true, "scan": false}, "bindStartTime": null, "bindEndTime": null, "value": null, "code": null, "isSubFormChild": false, "height": null, "position": null, "width": null, "colspan": null, "rowspan": null, "class": null}], "config": {"formType": "modal", "size": "default", "hideRequiredMark": null, "layout": "horizontal", "labelAlign": "right", "labelCol": {"span": 3, "offset": 0}, "formWidth": 900}, "hiddenComponent": []}, "isDataAuth": true, "dataAuthList": []}